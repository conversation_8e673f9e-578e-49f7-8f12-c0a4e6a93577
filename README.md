# diting

[![Pipeline](https://github.com/firstedu-engineering/diting/actions/workflows/release.yaml/badge.svg)](https://github.com/firstedu-engineering/diting/actions/workflows/release.yaml)
[![Service Role](https://github.com/firstedu-engineering/diting/actions/workflows/service-role.yaml/badge.svg)](https://github.com/firstedu-engineering/diting/actions/workflows/service-role.yaml)
[![Deployment](https://argocd.test.marsladder.com.au/api/badge?name=diting)](https://argocd.test.marsladder.com.au/applications/diting)

Realus Backend Service

## Swagger API Docs
Prod env is disabled
```
https://diting.test.marsladder.com.au/swagger-ui/index.html#/
```
