name: PR Check

on:
  pull_request:
    branches:
      - main

jobs:
  code_style:
    runs-on: ubuntu-latest
    name: Code Style

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Check code style
        run: ./gradlew checkstyleMain checkstyleTest

  unit_test:
    runs-on: ubuntu-latest
    name: Unit Test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Run unit test
        run: ./gradlew test

  integration_test:
    runs-on: ubuntu-latest
    name: Integration Test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Clone db migration repo
        run: ./auto/clone-db-migration-repo
        env:
          GIT_API_TOKEN: ${{ secrets.API_TOKEN_GITHUB }}

      - name: Run integration test
        run: ./gradlew integrationTest
