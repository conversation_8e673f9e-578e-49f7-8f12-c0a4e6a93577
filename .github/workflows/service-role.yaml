name: Provision Service Role

on:
  push:
    branches:
      - main
    paths:
    - 'cloudformation/service-role.yaml'
    - '.github/workflows/service-role.yaml'

env:
  AWS_DEFAULT_REGION: ap-southeast-2
  AWS_REGION: ap-southeast-2

jobs:
  provision-in-test:
    runs-on: ubuntu-latest
    name: Provision Service Role in Test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Assume Deployment Role
        uses: youyo/awscredswrap@v1
        with:
          role_arn: arn:aws:iam::862276445914:role/diting-deployment-role
          duration_seconds: 3600
          role_session_name: 'Diting@GitHubActions'
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Deploy Service Role
        uses: aws-actions/aws-cloudformation-github-deploy@v1.0.4
        with:
          name: diting-service-role
          no-fail-on-empty-changeset: "1"
          template: ./cloudformation/service-role.yaml
          capabilities: CAPABILITY_NAMED_IAM
          parameter-overrides: >-
            OidpId=317F86F6C192B3C73F2350B36449975C

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()

  provision-in-prod:
    runs-on: ubuntu-latest
    name: Provision Service Role in Prod

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Assume Deployment Role
        uses: youyo/awscredswrap@v1
        with:
          role_arn: arn:aws:iam::975616425746:role/diting-deployment-role
          duration_seconds: 3600
          role_session_name: 'Diting@GitHubActions'
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Deploy Service Role
        uses: aws-actions/aws-cloudformation-github-deploy@v1.0.4
        with:
          name: diting-service-role
          no-fail-on-empty-changeset: "1"
          template: ./cloudformation/service-role.yaml
          capabilities: CAPABILITY_NAMED_IAM
          parameter-overrides: >-
            OidpId=D2B66BAD4062581FB0F8B70CA6C84DF8

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()
