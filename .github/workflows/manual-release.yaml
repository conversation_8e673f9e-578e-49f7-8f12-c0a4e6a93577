name: Manual Release

on:
  workflow_dispatch:
    inputs:
      releaseTag:
        description: 'Input the image tag'
        required: false
        type: string
      releaseTo:
        description: 'Release environment'
        required: true
        type: choice
        default: prod
        options:
          - "test"
          - "prod"

env:
  AWS_DEFAULT_REGION: ap-southeast-2
  AWS_REGION: ap-southeast-2
  ROLE_ARN: "arn:aws:iam::862276445914:role/docker-image-release-role"
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  ENV: ${{ github.event.inputs.releaseTo }}
  RELEASE_TAG: ${{ github.event.inputs.releaseTag }}

jobs:
  release:
    runs-on: ubuntu-latest
    name: Manual Release
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Validate image
        run: |
          ./auto/validate-image

      - name: Generate helm file and update manifest repo
        run: |
          ./auto/generate-helm-config-files
          ./auto/push-files-to-remote-repo
        env:
          GIT_API_TOKEN: ${{ secrets.API_TOKEN_GITHUB }}
          SOURCE_FILE: "template/values-*.yaml"
          DESTINATION_REPO: "firstedu-engineering/Deployment-Manifests"
          USER_EMAIL: ${{ github.actor }}@users.noreply.github.com
          USER_NAME: ${{ github.actor }}
          DESTINATION_BRANCH: main

      - name: Tag the deployed commit
        run: |
          ./auto/tag-commit
        env:
          USER_EMAIL: ${{ github.actor }}@users.noreply.github.com
          USER_NAME: ${{ github.actor }}

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()

  newrelic:
    runs-on: ubuntu-latest
    name: Deployment Marker
    needs: [release]
    steps:
      - name: Create NewRelic deployment marker in Test
        if: ${{ github.event.inputs.releaseTo == 'test' }}
        uses: newrelic/deployment-marker-action@v1
        with:
          accountId: 3230083
          apiKey: ${{ secrets.TEST_NEW_RELIC_API_KEY }}
          applicationId: *********
          revision: "${{ github.ref }}-${{ github.sha }}"
          user: "${{ github.actor }}"

      - name: Create NewRelic deployment marker in Prod
        if: ${{ github.event.inputs.releaseTo == 'prod' }}
        uses: newrelic/deployment-marker-action@v1
        with:
          accountId: 3308161
          apiKey: ${{ secrets.PROD_NEW_RELIC_API_KEY }}
          applicationId: **********
          revision: "${{ github.ref }}-${{ github.sha }}"
          user: "${{ github.actor }}"

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()
