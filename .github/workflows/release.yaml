name: Release

on:
  push:
    branches:
      - main
    paths-ignore:
      - '**.md'
      - 'cloudformation/service-role.yaml'
      - '.github/workflows/service-role.yaml'
      - '.github/workflows/pr-check.yaml'
      - '.github/workflows/manual-release.yaml'

env:
  AWS_DEFAULT_REGION: ap-southeast-2
  AWS_REGION: ap-southeast-2
  ROLE_ARN: "arn:aws:iam::862276445914:role/docker-image-release-role"
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  ENV: test

jobs:
  validation:
    name: Validation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # fetch all history so multiple commits can be scanned

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: GitGuardian scan
        uses: GitGuardian/gg-shield-action@master
        env:
          GITHUB_PUSH_BEFORE_SHA: ${{ github.event.before }}
          GITHUB_PUSH_BASE_SHA: ${{ github.event.base }}
          GITHUB_PULL_BASE_SHA:  ${{ github.event.pull_request.base.sha }}
          GITHUB_DEFAULT_BRANCH: ${{ github.event.repository.default_branch }}
          GITGUARDIAN_API_KEY: ${{ secrets.GITGUARDIAN_API_KEY }}

      - name: Check code style
        run: ./gradlew checkstyleMain checkstyleTest --parallel --build-cache

      - name: Check and create/update ECR repo
        run: |
          ./auto/check-ecr-repo
          ./auto/update-ecr-policy

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()

  build:
    runs-on: ubuntu-latest
    name: Test & Build
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '21'

      - name: Run unit test
        run: ./gradlew test --parallel --build-cache

      - name: Clone db migration repo
        run: ./auto/clone-db-migration-repo
        env:
          GIT_API_TOKEN: ${{ secrets.API_TOKEN_GITHUB }}

      - name: Run integration test
        run: ./gradlew integrationTest --parallel --build-cache

      - name: Build and push image to Amazon ECR
        run: |
          ./auto/release-docker-image-to-ecr

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()

  release:
    runs-on: ubuntu-latest
    name: Release
    needs: [validation, build]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Generate helm file and update manifest repo
        run: |
          ./auto/generate-helm-config-files
          ./auto/push-files-to-remote-repo
        env:
          GIT_API_TOKEN: ${{ secrets.API_TOKEN_GITHUB }}
          SOURCE_FILE: "template/values-*.yaml"
          DESTINATION_REPO: "firstedu-engineering/Deployment-Manifests"
          USER_EMAIL: ${{ github.actor }}@users.noreply.github.com
          USER_NAME: ${{ github.actor }}
          DESTINATION_BRANCH: main

      - name: Tag the deployed commit
        run: |
          ./auto/tag-commit
        env:
          USER_EMAIL: ${{ github.actor }}@users.noreply.github.com
          USER_NAME: ${{ github.actor }}

      - name: Create NewRelic deployment marker in Test
        uses: newrelic/deployment-marker-action@v1
        with:
          accountId: 3230083
          apiKey: ${{ secrets.TEST_NEW_RELIC_API_KEY }}
          applicationId: *********
          revision: "${{ github.ref }}-${{ github.sha }}"
          user: "${{ github.actor }}"

      - uses: ghostoy/dingtalk-action@master
        with:
          webhook: ${{ secrets.DINGTALK_WEBHOOK_URL}}
          msgtype: markdown
          content: |
            {
              "title": "GHA CI",
              "text": "### Github Actions CI status\n> - Repository: ${{ github.repository }}\n> - Commit: ${{ github.sha }}\n> - Status: ${{ job.status == 'failure' && '<span style=\"color:red\">' || ''}}${{ job.status }}${{ job.status == 'failure' && '</span>' || ''}}\n> - [View Run Details](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
            }
        if: failure()
