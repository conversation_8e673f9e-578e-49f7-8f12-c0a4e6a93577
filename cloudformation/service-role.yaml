AWSTemplateFormatVersion: "2010-09-09"
Description: Diting service role

Parameters:
  OidpId:
    Type: String
    Description: Account Id

Resources:
  ServiceRole:
    Type: "AWS::IAM::Role"
    Properties:
      Description: This role is used for diting service
      RoleName: diting-service-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Federated:
                - !Sub 'arn:aws:iam::${AWS::AccountId}:oidc-provider/oidc.eks.ap-southeast-2.amazonaws.com/id/${OidpId}'
            Action:
              - "sts:AssumeRoleWithWebIdentity"
      Path: "/"
      Policies:
        - PolicyName: "Cognito"
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: "Allow"
                Action:
                  - "cognito-idp:ListUsersInGroup"
                  - "cognito-idp:ListGroups"
                  - "cognito-idp:ListUsers"
                Resource: "*"
        - PolicyName: 'KMS'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'kms:Decrypt'
                Resource: '*'
