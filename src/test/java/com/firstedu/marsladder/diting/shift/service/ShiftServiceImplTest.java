package com.firstedu.marsladder.diting.shift.service;

import com.firstedu.marsladder.diting.shift.service.domain.Shift;
import com.firstedu.marsladder.diting.shift.service.exception.XiaoguanjiaShiftNotFoundException;
import com.firstedu.marsladder.diting.shift.service.impl.ShiftServiceImpl;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaShiftRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaShiftEntity;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ShiftServiceImplTest {
    @Mock
    XiaoguanjiaShiftRepository xiaoguanjiaShiftRepository;

    @InjectMocks
    ShiftServiceImpl shiftService;

    @Nested
    class GetMyShiftsByShiftId {
        @Test
        void should_get_shift_by_shift_id() {
            String shiftId = "shiftId";
            String shiftName = "shiftName";
            when(xiaoguanjiaShiftRepository.findById(shiftId)).thenReturn(Optional.of(XiaoguanjiaShiftEntity.builder().id(shiftId).name(shiftName).build()));

            Shift shiftByShiftId = shiftService.findShiftByShiftId(shiftId);

            assertThat(shiftByShiftId.name()).isEqualTo(shiftName);
            assertThat(shiftByShiftId.id()).isEqualTo(shiftId);
        }

        @Test
        void should_throw_when_shift_not_found_with_given_id() {
            when(xiaoguanjiaShiftRepository.findById("shiftId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(XiaoguanjiaShiftNotFoundException.class)
                    .isThrownBy(() -> shiftService.findShiftByShiftId("shiftId"));
        }
    }
}