package com.firstedu.marsladder.diting.employee.service;

import com.firstedu.marsladder.diting.employee.service.domain.Employee;
import com.firstedu.marsladder.diting.employee.service.impl.EmployeeServiceImpl;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEmployeeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaEmployeeEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EmployeeServiceImplTest {
    @Mock
    XiaoguanjiaEmployeeRepository xiaoguanjiaEmployeeRepository;

    @InjectMocks
    EmployeeServiceImpl employeeService;

    @Test
    void should_get_employee_by_id() {
        when(xiaoguanjiaEmployeeRepository.findById("employeeId")).thenReturn(
                Optional.ofNullable(XiaoguanjiaEmployeeEntity.builder().id("employeeId").name("employeeName").build())
        );

        Employee employee = employeeService.getEmployeeById("employeeId");

        assertThat(employee.id()).isEqualTo("employeeId");
        assertThat(employee.name()).isEqualTo("employeeName");
    }

    @Test
    void should_return_empty_employee_when_employee_found() {
        when(xiaoguanjiaEmployeeRepository.findById("employeeId")).thenReturn(Optional.empty());

        assertThat(employeeService.getEmployeeById("employeeId").id()).isEqualTo("employeeId");
        assertThat(employeeService.getEmployeeById("employeeId").name()).isEqualTo("");

    }

}