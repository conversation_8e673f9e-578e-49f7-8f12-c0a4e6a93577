package com.firstedu.marsladder.diting.course.service.domain;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AbsentCauseTest {
    @Test
    void testPersonalAffair() {
        String value = "6281f3b3-03e2-461e-9cf8-a5eedfd62f8e";
        AbsentCause absentCause = AbsentCause.of(value);
        assertEquals(AbsentCause.PERSONAL_AFFAIR, absentCause);
    }

    @Test
    void testSick() {
        String value = "e2696981-98a7-42f5-8dbf-be44389e01db";
        AbsentCause absentCause = AbsentCause.of(value);
        assertEquals(AbsentCause.SICK, absentCause);
    }

    @Test
    void testTruant() {
        String value = "5793244c-09da-4c09-a4f3-cf0ad78cfd11";
        AbsentCause absentCause = AbsentCause.of(value);
        assertEquals(AbsentCause.TRUANT, absentCause);
    }

    @Test
    void testPublicTransportBreakdown() {
        String value = "70ab9ef3-8a7b-49f2-812a-c1377363a29f";
        AbsentCause absentCause = AbsentCause.of(value);
        assertEquals(AbsentCause.PUBLIC_TRANSPORT_BREAKDOWN, absentCause);
    }

    @Test
    void testUnknownValue() {
        String value = "unknown-uuid";
        AbsentCause absentCause = AbsentCause.of(value);
        assertNull(absentCause);
    }
}