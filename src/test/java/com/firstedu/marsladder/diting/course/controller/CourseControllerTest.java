package com.firstedu.marsladder.diting.course.controller;

import com.firstedu.marsladder.diting.classroom.service.ClassroomService;
import com.firstedu.marsladder.diting.classroom.service.domain.Classroom;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusParentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.course.service.CourseService;
import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByDay;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByMonthFilter;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.*;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = CourseController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser(roles = {"REALUS_STUDENT", "REALUS_PARENT"})
class CourseControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    CourseService courseService;

    @MockBean
    ClassroomService classroomService;

    @MockBean
    FalconClient falconClient;

    @Nested
    class GetCourseSchedules {
        @Test
        void should_return_course_schedules_successfully_for_student() throws Exception {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_STUDENT))
                            .realusStudentProfile(RealusStudentProfile.builder().xiaogjStudentId("xiaogjStudentId").build()).build());
            when(courseService.getCourseSchedulesByPage(eq(CourseScheduleFilter.builder().startTimeAfter(1L).startTimeBefore(2L).classId("classId").xiaogjStudentIds(List.of("xiaogjStudentId")).build()), any()))
                    .thenReturn(List.of(
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId")
                                    .studentName("Scott")
                                    .classId("classId1")
                                    .className("className1")
                                    .classUnitId("classUnitId1")
                                    .studentName("Scott")
                                    .startTime(LocalDateTime.of(2024, 12, 1, 10, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 1, 12, 0))
                                    .course("Math")
                                    .campus("Main Campus")
                                    .classroom("Room 101")
                                    .teacher("Mr. Smith")
                                    .build(),
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId")
                                    .studentName("Scott")
                                    .classId("classId2")
                                    .className("className2")
                                    .classUnitId("classUnitId2")
                                    .startTime(LocalDateTime.of(2024, 12, 2, 15, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 2, 17, 0))
                                    .course("Science")
                                    .campus("North Campus")
                                    .classroom(null)
                                    .teacher("Ms. Johnson")
                                    .build()
                    ));

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules")
                            .param("startTimeBefore", "2")
                            .param("startTimeAfter", "1")
                            .param("classId", "classId")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(2))
                    .andExpect(jsonPath("$[0].xiaogjStudentId").value("studentId"))
                    .andExpect(jsonPath("$[0].studentName").value("Scott"))
                    .andExpect(jsonPath("$[0].classId").value("classId1"))
                    .andExpect(jsonPath("$[0].className").value("className1"))
                    .andExpect(jsonPath("$[0].classUnitId").value("classUnitId1"))
                    .andExpect(jsonPath("$[0].course").value("Math"))
                    .andExpect(jsonPath("$[0].startTime").value(LocalDateTime.of(2024, 12, 1, 10, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].endTime").value(LocalDateTime.of(2024, 12, 1, 12, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].campus").value("Main Campus"))
                    .andExpect(jsonPath("$[0].classroom").value("Room 101"))
                    .andExpect(jsonPath("$[0].teacher").value("Mr. Smith"))
                    .andExpect(jsonPath("$[1].xiaogjStudentId").value("studentId"))
                    .andExpect(jsonPath("$[1].studentName").value("Scott"))
                    .andExpect(jsonPath("$[1].classId").value("classId2"))
                    .andExpect(jsonPath("$[1].className").value("className2"))
                    .andExpect(jsonPath("$[1].classUnitId").value("classUnitId2"))
                    .andExpect(jsonPath("$[1].course").value("Science"))
                    .andExpect(jsonPath("$[1].startTime").value(LocalDateTime.of(2024, 12, 2, 15, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].endTime").value(LocalDateTime.of(2024, 12, 2, 17, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].campus").value("North Campus"))
                    .andExpect(jsonPath("$[1].classroom").doesNotExist())
                    .andExpect(jsonPath("$[1].teacher").value("Ms. Johnson"));
        }

        @Test
        void should_return_course_schedules_successfully_for_parent() throws Exception {
            var students1 = RealusStudentProfile.builder().userId("userId1").xiaogjStudentId("xiaogjStudentId1").build();
            var students2 = RealusStudentProfile.builder().userId("userId2").xiaogjStudentId("xiaogjStudentId2").build();
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_PARENT))
                            .realusParentProfile(RealusParentProfile.builder().students(List.of(students1, students2)).build()).build());
            when(courseService.getCourseSchedulesByPage(eq(CourseScheduleFilter.builder().startTimeAfter(1L).startTimeBefore(2L).classId("classId").xiaogjStudentIds(List.of("xiaogjStudentId1")).build()), any()))
                    .thenReturn(List.of(
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId1")
                                    .studentName("Scott")
                                    .classId("classId1")
                                    .className("className1")
                                    .classUnitId("classUnitId1")
                                    .startTime(LocalDateTime.of(2024, 12, 1, 10, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 1, 12, 0))
                                    .course("Math")
                                    .campus("Main Campus")
                                    .classroom("Room 101")
                                    .teacher("Mr. Smith")
                                    .build(),
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId2")
                                    .studentName("Lucy")
                                    .classId("classId2")
                                    .className("className2")
                                    .classUnitId("classUnitId2")
                                    .startTime(LocalDateTime.of(2024, 12, 2, 15, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 2, 17, 0))
                                    .course("Science")
                                    .campus("North Campus")
                                    .classroom(null)
                                    .teacher("Ms. Johnson")
                                    .build()
                    ));

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules")
                            .param("startTimeBefore", "2")
                            .param("startTimeAfter", "1")
                            .param("classId", "classId")
                            .param("xiaogjStudentIds", "xiaogjStudentId1")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(2))
                    .andExpect(jsonPath("$[0].xiaogjStudentId").value("studentId1"))
                    .andExpect(jsonPath("$[0].studentName").value("Scott"))
                    .andExpect(jsonPath("$[0].classId").value("classId1"))
                    .andExpect(jsonPath("$[0].className").value("className1"))
                    .andExpect(jsonPath("$[0].classUnitId").value("classUnitId1"))
                    .andExpect(jsonPath("$[0].course").value("Math"))
                    .andExpect(jsonPath("$[0].startTime").value(LocalDateTime.of(2024, 12, 1, 10, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].endTime").value(LocalDateTime.of(2024, 12, 1, 12, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].campus").value("Main Campus"))
                    .andExpect(jsonPath("$[0].classroom").value("Room 101"))
                    .andExpect(jsonPath("$[0].teacher").value("Mr. Smith"))
                    .andExpect(jsonPath("$[1].xiaogjStudentId").value("studentId2"))
                    .andExpect(jsonPath("$[1].studentName").value("Lucy"))
                    .andExpect(jsonPath("$[1].classId").value("classId2"))
                    .andExpect(jsonPath("$[1].className").value("className2"))
                    .andExpect(jsonPath("$[1].classUnitId").value("classUnitId2"))
                    .andExpect(jsonPath("$[1].course").value("Science"))
                    .andExpect(jsonPath("$[1].startTime").value(LocalDateTime.of(2024, 12, 2, 15, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].endTime").value(LocalDateTime.of(2024, 12, 2, 17, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].campus").value("North Campus"))
                    .andExpect(jsonPath("$[1].classroom").doesNotExist())
                    .andExpect(jsonPath("$[1].teacher").value("Ms. Johnson"));
        }

        @Test
        void should_return_course_schedules_by_month_successfully_for_student() throws Exception {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_STUDENT))
                            .realusStudentProfile(RealusStudentProfile.builder().xiaogjStudentId("xiaogjStudentId").build()).build());
            YearMonth yearMonth = YearMonth.of(2024, 12);

            List<CourseScheduleByDay> courseSchedulesByMonth = List.of(
                    CourseScheduleByDay.builder()
                            .date(LocalDate.of(2024, 12, 1))
                            .courseSchedules(List.of(
                                    CourseSchedule.builder()
                                            .xiaogjStudentId("studentId1")
                                            .studentName("Scott")
                                            .classId("classId1")
                                            .className("className1")
                                            .classUnitId("classUnitId1")
                                            .startTime(LocalDateTime.of(2024, 12, 1, 10, 0))
                                            .endTime(LocalDateTime.of(2024, 12, 1, 12, 0))
                                            .course("Math")
                                            .campus("Main Campus")
                                            .classroom("Room 101")
                                            .teacher("Mr. Smith")
                                            .build()
                            ))
                            .build(),
                    CourseScheduleByDay.builder()
                            .date(LocalDate.of(2024, 12, 2))
                            .courseSchedules(List.of(
                                    CourseSchedule.builder()
                                            .xiaogjStudentId("studentId2")
                                            .studentName("Lucy")
                                            .classId("classId2")
                                            .className("className2")
                                            .classUnitId("classUnitId2")
                                            .startTime(LocalDateTime.of(2024, 12, 2, 15, 0))
                                            .endTime(LocalDateTime.of(2024, 12, 2, 17, 0))
                                            .course("Science")
                                            .campus("North Campus")
                                            .classroom(null)
                                            .teacher("Ms. Johnson")
                                            .build()
                            ))
                            .build()
            );

            when(courseService.getCourseSchedulesByMonth(eq(CourseScheduleByMonthFilter.builder().classId("classId").xiaogjStudentIds(List.of("xiaogjStudentId")).build()), eq(yearMonth), eq(ZoneId.of("Australia/Melbourne"))))
                    .thenReturn(courseSchedulesByMonth);

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules-by-month")
                            .param("yearMonth", yearMonth.toString())
                            .param("classId", "classId")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(2))
                    .andExpect(jsonPath("$[0].date").value("2024-12-01"))
                    .andExpect(jsonPath("$[0].courseSchedules.length()").value(1))
                    .andExpect(jsonPath("$[0].courseSchedules[0].course").value("Math"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].xiaogjStudentId").value("studentId1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].studentName").value("Scott"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].classId").value("classId1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].className").value("className1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].classUnitId").value("classUnitId1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].startTime").value(LocalDateTime.of(2024, 12, 1, 10, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].courseSchedules[0].endTime").value(LocalDateTime.of(2024, 12, 1, 12, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].courseSchedules[0].campus").value("Main Campus"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].classroom").value("Room 101"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].teacher").value("Mr. Smith"))
                    .andExpect(jsonPath("$[1].date").value("2024-12-02"))
                    .andExpect(jsonPath("$[1].courseSchedules.length()").value(1))
                    .andExpect(jsonPath("$[1].courseSchedules[0].xiaogjStudentId").value("studentId2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].studentName").value("Lucy"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].classId").value("classId2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].className").value("className2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].classUnitId").value("classUnitId2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].course").value("Science"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].startTime").value(LocalDateTime.of(2024, 12, 2, 15, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].courseSchedules[0].endTime").value(LocalDateTime.of(2024, 12, 2, 17, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].courseSchedules[0].campus").value("North Campus"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].classroom").doesNotExist())
                    .andExpect(jsonPath("$[1].courseSchedules[0].teacher").value("Ms. Johnson"));
        }

        @Test
        void should_return_course_schedules_by_month_successfully_for_parent() throws Exception {
            var students1 = RealusStudentProfile.builder().userId("userId1").xiaogjStudentId("xiaogjStudentId1").build();
            var students2 = RealusStudentProfile.builder().userId("userId2").xiaogjStudentId("xiaogjStudentId2").build();
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_PARENT))
                            .realusParentProfile(RealusParentProfile.builder().students(List.of(students1, students2)).build()).build());
            YearMonth yearMonth = YearMonth.of(2024, 12);

            List<CourseScheduleByDay> courseSchedulesByMonth = List.of(
                    CourseScheduleByDay.builder()
                            .date(LocalDate.of(2024, 12, 1))
                            .courseSchedules(List.of(
                                    CourseSchedule.builder()
                                            .xiaogjStudentId("studentId1")
                                            .studentName("Scott")
                                            .classId("classId1")
                                            .className("className1")
                                            .classUnitId("classUnitId1")
                                            .startTime(LocalDateTime.of(2024, 12, 1, 10, 0))
                                            .endTime(LocalDateTime.of(2024, 12, 1, 12, 0))
                                            .course("Math")
                                            .campus("Main Campus")
                                            .classroom("Room 101")
                                            .teacher("Mr. Smith")
                                            .build()
                            ))
                            .build(),
                    CourseScheduleByDay.builder()
                            .date(LocalDate.of(2024, 12, 2))
                            .courseSchedules(List.of(
                                    CourseSchedule.builder()
                                            .xiaogjStudentId("studentId2")
                                            .studentName("Lucy")
                                            .classId("classId2")
                                            .className("className2")
                                            .classUnitId("classUnitId2")
                                            .startTime(LocalDateTime.of(2024, 12, 2, 15, 0))
                                            .endTime(LocalDateTime.of(2024, 12, 2, 17, 0))
                                            .course("Science")
                                            .campus("North Campus")
                                            .classroom(null)
                                            .teacher("Ms. Johnson")
                                            .build()
                            ))
                            .build()
            );

            when(courseService.getCourseSchedulesByMonth(eq(CourseScheduleByMonthFilter.builder().classId("classId").xiaogjStudentIds(List.of("xiaogjStudentId1")).build()), eq(yearMonth), eq(ZoneId.of("Australia/Melbourne"))))
                    .thenReturn(courseSchedulesByMonth);

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules-by-month")
                            .param("yearMonth", yearMonth.toString())
                            .param("xiaogjStudentIds", "xiaogjStudentId1")
                            .param("classId", "classId")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(2))
                    .andExpect(jsonPath("$[0].date").value("2024-12-01"))
                    .andExpect(jsonPath("$[0].courseSchedules.length()").value(1))
                    .andExpect(jsonPath("$[0].courseSchedules[0].course").value("Math"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].xiaogjStudentId").value("studentId1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].studentName").value("Scott"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].classId").value("classId1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].className").value("className1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].classUnitId").value("classUnitId1"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].startTime").value(LocalDateTime.of(2024, 12, 1, 10, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].courseSchedules[0].endTime").value(LocalDateTime.of(2024, 12, 1, 12, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].courseSchedules[0].campus").value("Main Campus"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].classroom").value("Room 101"))
                    .andExpect(jsonPath("$[0].courseSchedules[0].teacher").value("Mr. Smith"))
                    .andExpect(jsonPath("$[1].date").value("2024-12-02"))
                    .andExpect(jsonPath("$[1].courseSchedules.length()").value(1))
                    .andExpect(jsonPath("$[1].courseSchedules[0].xiaogjStudentId").value("studentId2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].studentName").value("Lucy"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].classId").value("classId2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].className").value("className2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].classUnitId").value("classUnitId2"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].course").value("Science"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].startTime").value(LocalDateTime.of(2024, 12, 2, 15, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].courseSchedules[0].endTime").value(LocalDateTime.of(2024, 12, 2, 17, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].courseSchedules[0].campus").value("North Campus"))
                    .andExpect(jsonPath("$[1].courseSchedules[0].classroom").doesNotExist())
                    .andExpect(jsonPath("$[1].courseSchedules[0].teacher").value("Ms. Johnson"));
        }

        @Test
        void should_return_empty_list_when_user_has_no_relevant_roles() throws Exception {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.TEACHER))
                            .build());
            
            when(courseService.getCourseSchedulesByPage(eq(CourseScheduleFilter.builder().startTimeAfter(1L).startTimeBefore(2L).classId("classId").xiaogjStudentIds(List.of()).build()), any()))
                    .thenReturn(List.of());

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules")
                            .param("startTimeBefore", "2")
                            .param("startTimeAfter", "1")
                            .param("classId", "classId")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(0));
        }
        
        @Test
        void should_return_course_schedules_for_parent_without_xiaogj_student_ids() throws Exception {
            var students1 = RealusStudentProfile.builder().userId("userId1").xiaogjStudentId("xiaogjStudentId1").build();
            var students2 = RealusStudentProfile.builder().userId("userId2").xiaogjStudentId("xiaogjStudentId2").build();
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_PARENT))
                            .realusParentProfile(RealusParentProfile.builder().students(List.of(students1, students2)).build()).build());
            
            when(courseService.getCourseSchedulesByPage(eq(CourseScheduleFilter.builder().startTimeAfter(1L).startTimeBefore(2L).classId("classId").xiaogjStudentIds(List.of("xiaogjStudentId1", "xiaogjStudentId2")).build()), any()))
                    .thenReturn(List.of(
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId1")
                                    .studentName("Scott")
                                    .classId("classId1")
                                    .className("className1")
                                    .classUnitId("classUnitId1")
                                    .startTime(LocalDateTime.of(2024, 12, 1, 10, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 1, 12, 0))
                                    .course("Math")
                                    .campus("Main Campus")
                                    .classroom("Room 101")
                                    .teacher("Mr. Smith")
                                    .build()
                    ));

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules")
                            .param("startTimeBefore", "2")
                            .param("startTimeAfter", "1")
                            .param("classId", "classId")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(1))
                    .andExpect(jsonPath("$[0].xiaogjStudentId").value("studentId1"))
                    .andExpect(jsonPath("$[0].studentName").value("Scott"))
                    .andExpect(jsonPath("$[0].classId").value("classId1"))
                    .andExpect(jsonPath("$[0].className").value("className1"))
                    .andExpect(jsonPath("$[0].classUnitId").value("classUnitId1"))
                    .andExpect(jsonPath("$[0].course").value("Math"))
                    .andExpect(jsonPath("$[0].startTime").value(LocalDateTime.of(2024, 12, 1, 10, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].endTime").value(LocalDateTime.of(2024, 12, 1, 12, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].campus").value("Main Campus"))
                    .andExpect(jsonPath("$[0].classroom").value("Room 101"))
                    .andExpect(jsonPath("$[0].teacher").value("Mr. Smith"));
        }

        @Test
        void should_return_course_schedules_for_parent_with_xiaogj_student_ids() throws Exception {
            var students1 = RealusStudentProfile.builder().userId("userId1").xiaogjStudentId("xiaogjStudentId1").build();
            var students2 = RealusStudentProfile.builder().userId("userId2").xiaogjStudentId("xiaogjStudentId2").build();
            var students3 = RealusStudentProfile.builder().userId("userId3").xiaogjStudentId("xiaogjStudentId3").build();
            
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_PARENT))
                            .realusParentProfile(RealusParentProfile.builder().students(List.of(students1, students2, students3)).build()).build());
            
            when(courseService.getCourseSchedulesByPage(eq(CourseScheduleFilter.builder().startTimeAfter(1L).startTimeBefore(2L).classId("classId").xiaogjStudentIds(List.of("xiaogjStudentId1", "xiaogjStudentId3")).build()), any()))
                    .thenReturn(List.of(
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId1")
                                    .studentName("Scott")
                                    .classId("classId1")
                                    .className("className1")
                                    .classUnitId("classUnitId1")
                                    .startTime(LocalDateTime.of(2024, 12, 1, 10, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 1, 12, 0))
                                    .course("Math")
                                    .campus("Main Campus")
                                    .classroom("Room 101")
                                    .teacher("Mr. Smith")
                                    .build(),
                            CourseSchedule.builder()
                                    .xiaogjStudentId("studentId3")
                                    .studentName("Lucy")
                                    .classId("classId3")
                                    .className("className3")
                                    .classUnitId("classUnitId3")
                                    .startTime(LocalDateTime.of(2024, 12, 3, 15, 0))
                                    .endTime(LocalDateTime.of(2024, 12, 3, 17, 0))
                                    .course("Science")
                                    .campus("North Campus")
                                    .classroom(null)
                                    .teacher("Ms. Johnson")
                                    .build()
                    ));

            mockMvc.perform(MockMvcRequestBuilders.get("/course-schedules")
                            .param("startTimeBefore", "2")
                            .param("startTimeAfter", "1")
                            .param("classId", "classId")
                            .param("xiaogjStudentIds", "xiaogjStudentId1", "xiaogjStudentId3")
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.length()").value(2))
                    .andExpect(jsonPath("$[0].xiaogjStudentId").value("studentId1"))
                    .andExpect(jsonPath("$[0].studentName").value("Scott"))
                    .andExpect(jsonPath("$[0].classId").value("classId1"))
                    .andExpect(jsonPath("$[0].className").value("className1"))
                    .andExpect(jsonPath("$[0].classUnitId").value("classUnitId1"))
                    .andExpect(jsonPath("$[0].course").value("Math"))
                    .andExpect(jsonPath("$[0].startTime").value(LocalDateTime.of(2024, 12, 1, 10, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].endTime").value(LocalDateTime.of(2024, 12, 1, 12, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[0].campus").value("Main Campus"))
                    .andExpect(jsonPath("$[0].classroom").value("Room 101"))
                    .andExpect(jsonPath("$[0].teacher").value("Mr. Smith"))
                    .andExpect(jsonPath("$[1].xiaogjStudentId").value("studentId3"))
                    .andExpect(jsonPath("$[1].studentName").value("Lucy"))
                    .andExpect(jsonPath("$[1].classId").value("classId3"))
                    .andExpect(jsonPath("$[1].className").value("className3"))
                    .andExpect(jsonPath("$[1].classUnitId").value("classUnitId3"))
                    .andExpect(jsonPath("$[1].course").value("Science"))
                    .andExpect(jsonPath("$[1].startTime").value(LocalDateTime.of(2024, 12, 3, 15, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].endTime").value(LocalDateTime.of(2024, 12, 3, 17, 0).toEpochSecond(ZoneOffset.UTC)))
                    .andExpect(jsonPath("$[1].campus").value("North Campus"))
                    .andExpect(jsonPath("$[1].classroom").doesNotExist())
                    .andExpect(jsonPath("$[1].teacher").value("Ms. Johnson"));
        }
    }

    @Nested
    class GetClassroomsByCourseId {
        @Test
        void should_return_200_when_get_classrooms() throws Exception {
            when(classroomService.getClassroomsByShiftId("courseId"))
                    .thenReturn(List.of(new Classroom("id")));

            get("/courses/courseId/marsladder-classrooms")
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$[0].classroomId").value("id"));
        }
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}