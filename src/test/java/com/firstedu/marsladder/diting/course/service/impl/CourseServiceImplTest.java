package com.firstedu.marsladder.diting.course.service.impl;

import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByDay;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByMonthFilter;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CourseServiceImplTest {
    @Mock
    XiaoguanjiaCourseStudentDetailRepository xiaoguanjiaCourseStudentDetailRepository;

    @InjectMocks
    CourseServiceImpl courseService;

    @Test
    void should_return_courses_for_student_given_pageable() {
        var now = LocalDateTime.now();
        XiaoguanjiaCourseStudentDetailId xiaoguanjiaCourseStudentDetailId1 = new XiaoguanjiaCourseStudentDetailId("studentId", "courseId1");
        XiaoguanjiaCourseStudentDetailEntity entity1 = XiaoguanjiaCourseStudentDetailEntity.builder()
                .id(xiaoguanjiaCourseStudentDetailId1)
                .studentName("John Doe")
                .studentIsAttend(1)
                .studentAbsentCauseId("00000000-0000-0000-0000-000000000000")
                .courseIsFinished(0)
                .courseDeleted(false)
                .courseStartTime(now.plusDays(1))
                .courseEndTime(now.plusDays(1).plusHours(2))
                .courseTeacherName("Mr. Smith")
                .courseTeacherNickName("Smithy")
                .courseTeacherId("teacher1")
                .shiftName("Morning Shift")
                .classroomId("classroom1")
                .classroomName("Room 101")
                .campusId("campus1")
                .campusName("Main Campus")
                .build();

        XiaoguanjiaCourseStudentDetailId xiaoguanjiaCourseStudentDetailId2 = new XiaoguanjiaCourseStudentDetailId("studentId", "courseId1");
        XiaoguanjiaCourseStudentDetailEntity entity2 = XiaoguanjiaCourseStudentDetailEntity.builder()
                .id(xiaoguanjiaCourseStudentDetailId2)
                .studentName("John Doe")
                .studentIsAttend(0)
                .studentAbsentCauseId("70ab9ef3-8a7b-49f2-812a-c1377363a29f")
                .courseIsFinished(0)
                .courseDeleted(false)
                .courseStartTime(now.plusDays(2))
                .courseEndTime(now.plusDays(2).plusHours(2))
                .courseTeacherName("Ms. Johnson")
                .courseTeacherNickName("John")
                .courseTeacherId("teacher2")
                .shiftName("Afternoon Shift")
                .classroomId(null) // No classroom
                .campusId("campus2")
                .campusName("North Campus")
                .build();

        var filter = CourseScheduleFilter.builder()
                .xiaogjStudentIds(List.of("id"))
                .classId("classId")
                .startTimeAfter(1L)
                .startTimeBefore(2L).build();
        var pageRequest = PageRequest.of(0, 2);
        when(xiaoguanjiaCourseStudentDetailRepository.findAll(
                any(Specification.class),
                any(PageRequest.class)
        )).thenReturn(new PageImpl<>(List.of(entity1, entity2), pageRequest, 2));

        List<CourseSchedule> courseSchedules = courseService.getCourseSchedulesByPage(filter, pageRequest);

        assertThat(courseSchedules).hasSize(2);

        CourseSchedule course1 = courseSchedules.getFirst();
        assertThat(course1).isEqualTo(CourseSchedule.from(entity1));
        CourseSchedule course2 = courseSchedules.get(1);
        assertThat(course2).isEqualTo(CourseSchedule.from(entity2));
    }

    @Test
    void should_return_all_courses_for_student() {
        var now = LocalDateTime.now();
        XiaoguanjiaCourseStudentDetailId xiaoguanjiaCourseStudentDetailId1 = new XiaoguanjiaCourseStudentDetailId("studentId", "courseId1");
        XiaoguanjiaCourseStudentDetailEntity entity1 = XiaoguanjiaCourseStudentDetailEntity.builder()
                .id(xiaoguanjiaCourseStudentDetailId1)
                .studentName("John Doe")
                .studentIsAttend(1)
                .studentAbsentCauseId("00000000-0000-0000-0000-000000000000")
                .courseIsFinished(0)
                .courseDeleted(false)
                .courseStartTime(now.plusDays(1))
                .courseEndTime(now.plusDays(1).plusHours(2))
                .courseTeacherName("Mr. Smith")
                .courseTeacherNickName("Smithy")
                .courseTeacherId("teacher1")
                .shiftName("Morning Shift")
                .classroomId("classroom1")
                .classroomName("Room 101")
                .campusId("campus1")
                .campusName("Main Campus")
                .build();

        XiaoguanjiaCourseStudentDetailId xiaoguanjiaCourseStudentDetailId2 = new XiaoguanjiaCourseStudentDetailId("studentId", "courseId1");
        XiaoguanjiaCourseStudentDetailEntity entity2 = XiaoguanjiaCourseStudentDetailEntity.builder()
                .id(xiaoguanjiaCourseStudentDetailId2)
                .studentName("John Doe")
                .studentIsAttend(0)
                .studentAbsentCauseId("70ab9ef3-8a7b-49f2-812a-c1377363a29f")
                .courseIsFinished(0)
                .courseDeleted(false)
                .courseStartTime(now.plusDays(2))
                .courseEndTime(now.plusDays(2).plusHours(2))
                .courseTeacherName("Ms. Johnson")
                .courseTeacherNickName("John")
                .courseTeacherId("teacher2")
                .shiftName("Afternoon Shift")
                .classroomId(null) // No classroom
                .campusId("campus2")
                .campusName("North Campus")
                .build();

        var filter = CourseScheduleFilter.builder()
                .xiaogjStudentIds(List.of("id"))
                .classId("classId")
                .startTimeAfter(1L)
                .startTimeBefore(2L).build();
        when(xiaoguanjiaCourseStudentDetailRepository.findAll(
                any(Specification.class)
        )).thenReturn(List.of(entity1, entity2));

        List<CourseSchedule> courseSchedules = courseService.getCourseSchedules(filter);

        assertThat(courseSchedules).hasSize(2);

        CourseSchedule course1 = courseSchedules.getFirst();
        assertThat(course1).isEqualTo(CourseSchedule.from(entity1));
        CourseSchedule course2 = courseSchedules.get(1);
        assertThat(course2).isEqualTo(CourseSchedule.from(entity2));
    }

    @Test
    void should_return_course_schedules_by_month() {
        YearMonth yearMonth = YearMonth.of(2024, 12);

        LocalDateTime startTime1 = LocalDateTime.of(2024, 12, 9, 14, 50);
        LocalDateTime startTime2 = LocalDateTime.of(2024, 12, 10, 14, 50);

        XiaoguanjiaCourseStudentDetailId xiaoguanjiaCourseStudentDetailId1 = new XiaoguanjiaCourseStudentDetailId("studentId1", "courseId1");
        XiaoguanjiaCourseStudentDetailEntity entity1 = XiaoguanjiaCourseStudentDetailEntity.builder()
                .id(xiaoguanjiaCourseStudentDetailId1)
                .studentName("John")
                .studentIsAttend(0)
                .studentAbsentCauseId("5793244c-09da-4c09-a4f3-cf0ad78cfd11")
                .courseIsFinished(0)
                .courseDeleted(false)
                .courseStartTime(startTime1)
                .courseEndTime(startTime1.plusHours(2))
                .courseTeacherName("Mr. Smith")
                .courseTeacherNickName("Smithy")
                .courseTeacherId("teacher1")
                .shiftName("Morning Shift")
                .classroomId("classroom1")
                .classroomName("Room 101")
                .campusId("campus1")
                .campusName("Main Campus")
                .studentCost(120)
                .shiftUnit(1)
                .build();

        XiaoguanjiaCourseStudentDetailId xiaoguanjiaCourseStudentDetailId2 = new XiaoguanjiaCourseStudentDetailId("studentId2", "courseId2");
        XiaoguanjiaCourseStudentDetailEntity entity2 = XiaoguanjiaCourseStudentDetailEntity.builder()
                .id(xiaoguanjiaCourseStudentDetailId2)
                .studentName("Scott")
                .studentIsAttend(0)
                .studentAbsentCauseId("e2696981-98a7-42f5-8dbf-be44389e01db")
                .courseIsFinished(0)
                .courseDeleted(false)
                .courseStartTime(startTime2)
                .courseEndTime(startTime2.plusHours(2))
                .courseTeacherName("Ms. Johnson")
                .courseTeacherNickName("John")
                .courseTeacherId("teacher2")
                .shiftName("Afternoon Shift")
                .classroomId(null)
                .campusId("campus2")
                .campusName("North Campus")
                .studentCost(1.0)
                .shiftUnit(2)
                .build();

        when(xiaoguanjiaCourseStudentDetailRepository.findAll(
                any(Specification.class)
        )).thenReturn(List.of(entity1, entity2));

        List<CourseScheduleByDay> courseSchedulesByMonth = courseService.getCourseSchedulesByMonth(CourseScheduleByMonthFilter.builder().xiaogjStudentIds(List.of("xiaogjStudentId")).build(), yearMonth, ZoneId.of("Australia/Melbourne"));

        assertThat(courseSchedulesByMonth).hasSize(31);

        CourseScheduleByDay firstDaySchedule = courseSchedulesByMonth.stream().filter(schedule -> schedule.date().isEqual(LocalDate.of(2024, 12, 10))).findFirst().get();
        assertThat(firstDaySchedule.courseSchedules()).hasSize(1);
        CourseSchedule firstCourse = firstDaySchedule.courseSchedules().getFirst();
        assertThat(firstCourse).isEqualTo(CourseSchedule.from(entity1));
        CourseScheduleByDay secondDaySchedule = courseSchedulesByMonth.stream().filter(schedule -> schedule.date().isEqual(LocalDate.of(2024, 12, 11))).findFirst().get();
        CourseSchedule secondCourse = secondDaySchedule.courseSchedules().getFirst();
        assertThat(secondCourse).isEqualTo(CourseSchedule.from(entity2));
    }
}