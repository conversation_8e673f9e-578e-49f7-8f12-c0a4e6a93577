package com.firstedu.marsladder.diting.classroom.integration;

import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassStudentService;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(properties = "spring.flyway.clean-disabled=false")
@AutoConfigureMockMvc
@WithMockMarsLadderUser
@AutoConfigureWireMock
public class ClassroomIntegrationTest {

    @Autowired
    MockMvc mockMvc;

    @Autowired
    Flyway flyway;

    @MockBean
    XiaoguanjiaClassStudentService xiaoguanjiaClassStudentService;

    @AfterEach
    void tearDown() {
        flyway.clean();
        flyway.migrate();
    }

    @Test
    void should_bind_classroom_to_xiaoguanjia_classes() throws Exception {
        //Temporary mock
        doNothing().when(xiaoguanjiaClassStudentService).refreshClassStudents(anyString());

        // prepare data
        post("/classes/sync", """
                {
                   "createdAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        // bind
        post("/classrooms/" + "91d6009a-4f5f-478e-b56a-d39eaab737c3" + "/bindings", """
                {
                    "classIds": ["251a3cf7-a099-426e-af9b-2139c60ab917"]
                }""")
                .andExpect(status().isCreated());
        get("/classrooms/" + "91d6009a-4f5f-478e-b56a-d39eaab737c3" + "/bindings")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                          {
                            "id": "251a3cf7-a099-426e-af9b-2139c60ab917",
                            "name": "VCE精品一对一(2024)_Shengjie Tong(Jacky)网课"
                          }
                        ]
                        """));

        // modify binding
        post("/classrooms/" + "91d6009a-4f5f-478e-b56a-d39eaab737c3" + "/bindings", """
                {
                    "classIds": ["251a3cf7-a099-426e-af9b-2139c60ab917", "66f275fd-d959-4980-9330-a270f4c8b3cb"]
                }""")
                .andExpect(status().isCreated());
        get("/classrooms/" + "91d6009a-4f5f-478e-b56a-d39eaab737c3" + "/bindings")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                          {
                            "id": "251a3cf7-a099-426e-af9b-2139c60ab917",
                            "name": "VCE精品一对一(2024)_Shengjie Tong(Jacky)网课"
                          },
                          {
                            "id": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                            "name": "Y1-Y6精品英文一对一 (2024)_Sophia Wang (Sijia）"
                          }
                        ]
                        """));
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}
