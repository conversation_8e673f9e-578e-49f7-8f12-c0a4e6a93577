package com.firstedu.marsladder.diting.classroom.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.classroom.repository.ClassroomBindingsRepository;
import com.firstedu.marsladder.diting.classroom.repository.entity.ClassroomBindingsEntity;
import com.firstedu.marsladder.diting.classroom.service.domain.BindClassCommand;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.classroom.ClassroomStubs.*;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ClassroomServiceImplTest {
    @Mock
    ClassroomBindingsRepository classroomBindingsRepository;

    @Mock
    XiaoguanjiaClassRepository xiaoguanjiaClassRepository;

    @InjectMocks
    ClassroomServiceImpl classroomService;

    @Nested
    class BindClass {

        List<String> classIds = List.of(XIAOGUANJIA_CLASS_ID_1, XIAOGUANJIA_CLASS_ID_2);

        @Test
        void should_bind_xiaoguanjia_class_to_marsladder_classrooms_when_classroom_has_no_bindings() {
            var bindClassCommand = new BindClassCommand(CLASSROOM_ID1, classIds);
            assumeClassExists(XIAOGUANJIA_CLASS_ID_1);
            assumeClassExists(XIAOGUANJIA_CLASS_ID_2);

            when(classroomBindingsRepository.findByClassroomId(CLASSROOM_ID1)).thenReturn(Optional.empty());
            var classroomBindingsEntity = new ClassroomBindingsEntity(null, CLASSROOM_ID1, classIds, null, null);

            classroomService.bindClasses(bindClassCommand);

            verify(classroomBindingsRepository).findByClassroomId(CLASSROOM_ID1);
            verify(classroomBindingsRepository).save(classroomBindingsEntity);
            verify(xiaoguanjiaClassRepository).existsById(XIAOGUANJIA_CLASS_ID_1);
        }

        @Test
        void should_update_classroom_bindings_when_bindings_already_exist() {
            assumeClassExists(XIAOGUANJIA_CLASS_ID_1);

            var existingClassroomBindingsEntity = new ClassroomBindingsEntity(CLASS_BINDINGS_ID, CLASSROOM_ID1, classIds, MOCK_TIME, MOCK_TIME);
            when(classroomBindingsRepository.findByClassroomId(CLASSROOM_ID1)).thenReturn(Optional.of(existingClassroomBindingsEntity));

            var bindClassCommand = new BindClassCommand(CLASSROOM_ID1, List.of(XIAOGUANJIA_CLASS_ID_1));
            classroomService.bindClasses(bindClassCommand);

            verify(classroomBindingsRepository).findByClassroomId(CLASSROOM_ID1);
            verify(classroomBindingsRepository).save(new ClassroomBindingsEntity(CLASS_BINDINGS_ID, CLASSROOM_ID1, List.of(XIAOGUANJIA_CLASS_ID_1), MOCK_TIME, MOCK_TIME));
            verify(xiaoguanjiaClassRepository).existsById(XIAOGUANJIA_CLASS_ID_1);
        }

        @Test
        void should_throw_not_found_exception_when_given_class_id_not_found() {
            when(xiaoguanjiaClassRepository.existsById(XIAOGUANJIA_CLASS_ID_1)).thenReturn(false);

            var bindClassCommand = new BindClassCommand(CLASSROOM_ID1, classIds);
            assertThatExceptionOfType(XiaoguanjiaClassNotExistException.class).isThrownBy(
                    () -> classroomService.bindClasses(bindClassCommand)
            );
        }

        private void assumeClassExists(String xiaoguanjiaClassId) {
            when(xiaoguanjiaClassRepository.existsById(xiaoguanjiaClassId)).thenReturn(true);
        }

        @Test
        void should_get_classroom_bindings_given_classroom_id() {
            when(classroomBindingsRepository.findByClassroomId(CLASSROOM_ID1))
                    .thenReturn(Optional.of(new ClassroomBindingsEntity(CLASS_BINDINGS_ID, CLASSROOM_ID1, classIds, CREATED_AT, UPDATED_AT)));
            when(xiaoguanjiaClassRepository.findAllById(List.of(XIAOGUANJIA_CLASS_ID_1, XIAOGUANJIA_CLASS_ID_2)))
                    .thenReturn(List.of(MOCK_CLASS_1.toEntity(), MOCK_CLASS_2.toEntity()));

            List<XiaoguanjiaClass> bindings = classroomService.getBoundClasses(CLASSROOM_ID1);

            assertThat(bindings).hasSize(2);
            assertThat(bindings.get(0).id()).isEqualTo(XIAOGUANJIA_CLASS_ID_1);
            assertThat(bindings.get(1).id()).isEqualTo(XIAOGUANJIA_CLASS_ID_2);
        }

        @Test
        void should_return_empty_list_when_no_bindings_exist() {
            when(classroomBindingsRepository.findByClassroomId(CLASSROOM_ID1)).thenReturn(Optional.empty());

            var bindings = classroomService.getBoundClasses(CLASSROOM_ID1);

            assertThat(bindings).isEmpty();
        }

    }

    @Nested
    class GetMarsLadderClassrooms {

        XiaoguanjiaClassEntity entity1 = XiaoguanjiaClassEntity.builder().id("mockClassId").shiftId("shiftId").deleted(false).build();
        XiaoguanjiaClassEntity entity2 = XiaoguanjiaClassEntity.builder().id("mockClassId2").shiftId("shiftId").deleted(false).build();

        @Test
        void should_get_marsladder_classrooms_by_shift_id() {
            when(xiaoguanjiaClassRepository.findByShiftIdAndDeletedAndStatus("shiftId", false, 1))
                    .thenReturn(List.of(entity1, entity2));

            when(classroomBindingsRepository.findByXiaoguanjiaClassId("mockClassId"))
                    .thenReturn(List.of(new ClassroomBindingsEntity(null, CLASSROOM_ID1, List.of("mockClassId"), null, null)));

            when(classroomBindingsRepository.findByXiaoguanjiaClassId("mockClassId2"))
                    .thenReturn(List.of(new ClassroomBindingsEntity(null, CLASSROOM_ID2, List.of("mockClassId1"), null, null)));

            var classroomList = classroomService.getClassroomsByShiftId("shiftId");
            assertThat(classroomList.size()).isEqualTo(2);
            assertThat(classroomList.get(0).classroomId()).isEqualTo(CLASSROOM_ID2);
            assertThat(classroomList.get(1).classroomId()).isEqualTo(CLASSROOM_ID1);
        }
    }
}