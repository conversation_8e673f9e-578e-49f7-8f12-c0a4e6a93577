package com.firstedu.marsladder.diting.classroom;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaClassStudent;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;

import java.time.LocalDateTime;

public class ClassroomStudentStubs {
    public static final XiaoguanjiaClassStudent CLASS_STUDENT_1 = new XiaoguanjiaClassStudent(
            "id",
            "mockClassId",
            "xiaogjStudentId",
            ClassStudentStatus.ENTERED,
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            "causeId",
            "outMemo",
            "outType",
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            LocalDateTime.of(2024, 11, 11, 11, 11, 11)
    );

    public static final XiaoguanjiaClassStudent CLASS_STUDENT_1_1 = new XiaoguanjiaClassStudent(
            "id1",
            "mockClassId1",
            "xiaogjStudentId",
            ClassStudentStatus.ENTERED,
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            "causeId",
            "outMemo",
            "outType",
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            LocalDateTime.of(2024, 11, 11, 11, 11, 11)
    );

    public static final XiaoguanjiaClassStudent CLASS_STUDENT_1_2 = new XiaoguanjiaClassStudent(
            "id2",
            "mockClassId2",
            "xiaogjStudentId",
            ClassStudentStatus.ENTERED,
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            "causeId",
            "outMemo",
            "outType",
            LocalDateTime.of(2024, 11, 11, 11, 11, 11),
            LocalDateTime.of(2024, 11, 11, 11, 11, 11)
    );
}
