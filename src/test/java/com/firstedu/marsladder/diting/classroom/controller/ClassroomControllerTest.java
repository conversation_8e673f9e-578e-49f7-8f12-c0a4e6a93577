package com.firstedu.marsladder.diting.classroom.controller;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.classroom.service.ClassroomService;
import com.firstedu.marsladder.diting.classroom.service.domain.BindClassCommand;
import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static com.firstedu.marsladder.diting.classroom.ClassroomStubs.CLASSROOM_ID1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.*;
import static com.firstedu.marsladder.diting.xiaoguanjia.GradeStubs.GRADE_ID_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ShiftStubs.SHIFT_ID_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.SubjectStubs.SUBJECT_ID_1;
import static org.mockito.Mockito.*;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = ClassroomController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser
class ClassroomControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    ClassroomService classroomService;

    @Nested
    class Bind {
        List<String> classIds = List.of(XIAOGUANJIA_CLASS_ID_1, XIAOGUANJIA_CLASS_ID_2);
        BindClassCommand bindClassCommand = new BindClassCommand(CLASSROOM_ID1, classIds);

        @Test
        void should_return_201_when_binding_xiaoguanjia_class_to_marsladder_classrooms() throws Exception {
            doNothing().when(classroomService).bindClasses(bindClassCommand);

            post("/classrooms/" + CLASSROOM_ID1 + "/bindings", """
                    {
                      "classIds": ["fb6c27f9-3f60-44fd-a28e-6f17c67caa16", "66f275fd-d959-4980-9330-a270f4c8b3cb"]
                    }
                    """)
                    .andExpect(status().isCreated());
            verify(classroomService).bindClasses(bindClassCommand);
        }

        @Test
        void should_return_404_whne_given_class_id_not_found() throws Exception {
            doThrow(new XiaoguanjiaClassNotExistException("No such xiaoguanjia class id: " + XIAOGUANJIA_CLASS_ID_1))
                    .when(classroomService).bindClasses(bindClassCommand);

            post("/classrooms/" + CLASSROOM_ID1 + "/bindings", """
                    {
                      "classIds": ["fb6c27f9-3f60-44fd-a28e-6f17c67caa16", "66f275fd-d959-4980-9330-a270f4c8b3cb"]
                    }
                    """)
                    .andExpect(status().isNotFound());
        }

        @Test
        void should_return_200_when_get_bounded_classes_of_a_classroom() throws Exception {
            when(classroomService.getBoundClasses(CLASSROOM_ID1))
                    .thenReturn(List.of(new XiaoguanjiaClass(
                            XIAOGUANJIA_CLASS_ID_1,
                            XIAOGUANJIA_CLASS_NAME_1,
                            XIAOGUANJIA_CLASS_1_HEADMASTER_ID,
                            SUBJECT_ID_1,
                            SHIFT_ID_1,
                            false,
                            MOCK_TIME,
                            GRADE_ID_1)));

            get("/classrooms/" + CLASSROOM_ID1 + "/bindings")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "fb6c27f9-3f60-44fd-a28e-6f17c67caa16",
                                "name": "Y1-Y6精品一对一 (2024)_Enoch Lian"
                              }
                            ]
                            """));
        }

        @Test
        void should_return_200_when_bounded_xiaoguanjia_classes_of_a_classroom_is_empty() throws Exception {
            when(classroomService.getBoundClasses(CLASSROOM_ID1))
                    .thenReturn(List.of());

            get("/classrooms/" + CLASSROOM_ID1 + "/bindings")
                    .andExpect(status().isOk())
                    .andExpect(content().json("[]"));
        }
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }

}