package com.firstedu.marsladder.diting.subject.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.subject.SubjectService;
import com.firstedu.marsladder.diting.subject.service.domain.Subject;
import com.firstedu.marsladder.diting.subject.service.domain.SubjectType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = SubjectController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser
class SubjectControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SubjectService subjectService;

    @Test
    void should_return_200_when_getting_subjects() throws Exception {
        when(subjectService.getSubjects())
                .thenReturn(List.of(new Subject("850a3961-0461-4524-b7d5-56a00cef1101", SubjectType.MATHS)));

        get("/subjects")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [{"id": "850a3961-0461-4524-b7d5-56a00cef1101", "name": "MATHS"}]
                        """));
    }

    @Test
    void should_return_200_when_get_subject_by_id_() throws Exception {
        when(subjectService.getSubjectById("850a3961-0461-4524-b7d5-56a00cef1101"))
                .thenReturn(new Subject("850a3961-0461-4524-b7d5-56a00cef1101", SubjectType.MATHS));

        get("/subjects/850a3961-0461-4524-b7d5-56a00cef1101")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        {"id": "850a3961-0461-4524-b7d5-56a00cef1101", "name": "MATHS"}
                        """));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}

