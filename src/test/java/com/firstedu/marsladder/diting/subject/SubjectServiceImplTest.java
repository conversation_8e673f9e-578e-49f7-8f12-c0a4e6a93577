package com.firstedu.marsladder.diting.subject;

import com.firstedu.marsladder.diting.subject.exception.SubjectNotFoundException;
import com.firstedu.marsladder.diting.subject.service.domain.Subject;
import com.firstedu.marsladder.diting.subject.service.impl.SubjectServiceImpl;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaSubjectRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.subject.service.domain.SubjectType.MATHS;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_TIME;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus.ENABLED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SubjectServiceImplTest {
    @Mock
    XiaoguanjiaSubjectRepository xiaoguanjiaSubjectRepository;

    @InjectMocks
    SubjectServiceImpl subjectService;

    @Test
    void should_get_all_subjects() {
        var subjectId = "850a3961-0461-4524-b7d5-56a00cef1101";
        var subjectName = "数学";
        when(xiaoguanjiaSubjectRepository.findAll()).thenReturn(List.of(
                new XiaoguanjiaSubjectEntity(
                        subjectId,
                        subjectName,
                        ENABLED,
                        MOCK_TIME,
                        MOCK_TIME
                )
        ));

        List<Subject> subjects = subjectService.getSubjects();

        assertThat(subjects).hasSize(1);
        var subject = subjects.getFirst();
        assertThat(subject.id()).isEqualTo(subjectId);
        assertThat(subject.subjectType()).isEqualTo(MATHS);
    }

    @Test
    void should_get_subject_by_subject_id() {
        var subjectId = "850a3961-0461-4524-b7d5-56a00cef1101";
        var subjectName = "数学";
        when(xiaoguanjiaSubjectRepository.findById(subjectId)).thenReturn(Optional.of(
                new XiaoguanjiaSubjectEntity(
                        subjectId,
                        subjectName,
                        ENABLED,
                        MOCK_TIME,
                        MOCK_TIME
                )
        ));

        var subject = subjectService.getSubjectById(subjectId);

        assertThat(subject.id()).isEqualTo(subjectId);
        assertThat(subject.subjectType()).isEqualTo(MATHS);
    }

    @Test
    void should_throw_exception_when_subject_with_given_id_not_found() {
        var subjectId = "850a3961-0461-4524-b7d5-56a00cef1101";
        when(xiaoguanjiaSubjectRepository.findById(subjectId)).thenReturn(Optional.empty());

        assertThatExceptionOfType(SubjectNotFoundException.class).isThrownBy(
                () -> subjectService.getSubjectById(subjectId)
        );
    }

}