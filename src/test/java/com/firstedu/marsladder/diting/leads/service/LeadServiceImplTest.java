package com.firstedu.marsladder.diting.leads.service;

import com.firstedu.marsladder.diting.leads.exception.LeadNotFoundException;
import com.firstedu.marsladder.diting.leads.repository.LeadRepository;
import com.firstedu.marsladder.diting.leads.repository.entity.LeadEntity;
import com.firstedu.marsladder.diting.leads.service.domain.Lead;
import com.firstedu.marsladder.diting.leads.service.impl.LeadServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.time.LocalDateTime;
import java.util.List;

import static com.firstedu.marsladder.diting.leads.service.domain.Campus.GLEN_WAVERLEY;
import static com.firstedu.marsladder.diting.leads.service.domain.ReferralSource.YOUTUBE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LeadServiceImplTest {

    Lead mockLead = Lead.builder()
            .id("lead_id")
            .grade(4)
            .campus(GLEN_WAVERLEY)
            .name("ZhangYang")
            .phone("15129033023")
            .email("<EMAIL>")
            .postCode("710307")
            .referralSources(List.of(YOUTUBE))
            .inquiry("Looking for maths tutoring for y9 kid")
            .build();

    @InjectMocks
    private LeadServiceImpl leadService;

    @Mock
    private LeadRepository leadRepository;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Test
    void should_exists_email_when_create_lead() {
        var email = "<EMAIL>";
        when(leadRepository.existsByEmail(email)).thenReturn(true);

        var mockLeadService = Mockito.spy(new LeadServiceImpl(leadRepository, applicationEventPublisher));
        doReturn(mockLead).when(mockLeadService).updateLead(email, mockLead);

        var updatedLead = mockLeadService.createLead(mockLead);
        assertThat(updatedLead.getId()).isEqualTo("lead_id");
        assertThat(updatedLead.getGrade()).isEqualTo(4);
        assertThat(updatedLead.getCampus()).isEqualTo(GLEN_WAVERLEY);
        assertThat(updatedLead.getName()).isEqualTo("ZhangYang");
        assertThat(updatedLead.getPhone()).isEqualTo("15129033023");
        assertThat(updatedLead.getEmail()).isEqualTo("<EMAIL>");
        assertThat(updatedLead.getPostCode()).isEqualTo("710307");
        assertThat(updatedLead.getReferralSources().getFirst()).isEqualTo(YOUTUBE);
        assertThat(updatedLead.getInquiry()).isEqualTo("Looking for maths tutoring for y9 kid");
    }

    @Test
    void should_not_exists_email_when_create_lead() {
        var createdLeadEntity = LeadEntity.builder()
                .id("lead_id")
                .grade(4)
                .campus(GLEN_WAVERLEY)
                .name("ZhangYang")
                .phone("15129033023")
                .email("<EMAIL>")
                .postCode("710307")
                .referralSources(List.of(YOUTUBE))
                .inquiry("Looking for maths tutoring for y9 kid")
                .build();
        var email = "<EMAIL>";
        when(leadRepository.existsByEmail(email)).thenReturn(false);
        when(leadRepository.save(mockLead.toEntity())).thenReturn(createdLeadEntity);

        var updatedLead = leadService.createLead(mockLead);
        assertThat(updatedLead.getId()).isEqualTo("lead_id");
        assertThat(updatedLead.getGrade()).isEqualTo(4);
        assertThat(updatedLead.getCampus()).isEqualTo(GLEN_WAVERLEY);
        assertThat(updatedLead.getName()).isEqualTo("ZhangYang");
        assertThat(updatedLead.getPhone()).isEqualTo("15129033023");
        assertThat(updatedLead.getEmail()).isEqualTo("<EMAIL>");
        assertThat(updatedLead.getPostCode()).isEqualTo("710307");
        assertThat(updatedLead.getReferralSources().getFirst()).isEqualTo(YOUTUBE);
        assertThat(updatedLead.getInquiry()).isEqualTo("Looking for maths tutoring for y9 kid");
    }

    @Test
    void should_update_lead_successfully() {
        var email = "<EMAIL>";
        var existedLeadEntity = LeadEntity.builder()
                .id("existed_lead_id")
                .grade(4)
                .campus(GLEN_WAVERLEY)
                .name("ZhangYang")
                .phone("15129033023")
                .email("<EMAIL>")
                .postCode("710307")
                .createdAt(LocalDateTime.of(2024, 8, 8, 0, 0, 0))
                .referralSources(List.of(YOUTUBE))
                .inquiry("Looking for maths tutoring for y9 kid")
                .build();

        var updatedLeadEntity = LeadEntity.builder()
                .id("existed_lead_id")
                .grade(4)
                .campus(GLEN_WAVERLEY)
                .name("ZhangYang")
                .phone("15129033023")
                .email("<EMAIL>")
                .postCode("710307")
                .createdAt(LocalDateTime.of(2024, 8, 8, 0, 0, 0))
                .referralSources(List.of(YOUTUBE))
                .inquiry("Looking for maths tutoring for y9 kid")
                .build();

        when(leadRepository.findByEmail(email)).thenReturn(existedLeadEntity);
        when(leadRepository.save(updatedLeadEntity)).thenReturn(updatedLeadEntity);
        leadService.updateLead(email, mockLead);
        verify(leadRepository).save(updatedLeadEntity);
    }

    @Test
    void should_throw_exception_when_update_lead() {
        var email = "<EMAIL>";
        when(leadRepository.findByEmail(email)).thenReturn(null);
        var exception = assertThrows(LeadNotFoundException.class, () -> leadService.updateLead(email, mockLead));
        assertThat(exception.getMessage()).isEqualTo("Lead not found by email: <EMAIL>");
    }
}
