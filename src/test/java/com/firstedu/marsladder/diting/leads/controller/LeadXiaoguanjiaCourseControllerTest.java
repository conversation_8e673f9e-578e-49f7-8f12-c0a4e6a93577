package com.firstedu.marsladder.diting.leads.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.firstedu.marsladder.diting.leads.controller.dto.LeadCreationRequest;
import com.firstedu.marsladder.diting.leads.controller.dto.VceMockLeadCreateOrUpdateRequest;
import com.firstedu.marsladder.diting.leads.service.LeadService;
import com.firstedu.marsladder.diting.leads.service.VceMockLeadService;
import com.firstedu.marsladder.diting.leads.service.domain.Lead;
import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static com.firstedu.marsladder.diting.leads.service.domain.Campus.GLEN_WAVERLEY;
import static com.firstedu.marsladder.diting.leads.service.domain.ReferralSource.YOUTUBE;
import static org.mockito.Mockito.when;
import static org.springframework.http.HttpHeaders.LOCATION;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = LeadController.class,
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
public class LeadXiaoguanjiaCourseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private LeadService leadService;

    @MockBean
    private VceMockLeadService vceMockLeadService;

    @Test
    void should_return_201_when_successfully_created_lead() throws Exception {
        var mockLeadCreationRequest = LeadCreationRequest.builder()
                .grade(4)
                .campus(GLEN_WAVERLEY)
                .name("ZhangYang")
                .phone("15129033023")
                .email("<EMAIL>")
                .postCode("710307")
                .referralSources(List.of(YOUTUBE))
                .inquiry("Looking for maths tutoring for y9 kid")
                .build();

        var mockCreatedLead = Lead.builder()
                .id("lead_id")
                .grade(4)
                .campus(GLEN_WAVERLEY)
                .name("ZhangYang")
                .phone("15129033023")
                .email("<EMAIL>")
                .postCode("710307")
                .referralSources(List.of(YOUTUBE))
                .inquiry("Looking for maths tutoring for y9 kid")
                .build();

        when(leadService.createLead(mockLeadCreationRequest.toLead())).thenReturn(mockCreatedLead);

        post("/public/leads", mockLeadCreationRequest)
                .andExpect(status().isCreated())
                .andExpect(header().string(LOCATION, "/leads/" + mockCreatedLead.getId()))
                .andExpect(jsonPath("$.campus").value(GLEN_WAVERLEY.name()))
                .andExpect(jsonPath("$.name").value("ZhangYang"))
                .andExpect(jsonPath("$.phone").value("15129033023"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.postCode").value("710307"))
                .andExpect(jsonPath("$.referralSources[0]").value(YOUTUBE.name()));
    }

    @Test
    void should_return_200_when_successfully_created_or_updated_vce_mock_lead() throws Exception {
        var mockVceLeadCreationRequest = VceMockLeadCreateOrUpdateRequest.builder()
                .firstName("Zhang")
                .lastName("Yang")
                .school("school")
                .email("<EMAIL>")
                .phoneNumber("15129033023")
                .subjects(List.of("MATH"))
                .build();

        var mockCreatedLead = VceMockLead.builder()
                .id("lead_id")
                .firstName("Zhang")
                .lastName("Yang")
                .school("school")
                .email("<EMAIL>")
                .phoneNumber("15129033023")
                .subjects(List.of("MATH"))
                .build();

        when(vceMockLeadService.createOrUpdateVceMockLead(mockVceLeadCreationRequest.toVceMockLead())).thenReturn(mockCreatedLead);

        post("/public/vce-mock-leads", mockVceLeadCreationRequest)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.firstName").value("Zhang"))
                .andExpect(jsonPath("$.lastName").value("Yang"))
                .andExpect(jsonPath("$.school").value("school"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.phoneNumber").value("15129033023"))
                .andExpect(jsonPath("$.subjects[0]").value("MATH"));
    }

    private ResultActions post(String url, LeadCreationRequest leadCreationRequest) throws Exception {
        return post(url, objectMapper.writeValueAsString(leadCreationRequest));
    }

    private ResultActions post(String url, VceMockLeadCreateOrUpdateRequest request) throws Exception {
        return post(url, objectMapper.writeValueAsString(request));
    }

    private ResultActions post(String url, String content) throws Exception {
        return mockMvc.perform(MockMvcRequestBuilders.post(url).contentType(APPLICATION_JSON).content(content));
    }
}
