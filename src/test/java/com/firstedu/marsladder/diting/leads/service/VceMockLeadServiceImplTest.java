package com.firstedu.marsladder.diting.leads.service;

import com.firstedu.marsladder.diting.leads.exception.LeadNotFoundException;
import com.firstedu.marsladder.diting.leads.repository.VceMockLeadRepository;
import com.firstedu.marsladder.diting.leads.repository.entity.VceMockLeadEntity;
import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import com.firstedu.marsladder.diting.leads.service.impl.VceMockLeadServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VceMockLeadServiceImplTest {
    VceMockLead mockLead = VceMockLead.builder()
            .id("lead_id")
            .firstName("ZHANG")
            .lastName("YANG")
            .phoneNumber("1512903302322")
            .email("<EMAIL>")
            .school("school1")
            .subjects(List.of("MATH"))
            .build();

    @InjectMocks
    private VceMockLeadServiceImpl mockLeadService;

    @Mock
    private VceMockLeadRepository leadRepository;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Test
    void should_exists_email_when_create_vce_mock_lead() {
        var email = "<EMAIL>";
        when(leadRepository.existsByEmail(email)).thenReturn(true);

        VceMockLeadEntity existedEntity = VceMockLeadEntity.builder()
                .id("lead_id")
                .firstName("ZHANG")
                .lastName("YANG")
                .phoneNumber("15129033023")
                .email("<EMAIL>")
                .school("school")
                .subjects(List.of("MATH"))
                .build();
        when(leadRepository.findByEmail(email)).thenReturn(Optional.of(existedEntity));

        VceMockLeadEntity updateEntity = VceMockLeadEntity.builder()
                .id("lead_id")
                .firstName("ZHANG")
                .lastName("YANG")
                .phoneNumber("1512903302322")
                .email("<EMAIL>")
                .school("school1")
                .subjects(List.of("MATH"))
                .build();
        System.out.println(updateEntity.toString());
        when(leadRepository.save(updateEntity)).thenReturn(updateEntity);

        mockLeadService.createOrUpdateVceMockLead(mockLead);
        verify(leadRepository, times(1)).save(updateEntity);
        verify(applicationEventPublisher, times(1)).publishEvent(any());
    }

    @Test
    void should_not_exists_email_when_create_lead() {
        var email = "<EMAIL>";
        when(leadRepository.existsByEmail(email)).thenReturn(false);
        when(leadRepository.save(mockLead.toEntity())).thenReturn(mockLead.toEntity());

        mockLeadService.createOrUpdateVceMockLead(mockLead);
        verify(leadRepository, times(1)).save(mockLead.toEntity());
        verify(applicationEventPublisher, times(1)).publishEvent(any());
    }

    @Test
    void should_throw_exception_when_update_lead() {
        var email = "<EMAIL>";
        when(leadRepository.existsByEmail(email)).thenReturn(true);
        when(leadRepository.findByEmail(email)).thenReturn(Optional.empty());

        var exception = assertThrows(LeadNotFoundException.class, () -> mockLeadService.createOrUpdateVceMockLead(mockLead));
        assertThat(exception.getMessage()).isEqualTo("vce mock Lead not found by email: <EMAIL>");
    }
}
