package com.firstedu.marsladder.diting.student.service;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaStudentNotFoundException;
import com.firstedu.marsladder.diting.student.service.impl.StudentServiceImpl;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaDepartmentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaDepartmentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.MOCK_STUDENT_1;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StudentServiceImplTest {
    @Mock
    XiaoguanjiaStudentRepository xiaoguanjiaStudentRepository;

    @Mock
    XiaoguanjiaDepartmentRepository xiaoguanjiaDepartmentRepository;

    @InjectMocks
    StudentServiceImpl studentService;

    @Nested
    class GetStudents {
        @Test
        void should_get_searched_students_by_name_when_name_is_not_empty() {
            Pageable pageable = PageRequest.of(0, 10);
            XiaoguanjiaStudentEntity student = new XiaoguanjiaStudentEntity(
                    "12345", XiaoguanjiaStudentStatus.ENROLLED, "campus01", "John Doe", "John",
                    1, "1234567890", "123 Main St", "johndoe", "http://example.com/image.jpg",
                    "SN123456", "ID987654321", LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0),
                    LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0), "Full Time School", "Full Time Class",
                    "Grade01", LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Master01",
                    LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Type01", LocalDateTime.now(),
                    LocalDateTime.now(), "CustomerCampus01", "Introducer01",
                    LocalDateTime.of(2024, 12, 31, 0, 0, 0, 0), 0, 1, LocalDateTime.now(),
                    LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), 0,
                    "No Cause", "CauseID123", "No description", 1, "SalePerson01", "100",
                    LocalDateTime.now(), LocalDateTime.now()
            );
            
            XiaoguanjiaDepartmentEntity campusDepartment = XiaoguanjiaDepartmentEntity.builder()
                    .id("campus01")
                    .name("Test Campus")
                    .isCampus(true)
                    .build();
            
            Page<XiaoguanjiaStudentEntity> page = new PageImpl<>(List.of(student), pageable, 1);
            when(xiaoguanjiaStudentRepository.findByNameLikeIgnoreCaseAndStatusIn(pageable, "%John%", List.of(XiaoguanjiaStudentStatus.ENROLLED, XiaoguanjiaStudentStatus.AUDITION)))
                    .thenReturn(page);
            when(xiaoguanjiaDepartmentRepository.findAll())
                    .thenReturn(List.of(campusDepartment));

            var pageStudent = studentService.getStudentByName(pageable, "John");
            assertThat(pageStudent.getTotalElements()).isEqualTo(1);
            assertThat(pageStudent.getContent().getFirst().id()).isEqualTo("12345");
            assertThat(pageStudent.getContent().getFirst().name()).isEqualTo("John Doe");
            assertThat(pageStudent.getContent().getFirst().serial()).isEqualTo("SN123456");
            assertThat(pageStudent.getContent().getFirst().userName()).isEqualTo("johndoe");
            assertThat(pageStudent.getContent().getFirst().status()).isEqualTo(XiaoguanjiaStudentStatus.ENROLLED);
            assertThat(pageStudent.getContent().getFirst().campusName()).isEqualTo("Test Campus");
            assertThat(pageStudent.getContent().getFirst().campusId()).isEqualTo("campus01");
        }

        @Test
        void should_get_searched_students_by_name_when_name_is_empty() {
            Pageable pageable = PageRequest.of(0, 10);
            XiaoguanjiaStudentEntity student = new XiaoguanjiaStudentEntity(
                    "12345", XiaoguanjiaStudentStatus.ENROLLED, "campus01", "John Doe", "John",
                    1, "1234567890", "123 Main St", "johndoe", "http://example.com/image.jpg",
                    "SN123456", "ID987654321", LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0),
                    LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0), "Full Time School", "Full Time Class",
                    "Grade01", LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Master01",
                    LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Type01", LocalDateTime.now(),
                    LocalDateTime.now(), "CustomerCampus01", "Introducer01",
                    LocalDateTime.of(2024, 12, 31, 0, 0, 0, 0), 0, 1, LocalDateTime.now(),
                    LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), 0,
                    "No Cause", "CauseID123", "No description", 1, "SalePerson01", "100",
                    LocalDateTime.now(), LocalDateTime.now()
            );
            
            XiaoguanjiaDepartmentEntity campusDepartment = XiaoguanjiaDepartmentEntity.builder()
                    .id("campus01")
                    .name("Test Campus")
                    .isCampus(true)
                    .build();
            
            Page<XiaoguanjiaStudentEntity> page = new PageImpl<>(List.of(student), pageable, 1);
            when(xiaoguanjiaStudentRepository.findAllByStatusIn(pageable, List.of(XiaoguanjiaStudentStatus.ENROLLED, XiaoguanjiaStudentStatus.AUDITION)))
                    .thenReturn(page);
            when(xiaoguanjiaDepartmentRepository.findAll())
                    .thenReturn(List.of(campusDepartment));

            var pageStudent = studentService.getStudentByName(pageable, null);
            assertThat(pageStudent.getTotalElements()).isEqualTo(1);
            assertThat(pageStudent.getContent().getFirst().id()).isEqualTo("12345");
            assertThat(pageStudent.getContent().getFirst().name()).isEqualTo("John Doe");
            assertThat(pageStudent.getContent().getFirst().serial()).isEqualTo("SN123456");
            assertThat(pageStudent.getContent().getFirst().userName()).isEqualTo("johndoe");
            assertThat(pageStudent.getContent().getFirst().status()).isEqualTo(XiaoguanjiaStudentStatus.ENROLLED);
            assertThat(pageStudent.getContent().getFirst().campusName()).isEqualTo("Test Campus");
            assertThat(pageStudent.getContent().getFirst().campusId()).isEqualTo("campus01");
        }

        @Test
        void should_get_student_by_studentId() {
            XiaoguanjiaDepartmentEntity campusDepartment = XiaoguanjiaDepartmentEntity.builder()
                    .id("C1001")
                    .name("Test Campus")
                    .isCampus(true)
                    .build();
            
            when(xiaoguanjiaStudentRepository.findById("1"))
                    .thenReturn(Optional.of(MOCK_STUDENT_1.toEntity()));
            when(xiaoguanjiaDepartmentRepository.findAll())
                    .thenReturn(List.of(campusDepartment));

            var student = studentService.getStudentById("1");
            assertThat(student.id()).isEqualTo("1");
            assertThat(student.name()).isEqualTo("John Doe");
            assertThat(student.serial()).isEqualTo("A001");
            assertThat(student.userName()).isEqualTo("johndoe");
            assertThat(student.status()).isEqualTo(XiaoguanjiaStudentStatus.ENROLLED);
            assertThat(student.campusName()).isEqualTo("Test Campus");
            assertThat(student.campusId()).isEqualTo("C1001");
        }

        @Test
        void should_get_students_by_studentIds() {
            XiaoguanjiaDepartmentEntity campusDepartment = XiaoguanjiaDepartmentEntity.builder()
                    .id("C1001")
                    .name("Test Campus")
                    .isCampus(true)
                    .build();

            when(xiaoguanjiaStudentRepository.findAllByIdIn(List.of("1")))
                    .thenReturn(List.of(MOCK_STUDENT_1.toEntity()));
            when(xiaoguanjiaDepartmentRepository.findAll())
                    .thenReturn(List.of(campusDepartment));

            var student = studentService.getStudentsByIds(List.of("1"));
            assertThat(student.getFirst().id()).isEqualTo("1");
            assertThat(student.getFirst().name()).isEqualTo("John Doe");
            assertThat(student.getFirst().serial()).isEqualTo("A001");
            assertThat(student.getFirst().userName()).isEqualTo("johndoe");
            assertThat(student.getFirst().status()).isEqualTo(XiaoguanjiaStudentStatus.ENROLLED);
            assertThat(student.getFirst().campusName()).isEqualTo("Test Campus");
            assertThat(student.getFirst().campusId()).isEqualTo("C1001");
        }

        @Test
        void should_throw_exception_when_get_student_by_id() {
            when(xiaoguanjiaStudentRepository.findById("1")).thenReturn(Optional.empty());
            var exception = assertThrows(XiaoguanjiaStudentNotFoundException.class, () -> studentService.getStudentById("1"));
            assertThat(exception.getMessage()).isEqualTo("Student not found by id: 1");
        }

        @Test
        void should_return_null_campus_name_when_campus_id_is_null() {
            XiaoguanjiaStudentEntity student = new XiaoguanjiaStudentEntity(
                    "12345", XiaoguanjiaStudentStatus.ENROLLED, null, "John Doe", "John",
                    1, "1234567890", "123 Main St", "johndoe", "http://example.com/image.jpg",
                    "SN123456", "ID987654321", LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0),
                    LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0), "Full Time School", "Full Time Class",
                    "Grade01", LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Master01",
                    LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Type01", LocalDateTime.now(),
                    LocalDateTime.now(), "CustomerCampus01", "Introducer01",
                    LocalDateTime.of(2024, 12, 31, 0, 0, 0, 0), 0, 1, LocalDateTime.now(),
                    LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), 0,
                    "No Cause", "CauseID123", "No description", 1, "SalePerson01", "100",
                    LocalDateTime.now(), LocalDateTime.now()
            );
            
            when(xiaoguanjiaStudentRepository.findById("12345"))
                    .thenReturn(Optional.of(student));
            when(xiaoguanjiaDepartmentRepository.findAll())
                    .thenReturn(List.of());

            var result = studentService.getStudentById("12345");
            assertThat(result.campusName()).isNull();
            assertThat(result.campusId()).isNull();
        }

        @Test
        void should_return_null_campus_name_when_campus_not_found() {
            XiaoguanjiaStudentEntity student = new XiaoguanjiaStudentEntity(
                    "12345", XiaoguanjiaStudentStatus.ENROLLED, "nonexistent_campus", "John Doe", "John",
                    1, "1234567890", "123 Main St", "johndoe", "http://example.com/image.jpg",
                    "SN123456", "ID987654321", LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0),
                    LocalDateTime.of(2000, 1, 1, 0, 0, 0, 0), "Full Time School", "Full Time Class",
                    "Grade01", LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Master01",
                    LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0), "Type01", LocalDateTime.now(),
                    LocalDateTime.now(), "CustomerCampus01", "Introducer01",
                    LocalDateTime.of(2024, 12, 31, 0, 0, 0, 0), 0, 1, LocalDateTime.now(),
                    LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), 0,
                    "No Cause", "CauseID123", "No description", 1, "SalePerson01", "100",
                    LocalDateTime.now(), LocalDateTime.now()
            );
            
            when(xiaoguanjiaStudentRepository.findById("12345"))
                    .thenReturn(Optional.of(student));
            when(xiaoguanjiaDepartmentRepository.findAll())
                    .thenReturn(List.of());

            var result = studentService.getStudentById("12345");
            assertThat(result.campusName()).isNull();
            assertThat(result.campusId()).isEqualTo("nonexistent_campus");
        }
    }
}