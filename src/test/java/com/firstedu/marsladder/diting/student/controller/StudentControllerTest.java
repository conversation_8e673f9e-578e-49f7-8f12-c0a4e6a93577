package com.firstedu.marsladder.diting.student.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.student.service.StudentService;
import com.firstedu.marsladder.diting.student.service.domain.Student;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = StudentController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser(roles = "ADMIN")
class StudentControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    StudentService studentService;

    @Test
    void should_return_searched_students_by_name_successfully() throws Exception {
        Pageable pageable = PageRequest.of(0, 10);
        List<Student> students = List.of(new Student("id1", "zhang san", "001", "zhang san@firstedu", XiaoguanjiaStudentStatus.ENROLLED, "Test Campus", "campus01"));
        Page<Student> pages = new PageImpl<>(students, pageable, students.size());
        when(studentService.getStudentByName(pageable, "zhang"))
                .thenReturn(pages);

        mockMvc.perform(MockMvcRequestBuilders.get("/students")
                        .param("name", "zhang")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalCounts").value(1))
                .andExpect(jsonPath("$.content[0].id").value("id1"))
                .andExpect(jsonPath("$.content[0].name").value("zhang san"))
                .andExpect(jsonPath("$.content[0].serial").value("001"))
                .andExpect(jsonPath("$.content[0].userName").value("zhang san@firstedu"))
                .andExpect(jsonPath("$.content[0].status").value(XiaoguanjiaStudentStatus.ENROLLED.name()))
                .andExpect(jsonPath("$.content[0].campusName").value("Test Campus"))
                .andExpect(jsonPath("$.content[0].campusId").value("campus01"));
    }

    @Test
    void should_return_student_successfully() throws Exception {
        when(studentService.getStudentById("id1"))
                .thenReturn(new Student("id1", "zhang san", "001", "zhang san@firstedu", XiaoguanjiaStudentStatus.ENROLLED, "Test Campus", "campus01"));

        mockMvc.perform(MockMvcRequestBuilders.get("/students/id1")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("id1"))
                .andExpect(jsonPath("$.name").value("zhang san"))
                .andExpect(jsonPath("$.serial").value("001"))
                .andExpect(jsonPath("$.userName").value("zhang san@firstedu"))
                .andExpect(jsonPath("$.status").value(XiaoguanjiaStudentStatus.ENROLLED.name()))
                .andExpect(jsonPath("$.campusName").value("Test Campus"))
                .andExpect(jsonPath("$.campusId").value("campus01"));
    }

    @Test
    void should_return_students_successfully() throws Exception {
        when(studentService.getStudentsByIds(List.of("id1")))
                .thenReturn(List.of(new Student("id1", "zhang san", "001", "zhang san@firstedu", XiaoguanjiaStudentStatus.ENROLLED, "Test Campus", "campus01")));

        mockMvc.perform(MockMvcRequestBuilders.get("/students/batch")
                        .param("studentIds", "id1")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value("id1"))
                .andExpect(jsonPath("$[0].name").value("zhang san"))
                .andExpect(jsonPath("$[0].serial").value("001"))
                .andExpect(jsonPath("$[0].userName").value("zhang san@firstedu"))
                .andExpect(jsonPath("$[0].status").value(XiaoguanjiaStudentStatus.ENROLLED.name()))
                .andExpect(jsonPath("$[0].campusName").value("Test Campus"))
                .andExpect(jsonPath("$[0].campusId").value("campus01"));
    }
}