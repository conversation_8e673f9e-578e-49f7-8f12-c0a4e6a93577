package com.firstedu.marsladder.diting.security;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class WebMvcTestConfiguration {

    BearerTokenAuthenticationBuilder bearerTokenAuthenticationBuilder;
    HttpServletRequest request;

    @Bean
    public JwtAuthenticationTokenConverter jwtAuthenticationConverter() {
        return new JwtAuthenticationTokenConverter(request, bearerTokenAuthenticationBuilder);
    }

    @Bean
    AmzOidcBearerTokenResolver bearerTokenResolver() {
        return new AmzOidcBearerTokenResolver();
    }
}
