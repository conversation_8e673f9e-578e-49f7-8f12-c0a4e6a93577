package com.firstedu.marsladder.diting.security.custommockuser;

import com.firstedu.marsladder.diting.security.RealusOAuth2AuthenticatedPrincipal;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.DefaultOAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthentication;
import org.springframework.security.test.context.support.WithSecurityContextFactory;

import java.time.Instant;
import java.util.Arrays;
import java.util.Map;

public class WithMockCustomUserSecurityContextFactory implements WithSecurityContextFactory<WithMockMarsLadderUser> {
    private final static String ACCESS_TOKEN_VALUE = "token";

    @Override
    public SecurityContext createSecurityContext(WithMockMarsLadderUser marsLadderUser) {
        var context = SecurityContextHolder.createEmptyContext();
        var jwt = Jwt.withTokenValue(ACCESS_TOKEN_VALUE)
                .subject(marsLadderUser.name())
                .expiresAt(Instant.MAX)
                .header("Test", "Yes")
                .build();
        var bearerTokenAuthentication = buildBearerTokenAuthentication(jwt, marsLadderUser.email(), marsLadderUser.roles());
        context.setAuthentication(bearerTokenAuthentication);
        return context;
    }

    private static BearerTokenAuthentication buildBearerTokenAuthentication(Jwt jwt, String email, String[] roles) {
        var authorities = Arrays.stream(roles).map(role -> (GrantedAuthority) new SimpleGrantedAuthority("ROLE_" + role)).toList();
        var oAuth2AuthenticatedPrincipal = new DefaultOAuth2AuthenticatedPrincipal(
                jwt.getSubject(),
                Map.of("email", email),
                authorities
        );
        var marsLadderPrincipal = new RealusOAuth2AuthenticatedPrincipal(oAuth2AuthenticatedPrincipal, email);
        var accessToken = new OAuth2AccessToken(OAuth2AccessToken.TokenType.BEARER, jwt.getTokenValue(), jwt.getIssuedAt(), jwt.getExpiresAt());
        return new BearerTokenAuthentication(marsLadderPrincipal, accessToken, authorities);
    }
}
