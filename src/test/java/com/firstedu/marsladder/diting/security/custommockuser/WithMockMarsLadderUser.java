package com.firstedu.marsladder.diting.security.custommockuser;

import org.springframework.security.test.context.support.WithSecurityContext;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@WithSecurityContext(factory = WithMockCustomUserSecurityContextFactory.class)
public @interface WithMockMarsLadderUser {
    String name() default "id";
    String email() default "email";
    String[] roles() default {"REALUS_STUDENT"};
}