package com.firstedu.marsladder.diting.grade.controller;

import com.firstedu.marsladder.diting.grade.service.GradeService;
import com.firstedu.marsladder.diting.grade.service.domain.Grade;
import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = GradeController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser(roles = {"REALUS_STUDENT", "REALUS_PARENT"})
class GradeControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    GradeService gradeService;

    @Test
    void should_get_all_grades() throws Exception {
        when(gradeService.getGrades()).thenReturn(
                List.of(new Grade("mockGradeId", "year 1"))
        );

        get("/grades")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                          {
                            "id": "mockGradeId",
                            "value": "year 1"
                          }
                        ]
                        """));
    }

    @Test
    void should_get_grades_by_id() throws Exception {
        var mockGradeId = "mockGradeId";
        when(gradeService.getGradeById(mockGradeId)).thenReturn(
                new Grade(mockGradeId, "year 1")
        );

        get("/grades/" + mockGradeId)
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        {
                          "id": "mockGradeId",
                          "value": "year 1"
                        }
                        """));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}