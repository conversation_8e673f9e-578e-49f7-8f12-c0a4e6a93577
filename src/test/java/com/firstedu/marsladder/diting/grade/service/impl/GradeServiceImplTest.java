package com.firstedu.marsladder.diting.grade.service.impl;

import com.firstedu.marsladder.diting.grade.exception.XiaoguanjiaGradeNotFoundException;
import com.firstedu.marsladder.diting.grade.service.domain.Grade;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaGradeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_TIME;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus.ENABLED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GradeServiceImplTest {

    @Mock
    XiaoguanjiaGradeRepository xiaoguanjiaGradeRepository;

    @InjectMocks
    GradeServiceImpl gradeService;

    @Test
    void should_return_all_grades() {
        when(xiaoguanjiaGradeRepository.findAll())
                .thenReturn(List.of(new XiaoguanjiaGradeEntity(
                        "001",
                        "Y6",
                        ENABLED,
                        MOCK_TIME,
                        MOCK_TIME
                )));

        var grades = gradeService.getGrades();

        assertThat(grades).isEqualTo(List.of(
                new Grade("001", "Y6")
        ));
    }

    @Test
    void should_get_grade_by_id() {
        when(xiaoguanjiaGradeRepository.findById("001"))
                .thenReturn(Optional.of(new XiaoguanjiaGradeEntity(
                        "001",
                        "Y6",
                        ENABLED,
                        MOCK_TIME,
                        MOCK_TIME
                )));

        var grade = gradeService.getGradeById("001");

        assertThat(grade).isEqualTo(new Grade("001", "Y6"));
    }

    @Test
    void should_throw_exception_when_grade_not_found() {
        when(xiaoguanjiaGradeRepository.findById("001")).thenReturn(Optional.empty());

        assertThatExceptionOfType(XiaoguanjiaGradeNotFoundException.class)
                .isThrownBy(() -> gradeService.getGradeById("001"));
    }
}