package com.firstedu.marsladder.diting.exception;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.mock.http.client.MockClientHttpResponse;
import org.springframework.web.client.HttpClientErrorException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;

class GlobalExceptionHandlerTest {
    private final GlobalExceptionHandler handler = new GlobalExceptionHandler();

    @Test
    void should_return_400_when_handle_HttpMessageNotReadableException() {
        var result = handler.handleException(new HttpMessageNotReadableException("HttpMessageNotReadableException", new MockClientHttpResponse()));
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo("invalid payload");
    }

    @Test
    void should_return_400_when_handle_BadRequestException() {
        var result = handler.handleException(new BadRequestException("badRequestException"));
        assertThat(result.getStatusCode()).isEqualTo(BAD_REQUEST);
        assertThat(result.getBody()).isEqualTo("badRequestException");
    }

    @Test
    void should_return_500_when_handle_InternalServerErrorException() {
        var result = handler.handleException(new InternalServerErrorException("InternalServerErrorException"));
        assertThat(result.getStatusCode()).isEqualTo(INTERNAL_SERVER_ERROR);
        assertThat(result.getBody()).isEqualTo("InternalServerErrorException");
    }

    @Test
    void should_return_404_when_handle_NotFoundException() {
        var result = handler.handleException(new NotFoundException("NotFoundException"));
        assertThat(result.getStatusCode()).isEqualTo(NOT_FOUND);
        assertThat(result.getBody()).isEqualTo("NotFoundException");
    }

    @Test
    void should_return_http_status_when_handle_HttpClientErrorException() {
        var httpStatus = HttpStatus.BAD_REQUEST;
        var exception = new HttpClientErrorException(httpStatus, "error");
        var result = handler.handleException(exception);
        assertThat(result.getStatusCode()).isEqualTo(httpStatus);
        assertThat(result.getBody()).isEqualTo("400 error");
    }
}
