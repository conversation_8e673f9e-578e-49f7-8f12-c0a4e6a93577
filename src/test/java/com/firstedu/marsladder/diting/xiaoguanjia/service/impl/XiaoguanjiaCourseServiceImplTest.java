package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaCourseNotExistException;
import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfCourses;
import com.firstedu.marsladder.diting.xiaoguanjia.event.XiaoguanjiaEventEntityEventHandler;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.CourseUpdatedOrCreatedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.CourseRemovedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseTeacherRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCoursesCommand;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.ERROR_CODE;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.ERR_MSG_NO_ERR;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.MOCK_COURSE_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.MOCK_COURSE_2;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.MOCK_COURSE_STUDENT1;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.MOCK_COURSE_STUDENT2;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.MOCK_COURSE_TEACHER1;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.MOCK_COURSE_TEACHER2;
import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.START_TIME_AFTER;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaCourseServiceImplTest {

    @Mock
    XiaoguanjiaCourseRepository xiaoguanjiaCourseRepository;

    @Mock
    XiaoguanjiaCourseTeacherRepository xiaoguanjiaCourseTeacherRepository;

    @Mock
    XiaoguanjiaCourseStudentRepository xiaoguanjiaCourseStudentRepository;

    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @InjectMocks
    XiaoguanjiaCourseServiceImpl service;

    @Nested
    class SyncCourses {
        @Test
        void should_get_and_persist_xiaoguanjia_course_to_db() {
            SyncCoursesCommand command = new SyncCoursesCommand(START_TIME_AFTER, 2);
            PageOfCourses page = new PageOfCourses(0, List.of(MOCK_COURSE_1), ERROR_CODE, ERR_MSG_NO_ERR);

            when(xiaoguanjiaClient.getPageOfCourses(START_TIME_AFTER, 2, 1)).thenReturn(page);
            when(xiaoguanjiaClient.fetchCourseTeachers("courseId1")).thenReturn(List.of(MOCK_COURSE_TEACHER1, MOCK_COURSE_TEACHER2));
            when(xiaoguanjiaClient.fetchCourseStudents("courseId1")).thenReturn(List.of(MOCK_COURSE_STUDENT1, MOCK_COURSE_STUDENT2));

            service.syncCourses(command);

            verify(xiaoguanjiaCourseRepository).save(MOCK_COURSE_1.toEntity());
            verify(xiaoguanjiaCourseTeacherRepository).deleteByCourseId("courseId1");
            verify(xiaoguanjiaCourseTeacherRepository).save(MOCK_COURSE_TEACHER1.toEntity("courseId1"));
            verify(xiaoguanjiaCourseTeacherRepository).save(MOCK_COURSE_TEACHER2.toEntity("courseId1"));

            verify(xiaoguanjiaCourseStudentRepository).deleteByCourseId("courseId1");
            verify(xiaoguanjiaCourseStudentRepository).save(MOCK_COURSE_STUDENT1.toEntity("courseId1"));
            verify(xiaoguanjiaCourseStudentRepository).save(MOCK_COURSE_STUDENT2.toEntity("courseId1"));
        }

        @Test
        void should_get_and_save_all_courses_when_there_are_more_than_one_page_of_courses() {
            SyncCoursesCommand command = new SyncCoursesCommand(START_TIME_AFTER, 1);
            var page1 = new PageOfCourses(1, List.of(MOCK_COURSE_1), ERROR_CODE, ERR_MSG_NO_ERR);
            var page2 = new PageOfCourses(0, List.of(MOCK_COURSE_2), ERROR_CODE, ERR_MSG_NO_ERR);
            when(xiaoguanjiaClient.getPageOfCourses(START_TIME_AFTER, 1, 1)).thenReturn(page1);
            when(xiaoguanjiaClient.getPageOfCourses(START_TIME_AFTER, 1, 2)).thenReturn(page2);
            when(xiaoguanjiaClient.fetchCourseTeachers("courseId1")).thenReturn(List.of(MOCK_COURSE_TEACHER1));
            when(xiaoguanjiaClient.fetchCourseTeachers("courseId2")).thenReturn(List.of(MOCK_COURSE_TEACHER2));
            when(xiaoguanjiaClient.fetchCourseStudents("courseId1")).thenReturn(List.of(MOCK_COURSE_STUDENT1));
            when(xiaoguanjiaClient.fetchCourseStudents("courseId2")).thenReturn(List.of(MOCK_COURSE_STUDENT2));

            service.syncCourses(command);

            verify(xiaoguanjiaClient).getPageOfCourses(START_TIME_AFTER, 1, 1);
            verify(xiaoguanjiaCourseRepository).save(MOCK_COURSE_1.toEntity());

            verify(xiaoguanjiaClient).getPageOfCourses(START_TIME_AFTER, 1, 2);
            verify(xiaoguanjiaCourseRepository).save(MOCK_COURSE_2.toEntity());

            verify(xiaoguanjiaCourseTeacherRepository).deleteByCourseId("courseId1");
            verify(xiaoguanjiaCourseTeacherRepository).deleteByCourseId("courseId2");
            verify(xiaoguanjiaCourseTeacherRepository).save(MOCK_COURSE_TEACHER1.toEntity("courseId1"));
            verify(xiaoguanjiaCourseTeacherRepository).save(MOCK_COURSE_TEACHER2.toEntity("courseId2"));

            verify(xiaoguanjiaCourseStudentRepository).deleteByCourseId("courseId1");
            verify(xiaoguanjiaCourseStudentRepository).deleteByCourseId("courseId2");
            verify(xiaoguanjiaCourseStudentRepository).save(MOCK_COURSE_STUDENT1.toEntity("courseId1"));
            verify(xiaoguanjiaCourseStudentRepository).save(MOCK_COURSE_STUDENT2.toEntity("courseId2"));
            verifyNoMoreInteractions(xiaoguanjiaCourseRepository);
        }
    }

    @Nested
    class Events {
        @Nested
        class HandleCourseUpdatedOrCreatedEvent {
            @Test
            void should_handle_course_created_event() {
                var courseCreatedEvent = new CourseUpdatedOrCreatedEvent(XiaoguanjiaEventEntityEventHandler.class, "courseId1");
                when(xiaoguanjiaClient.fetchCourse("courseId1"))
                        .thenReturn(MOCK_COURSE_1);
                when(xiaoguanjiaClient.fetchCourseTeachers("courseId1")).thenReturn(List.of(MOCK_COURSE_TEACHER1, MOCK_COURSE_TEACHER2));
                when(xiaoguanjiaClient.fetchCourseStudents("courseId1")).thenReturn(List.of(MOCK_COURSE_STUDENT1, MOCK_COURSE_STUDENT2));

                service.handleCourseUpdatedOrCreatedEvent(courseCreatedEvent);

                verify(xiaoguanjiaClient).fetchCourse(courseCreatedEvent.getCourseId());
                verify(xiaoguanjiaCourseRepository).save(MOCK_COURSE_1.toEntity());
                verify(xiaoguanjiaCourseTeacherRepository).deleteByCourseId("courseId1");
                verify(xiaoguanjiaCourseTeacherRepository).save(MOCK_COURSE_TEACHER1.toEntity("courseId1"));
                verify(xiaoguanjiaCourseTeacherRepository).save(MOCK_COURSE_TEACHER2.toEntity("courseId1"));

                verify(xiaoguanjiaCourseStudentRepository).deleteByCourseId("courseId1");
                verify(xiaoguanjiaCourseStudentRepository).save(MOCK_COURSE_STUDENT1.toEntity("courseId1"));
                verify(xiaoguanjiaCourseStudentRepository).save(MOCK_COURSE_STUDENT2.toEntity("courseId1"));
            }
        }

        @Nested
        class HandleCourseRemovedEvent {
            @Test
            void should_handle_course_removed_event() {
                var courseRemovedEvent = new CourseRemovedEvent(XiaoguanjiaEventEntityEventHandler.class, "courseId1");
                var xiaoguanjiaCourseEntity = mock(XiaoguanjiaCourseEntity.class);
                when(xiaoguanjiaCourseRepository.findById("courseId1")).thenReturn(Optional.of(xiaoguanjiaCourseEntity));

                service.handleCourseRemovedEvent(courseRemovedEvent);

                verify(xiaoguanjiaCourseRepository).save(xiaoguanjiaCourseEntity);
            }

            @Test
            void should_ignore_event_when_course_is_removed() {
                var courseRemovedEvent = new CourseRemovedEvent(XiaoguanjiaEventEntityEventHandler.class, "courseId1");
                var xiaoguanjiaCourseEntity = mock(XiaoguanjiaCourseEntity.class);
                when(xiaoguanjiaCourseEntity.isDeleted()).thenReturn(true);
                when(xiaoguanjiaCourseRepository.findById("courseId1")).thenReturn(Optional.of(xiaoguanjiaCourseEntity));

                service.handleCourseRemovedEvent(courseRemovedEvent);

                verify(xiaoguanjiaCourseRepository, never()).save(any());
            }

            @Test
            void should_throw_exception_when_xiaoguanjia_course_not_found() {
                var courseRemovedEvent = new CourseRemovedEvent(XiaoguanjiaEventEntityEventHandler.class, "courseId1");
                when(xiaoguanjiaCourseRepository.findById("courseId1")).thenReturn(Optional.empty());

                assertThatExceptionOfType(XiaoguanjiaCourseNotExistException.class).isThrownBy(
                        () -> service.handleCourseRemovedEvent(courseRemovedEvent)
                );

                verify(xiaoguanjiaCourseRepository, never()).save(any());
                verify(xiaoguanjiaClient, never()).fetchCourse(any());
            }
        }
    }
}