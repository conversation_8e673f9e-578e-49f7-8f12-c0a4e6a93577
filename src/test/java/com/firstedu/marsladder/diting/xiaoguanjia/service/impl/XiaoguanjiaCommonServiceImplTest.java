package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEventRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEvent;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaCommonServiceImplTest {
    @Mock
    XiaoguanjiaClient client;

    @Mock
    XiaoguanjiaEventRepository repository;

    @InjectMocks
    XiaoguanjiaCommonServiceImpl service;

    @Test
    void should_get_access_token_from_xiaoguanjia_client() {
        String expectedToken = "mockAccessToken";
        when(client.getAccessToken()).thenReturn(expectedToken);

        String actualToken = service.getAccessToken();

        assertEquals(expectedToken, actualToken);
        verify(client, times(1)).getAccessToken();
    }

    @Test
    void should_save_xiaoguanjia_event() {
        XiaoguanjiaEvent event = XiaoguanjiaEvent.builder()
                        .eventKey("eventKey")
                        .eventId("eventId")
                        .eventTime("eventTime")
                        .appId("appId")
                        .companyId("companayId").build();
        service.saveEvent(event);

        verify(repository, times(1)).save(event.toEntity());
    }
}