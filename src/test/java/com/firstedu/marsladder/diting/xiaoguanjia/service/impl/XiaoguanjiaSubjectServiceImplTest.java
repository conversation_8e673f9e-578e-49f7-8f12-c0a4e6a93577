package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DictInfo;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaGradeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaSubjectRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;
import java.util.List;

import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus.ENABLED;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
class XiaoguanjiaSubjectServiceImplTest {
    @Mock
    private XiaoguanjiaClient xiaoguanjiaClient;

    @Mock
    private XiaoguanjiaSubjectRepository subjectRepository;

    @Mock
    private XiaoguanjiaGradeRepository xiaoguanjiaGradeRepository;

    @InjectMocks
    private XiaoguanjiaSubjectServiceImpl xiaoguanjiaSubjectService;

    @Test
    void should_sync_subjects() {
        DictInfo dictInfo = new DictInfo(
                "1bcf14e6-fd35-4a68-b694-b55d4bcb0e08",
                "20180221110118",
                "20180221110118",
                "生物",
                "",
                0,
                "",
                1
        );
        when(xiaoguanjiaClient.fetchDict("SHIFT_SUBJECT")).thenReturn(List.of(dictInfo));

        xiaoguanjiaSubjectService.syncSubjects();

        verify(xiaoguanjiaClient).fetchDict("SHIFT_SUBJECT");
        verify(subjectRepository).save(new XiaoguanjiaSubjectEntity(
                "1bcf14e6-fd35-4a68-b694-b55d4bcb0e08",
                "生物",
                ENABLED,
                LocalDateTime.of(2018, 2, 21, 3, 1, 18),
                LocalDateTime.of(2018, 2, 21, 3, 1, 18)
        ));
    }

    @Test
    void should_sync_grades() {
        DictInfo dictInfo = new DictInfo(
                "1bcf14e6-fd35-4a68-b694-b55d4bcb0e08",
                "20180221110118",
                "20180221110118",
                "y9",
                "",
                0,
                "",
                1
        );
        when(xiaoguanjiaClient.fetchDict("SHIFT_GRADE")).thenReturn(List.of(dictInfo));

        xiaoguanjiaSubjectService.syncGrades();

        verify(xiaoguanjiaClient).fetchDict("SHIFT_GRADE");
        verify(xiaoguanjiaGradeRepository).save(new XiaoguanjiaGradeEntity(
                "1bcf14e6-fd35-4a68-b694-b55d4bcb0e08",
                "y9",
                ENABLED,
                LocalDateTime.of(2018, 2, 21, 3, 1, 18),
                LocalDateTime.of(2018, 2, 21, 3, 1, 18)
        ));
    }
}