package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfClasses;
import com.firstedu.marsladder.diting.xiaoguanjia.event.XiaoguanjiaEventEntityEventHandler;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ClassCreatedOrUpdatedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ClassRemovedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassStudentService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncClassesCommand;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.*;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatExceptionOfType;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaClassServiceImplTest {
    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @Mock
    XiaoguanjiaClassRepository xiaoguanjiaClassRepository;

    @Mock
    XiaoguanjiaClassTeacherServiceImpl xiaoguanjiaClassTeacherService;

    @Mock
    XiaoguanjiaClassStudentService xiaoguanjiaClassStudentService;

    @InjectMocks
    XiaoguanjiaClassServiceImpl xiaoguanjiaClassService;

    @Nested
    class SyncClasses {
        SyncClassesCommand syncClassroomCommand = new SyncClassesCommand(CREATED_AFTER, PAGE_SIZE);

        @Test
        void should_get_and_persist_xiaoguanjia_class_to_db() {
            PageOfClasses page = new PageOfClasses(0, List.of(MOCK_CLASS_1), ERROR_CODE, ERR_MSG_NO_ERR);
            when(xiaoguanjiaClient.getPageOfClasses(CREATED_AFTER, PAGE_SIZE, 1)).thenReturn(page);

            xiaoguanjiaClassService.syncClasses(syncClassroomCommand);

            verify(xiaoguanjiaClassRepository).save(MOCK_CLASS_1.toEntity());
            verify(xiaoguanjiaClassStudentService).refreshClassStudents(MOCK_CLASS_1.classId());
            verify(xiaoguanjiaClassTeacherService).refreshClassTeachers(MOCK_CLASS_1.classId());
        }

        @Test
        void should_get_and_save_all_classes_when_there_are_more_than_one_page_of_classes() {
            var page1 = new PageOfClasses(1, List.of(MOCK_CLASS_1, MOCK_CLASS_2), ERROR_CODE, ERR_MSG_NO_ERR);
            var page2 = new PageOfClasses(0, List.of(MOCK_CLASS_3), ERROR_CODE, ERR_MSG_NO_ERR);
            when(xiaoguanjiaClient.getPageOfClasses(CREATED_AFTER, PAGE_SIZE, 1)).thenReturn(page1);
            when(xiaoguanjiaClient.getPageOfClasses(CREATED_AFTER, PAGE_SIZE, 2)).thenReturn(page2);

            xiaoguanjiaClassService.syncClasses(syncClassroomCommand);

            verify(xiaoguanjiaClient).getPageOfClasses(CREATED_AFTER, PAGE_SIZE, 1);
            verify(xiaoguanjiaClassRepository).save(MOCK_CLASS_1.toEntity());
            verify(xiaoguanjiaClassRepository).save(MOCK_CLASS_2.toEntity());
            verify(xiaoguanjiaClassStudentService).refreshClassStudents(MOCK_CLASS_1.classId());
            verify(xiaoguanjiaClassStudentService).refreshClassStudents(MOCK_CLASS_2.classId());
            verify(xiaoguanjiaClassTeacherService).refreshClassTeachers(MOCK_CLASS_1.classId());
            verify(xiaoguanjiaClassTeacherService).refreshClassTeachers(MOCK_CLASS_2.classId());

            verify(xiaoguanjiaClient).getPageOfClasses(CREATED_AFTER, PAGE_SIZE, 2);
            verify(xiaoguanjiaClassRepository).save(MOCK_CLASS_3.toEntity());
            verify(xiaoguanjiaClassStudentService).refreshClassStudents(MOCK_CLASS_3.classId());
            verify(xiaoguanjiaClassTeacherService).refreshClassTeachers(MOCK_CLASS_3.classId());

            verifyNoMoreInteractions(xiaoguanjiaClassRepository);
        }
    }

    @Nested
    class Events {
        @Nested
        class CreatedOrUpdatedEvent {
            @Test
            void should_handle_class_created_or_modified_event() {
                var classCreatedOrModifiedEvent = new ClassCreatedOrUpdatedEvent(XiaoguanjiaEventEntityEventHandler.class, XIAOGUANJIA_CLASS_ID_1);
                when(xiaoguanjiaClient.fetchClass(XIAOGUANJIA_CLASS_ID_1))
                        .thenReturn(MOCK_CLASS_1);

                xiaoguanjiaClassService.handleClassCreatedOrUpdatedEvent(classCreatedOrModifiedEvent);

                verify(xiaoguanjiaClient).fetchClass(classCreatedOrModifiedEvent.getClassId());
                verify(xiaoguanjiaClassRepository).save(MOCK_CLASS_1.toEntity());
                verify(xiaoguanjiaClassStudentService).refreshClassStudents(MOCK_CLASS_1.classId());
                verify(xiaoguanjiaClassTeacherService).refreshClassTeachers(MOCK_CLASS_1.classId());
            }
        }

        @Nested
        class RemovedEvent {
            @Test
            void should_handle_class_removed_event() {
                var classRemovedEvent = new ClassRemovedEvent(XiaoguanjiaEventEntityEventHandler.class, XIAOGUANJIA_CLASS_ID_1);
                var xiaoguanjiaClassEntity = XiaoguanjiaClassEntity.builder().deleted(false).build();
                when(xiaoguanjiaClassRepository.findById(XIAOGUANJIA_CLASS_ID_1)).thenReturn(Optional.of(xiaoguanjiaClassEntity));

                xiaoguanjiaClassService.handleClassRemovedEvent(classRemovedEvent);

                verify(xiaoguanjiaClassRepository).save(XiaoguanjiaClassEntity.builder().deleted(true).build());
            }

            @Test
            void should_ignore_evnet_when_class_is_removed() {
                var classRemovedEvent = new ClassRemovedEvent(XiaoguanjiaEventEntityEventHandler.class, XIAOGUANJIA_CLASS_ID_1);
                var xiaoguanjiaClassEntity = XiaoguanjiaClassEntity.builder().deleted(true).build();
                when(xiaoguanjiaClassRepository.findById(XIAOGUANJIA_CLASS_ID_1)).thenReturn(Optional.of(xiaoguanjiaClassEntity));

                xiaoguanjiaClassService.handleClassRemovedEvent(classRemovedEvent);

                verify(xiaoguanjiaClassRepository, never()).save(any());
            }

            @Test
            void should_throw_exception_when_xiaoguanjia_class_not_found() {
                var classRemovedEvent = new ClassRemovedEvent(XiaoguanjiaEventEntityEventHandler.class, XIAOGUANJIA_CLASS_ID_1);
                when(xiaoguanjiaClassRepository.findById(XIAOGUANJIA_CLASS_ID_1)).thenReturn(Optional.empty());

                assertThatExceptionOfType(XiaoguanjiaClassNotExistException.class).isThrownBy(
                        () -> xiaoguanjiaClassService.handleClassRemovedEvent(classRemovedEvent)
                );

                verify(xiaoguanjiaClassRepository, never()).save(any());
            }
        }
    }
}
