package com.firstedu.marsladder.diting.xiaoguanjia;

import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassInfo;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class ClassStubs {
    public static final LocalDate CREATED_AFTER = LocalDate.of(2024, 1, 1);
    public static final int PAGE_SIZE = 2;

    public static final String XIAOGUANJIA_CLASS_ID_1 = "fb6c27f9-3f60-44fd-a28e-6f17c67caa16";
    public static final String XIAOGUANJIA_CLASS_ID_2 = "66f275fd-d959-4980-9330-a270f4c8b3cb";
    public static final String XIAOGUANJIA_CLASS_ID_3 = "251a3cf7-a099-426e-af9b-2139c60ab917";
    public static final String XIAOGUANJIA_CLASS_NAME_1 = "Y1-Y6精品一对一 (2024)_Enoch Lian";
    public static final String XIAOGUANJIA_CLASS_NAME_2 = "Y1-Y6精品英文一对一 (2024)_<PERSON> (Sijia）";
    public static final String XIAOGUANJIA_CLASS_NAME_3 = "VCE精品一对一(2024)_Shengjie Tong(Jacky)网课";

    public static final String XIAOGUANJIA_CLASS_1_HEADMASTER_ID = "00000000-0000-0000-0000-000000000000";

    public static final String CREATETIME = "20240220072554";
    public static final String UPDATETIME = "20240220072554";

    public static final ClassInfo MOCK_CLASS_1 = new ClassInfo(
            "fb6c27f9-3f60-44fd-a28e-6f17c67caa16",
            "Y1-Y6精品一对一 (2024)_Enoch Lian",
            "20240220072554",
            "20240801151325",
            "00000000-0000-0000-0000-000000000000",
            "00000000-0000-0000-0000-000000000000",
            "7f45b7df-0a7b-44b4-9c01-87d622b0500b",
            "d9d700db-1805-4f36-a967-686303852a6e",
            1,
            "20240220072554",
            0,
            "00010101000000",
            "周日10:00~12:00",
            "00000000-0000-0000-0000-000000000000",
            "3b56be36-abf5-46a9-a4c3-256ee875f9d0",
            1,
            "00010101000000",
            0.00f,
            "00000000-0000-0000-0000-000000000000",
            "",
            0,
            0,
            "",
            0,
            1,
            1,
            "",
            "20241108180000",
            "20240225100000"
    );

    public static final ClassInfo MOCK_CLASS_2 = new ClassInfo(
            "66f275fd-d959-4980-9330-a270f4c8b3cb",
            "Y1-Y6精品英文一对一 (2024)_Sophia Wang (Sijia）",
            "20240731163014",
            "20240804143237",
            "00000000-0000-0000-0000-000000000000",
            "00000000-0000-0000-0000-000000000000",
            "7f45b7df-0a7b-44b4-9c01-87d622b0500b",
            "d9d700db-1805-4f36-a967-686303852a6e",
            1,
            "20240731000000",
            0,
            "00010101000000",
            "周二19:00~20:00",
            "00000000-0000-0000-0000-000000000000",
            "3a037a02-6a1c-4711-810a-3ee70c0bcbd6",
            1,
            "00010101000000",
            0.00f,
            "00000000-0000-0000-0000-000000000000",
            "",
            0,
            0,
            "",
            0,
            1,
            1,
            "",
            "20240829194000",
            "20240801163000"
    );
    
    public static final ClassInfo MOCK_CLASS_3 = new ClassInfo(
            "251a3cf7-a099-426e-af9b-2139c60ab917",
            "VCE精品一对一(2024)_Shengjie Tong(Jacky)网课",
            "20240807081825",
            "20240807082151",
            "00000000-0000-0000-0000-000000000000",
            "00000000-0000-0000-0000-000000000000",
            "7f45b7df-0a7b-44b4-9c01-87d622b0500b",
            "a7a8c084-391b-4ad9-a624-e82934f8db3a",
            1,
            "20240807081825",
            0,
            "00010101000000",
            "周三17:00~19:00",
            "00000000-0000-0000-0000-000000000000",
            "3a037a02-6a1c-4711-810a-3ee70c0bcbd6",
            1,
            "00010101000000",
            0.00f,
            "00000000-0000-0000-0000-000000000000",
            "",
            0,
            0,
            "",
            0,
            1,
            1,
            "",
            "20240814190000",
            "20240814170000"
    );
    
    public static final int ERROR_CODE = 0;
    public static final String ERR_MSG_NO_ERR = "";

    public static final LocalDateTime MOCK_TIME = LocalDateTime.of(2024, 12, 23, 0, 0, 0);
}
