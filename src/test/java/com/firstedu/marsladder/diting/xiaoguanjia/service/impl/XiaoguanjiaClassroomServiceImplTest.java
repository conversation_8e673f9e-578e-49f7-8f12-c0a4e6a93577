package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfClassrooms;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassroomRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncClassroomsCommand;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassroomStubs.MOCK_CLASSROOM1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassroomStubs.MOCK_CLASSROOM2;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassroomStubs.UPDATE_TIME_AFTER;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaClassroomServiceImplTest {

    @Mock
    XiaoguanjiaClassroomRepository xiaoguanjiaClassroomRepository;

    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @InjectMocks
    XiaoguanjiaClassroomServiceImpl service;

    @Nested
    class SyncClassrooms {
        @Test
        void should_get_and_persist_xiaoguanjia_classroom_to_db() {
            var command = new SyncClassroomsCommand(UPDATE_TIME_AFTER, 1);
            var page1 = new PageOfClassrooms(1, List.of(MOCK_CLASSROOM1), 0, "");
            var page2 = new PageOfClassrooms(0, List.of(MOCK_CLASSROOM2), 0, "");
            when(xiaoguanjiaClient.getPageOfClassrooms(UPDATE_TIME_AFTER, 1, 1)).thenReturn(page1);
            when(xiaoguanjiaClient.getPageOfClassrooms(UPDATE_TIME_AFTER, 1, 2)).thenReturn(page2);

            service.syncClassrooms(command);

            verify(xiaoguanjiaClient).getPageOfClassrooms(UPDATE_TIME_AFTER, 1, 1);
            verify(xiaoguanjiaClassroomRepository).save(MOCK_CLASSROOM1.toEntity());

            verify(xiaoguanjiaClient).getPageOfClassrooms(UPDATE_TIME_AFTER, 1, 2);
            verify(xiaoguanjiaClassroomRepository).save(MOCK_CLASSROOM2.toEntity());

            verifyNoMoreInteractions(xiaoguanjiaClassroomRepository);
        }
    }
}