package com.firstedu.marsladder.diting.xiaoguanjia.integration;

import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(properties = "spring.flyway.clean-disabled=false")
@AutoConfigureMockMvc
@WithMockMarsLadderUser
@AutoConfigureWireMock
public class SyncIntegrationTest {

    @Autowired
    MockMvc mockMvc;

    @Autowired
    Flyway flyway;

    @MockBean
    FalconClient falconClient;

    @AfterEach
    void tearDown() {
        flyway.clean();
        flyway.migrate();
    }

    @Test
    void should_sync_and_get_classes() throws Exception {
        when(falconClient.getUser()).thenReturn(UserDto.builder().roles(List.of(UserRole.TUTOR)).build());
        post("/classes/sync", """
                {
                   "createdAfter": "2024-10-01",
                   "pageSize": 2
                }
                """)
                .andExpect(status().isOk());

        get("/classes")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                            {
                                "id": "251a3cf7-a099-426e-af9b-2139c60ab917",
                                "name": "VCE精品一对一(2024)_Shengjie Tong(Jacky)网课"
                            },
                            {
                                "id": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                                "name": "Y1-Y6精品英文一对一 (2024)_Sophia Wang (Sijia）"
                            },
                            {
                                "id": "fb6c27f9-3f60-44fd-a28e-6f17c67caa16",
                                "name": "Y1-Y6精品一对一 (2024)_Enoch Lian"
                            }
                        ]"""
                ));
    }

    @Test
    void should_sync_shifts() throws Exception {
        post("/shifts/sync", """
                {
                   "updatedAfter": "2024-10-01",
                   "pageSize": 2
                }
                """)
                .andExpect(status().isOk());
    }

    @Test
    void should_sync_courses() throws Exception {
        post("/courses/sync", """
                {
                  "startTimeAfter": "2024-01-01",
                  "pageSize": 100
                }""")
                .andExpect(status().isOk());
    }

    @Test
    void should_sync_subjects() throws Exception {
        post("/subjects/sync", "")
                .andExpect(status().isOk());

        get("/subjects")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                          {
                            "id": "41b558a3-77dc-480b-bfed-00eefb86bd02",
                            "name": "ENGLISH"
                          },
                          {
                            "id": "850a3961-0461-4524-b7d5-56a00cef1101",
                            "name": "MATHS"
                          }
                        ]"""));
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}
