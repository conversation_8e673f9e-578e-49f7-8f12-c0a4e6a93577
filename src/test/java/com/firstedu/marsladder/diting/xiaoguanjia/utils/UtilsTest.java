package com.firstedu.marsladder.diting.xiaoguanjia.utils;

import com.firstedu.marsladder.diting.exception.InternalServerErrorException;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

class UtilsTest {

    @Test
    void should_convert_xiaoguanjia_timestamp_string_to_utc_local_datetime() {
        var beijingDateTime = "20241206143459";
        var utcTime = fromBJTimeToUTC(beijingDateTime);

        assertThat(utcTime).isEqualTo(LocalDateTime.of(2024, 12, 6, 6, 34, 59));
    }

    @Test
    void should_convert_date_to_utc_datetime() {
        var beijingDate = "20241219";
        var utcTime = fromBJTimeToUTC(beijingDate);
        assertThat(utcTime).isEqualTo(LocalDateTime.of(2024, 12, 19, 4, 0, 0));
    }

    @Test
    void should_throw_exception_when_unexpected_format_of_timestamp_receieved_from_xiaoguanjia() {
        assertThatExceptionOfType(InternalServerErrorException.class).isThrownBy(
                () -> fromBJTimeToUTC("202412")
        );
    }

}