package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfEmployees;
import com.firstedu.marsladder.diting.employee.exception.XiaoguanjiaEmployeeNotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.event.EmployeeAction;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.EmployeeActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEmployeeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncEmployeesCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEmployeeStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.EmployeeStubs.*;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaEmployeeServiceImplTest {
    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @Mock
    XiaoguanjiaEmployeeRepository xiaoguanjiaEmployeeRepository;

    @InjectMocks
    XiaoguanjiaEmployeeServiceImpl xiaoguanjiaEmployeeService;

    @Nested
    class SyncEmployees {
        SyncEmployeesCommand syncEmployeesCommand = new SyncEmployeesCommand(START_TIME_AFTER, PAGE_SIZE);

        @Test
        void should_get_and_save_all_employees_when_there_are_more_than_one_page_of_employees() {
            var page1 = new PageOfEmployees(1, List.of(MOCK_EMPLOYEE_1), ERROR_CODE, ERR_MSG_NO_ERR);
            var page2 = new PageOfEmployees(0, List.of(MOCK_EMPLOYEE_2), ERROR_CODE, ERR_MSG_NO_ERR);
            when(xiaoguanjiaClient.getPageOfEmployees(START_TIME_AFTER, PAGE_SIZE, 1)).thenReturn(page1);
            when(xiaoguanjiaClient.getPageOfEmployees(START_TIME_AFTER, PAGE_SIZE, 2)).thenReturn(page2);

            xiaoguanjiaEmployeeService.syncEmployee(syncEmployeesCommand);

            verify(xiaoguanjiaClient).getPageOfEmployees(START_TIME_AFTER, PAGE_SIZE, 1);
            verify(xiaoguanjiaEmployeeRepository).save(MOCK_EMPLOYEE_1.toEntity());

            verify(xiaoguanjiaClient).getPageOfEmployees(START_TIME_AFTER, PAGE_SIZE, 2);
            verify(xiaoguanjiaEmployeeRepository).save(MOCK_EMPLOYEE_2.toEntity());
        }
    }

    @Nested
    class Events {
        @Test
        void should_save_xiaoguanjia_employee_when_receive_created_event() {
            EmployeeActionEvent actionEvent = new EmployeeActionEvent("", "employeeId", EmployeeAction.CREATE);
            when(xiaoguanjiaEmployeeRepository.existsById("employeeId")).thenReturn(false);
            when(xiaoguanjiaClient.fetchEmployee("employeeId"))
                    .thenReturn(MOCK_EMPLOYEE_1);

            xiaoguanjiaEmployeeService.handleEmployeeStudentEvent(actionEvent);
            verify(xiaoguanjiaEmployeeRepository).save(MOCK_EMPLOYEE_1.toEntity("employeeId"));
        }

        @Test
        void should_save_xiaoguanjia_employee_when_receive_created_event_and_entity_existed() {
            EmployeeActionEvent actionEvent = new EmployeeActionEvent("", "employeeId", EmployeeAction.CREATE);
            when(xiaoguanjiaEmployeeRepository.existsById("employeeId")).thenReturn(true);

            xiaoguanjiaEmployeeService.handleEmployeeStudentEvent(actionEvent);
            verify(xiaoguanjiaEmployeeRepository, times(0)).save(any());
        }

        @Test
        void should_save_xiaoguanjia_employee_when_receive_modified_event() {
            EmployeeActionEvent actionEvent = new EmployeeActionEvent("", "employeeId", EmployeeAction.MODIFY);
            when(xiaoguanjiaEmployeeRepository.findById("employeeId")).thenReturn(Optional.of(MOCK_EMPLOYEE_1.toEntity()));
            when(xiaoguanjiaClient.fetchEmployee("employeeId"))
                    .thenReturn(MOCK_EMPLOYEE_2);

            xiaoguanjiaEmployeeService.handleEmployeeStudentEvent(actionEvent);
            verify(xiaoguanjiaEmployeeRepository).save(MOCK_EMPLOYEE_2.toEntity("employeeId"));
        }

        @Test
        void should_save_xiaoguanjia_employee_when_receive_removed_event() {
            EmployeeActionEvent actionEvent = new EmployeeActionEvent("", "employeeId", EmployeeAction.REMOVE);
            var existedEntity = MOCK_EMPLOYEE_1.toEntity();
            when(xiaoguanjiaEmployeeRepository.findById("employeeId")).thenReturn(Optional.of(existedEntity));

            xiaoguanjiaEmployeeService.handleEmployeeStudentEvent(actionEvent);
            existedEntity.setStatus(XiaoguanjiaEmployeeStatus.DELETED);
            verify(xiaoguanjiaEmployeeRepository).save(existedEntity);
        }

        @Test
        void should_throw_exception_when_receive_removed_event() {
            EmployeeActionEvent actionEvent = new EmployeeActionEvent("", "employeeId", EmployeeAction.REMOVE);
            when(xiaoguanjiaEmployeeRepository.findById("employeeId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(XiaoguanjiaEmployeeNotFoundException.class).isThrownBy(
                    () -> xiaoguanjiaEmployeeService.handleEmployeeStudentEvent(actionEvent)
            );
        }
    }

}