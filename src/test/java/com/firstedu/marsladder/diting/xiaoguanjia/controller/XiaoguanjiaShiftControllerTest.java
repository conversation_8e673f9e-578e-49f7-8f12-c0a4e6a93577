package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncShiftsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaShiftService;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.CREATED_AFTER;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.PAGE_SIZE;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = XiaoguanjiaShiftController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser
class XiaoguanjiaShiftControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private XiaoguanjiaShiftService xiaoguanjiaShiftService;

    @Nested
    class SyncShifts {
        @Test
        void should_return_200_when_syncing_shift_data_from_xiaoguanjia() throws Exception {
            var request = """
                    {
                        "updatedAfter": "2024-01-01",
                        "pageSize": 2
                    }
                    """;
            var syncRequest = new SyncShiftsRequest(CREATED_AFTER, PAGE_SIZE);
            var syncCommand = syncRequest.toSyncShiftsCommand();
            doNothing().when(xiaoguanjiaShiftService).syncShifts(syncCommand);

            post("/shifts/sync", request)
                    .andExpect(status().isOk());

            verify(xiaoguanjiaShiftService).syncShifts(syncCommand);
        }
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}