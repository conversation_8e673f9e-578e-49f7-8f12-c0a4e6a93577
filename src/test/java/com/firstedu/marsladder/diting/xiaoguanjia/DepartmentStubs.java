package com.firstedu.marsladder.diting.xiaoguanjia;

import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DepartmentInfo;

import java.time.LocalDate;

public class DepartmentStubs {
    public static final LocalDate UPDATE_TIME_AFTER = LocalDate.of(2024, 1, 1);

    public static final DepartmentInfo MOCK_DEPARTMENT1 = new DepartmentInfo(
            "D001",
            "Computer Science",
            true,
            1,
            "CS",
            "20241116103808",
            "20241116103808"
    );

    public static final DepartmentInfo MOCK_DEPARTMENT2 = new DepartmentInfo(
            "D002",
            "Mathematics",
            false,
            0,
            "Math",
            "20241116103808",
            "20241116103808"
    );
}
