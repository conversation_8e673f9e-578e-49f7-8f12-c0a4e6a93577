package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassStudentListElement;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseStudentInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentCourseIdInfo;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ClassStudentModifiedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.any;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaClassStudentServiceImplTest {
    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @Mock
    XiaoguanjiaClassStudentRepository xiaoguanjiaClassStudentRepository;

    @Mock
    XiaoguanjiaCourseStudentRepository xiaoguanjiaCourseStudentRepository;

    @Mock
    XiaoguanjiaCourseRepository xiaoguanjiaCourseRepository;

    @InjectMocks
    XiaoguanjiaClassStudentServiceImpl xiaoguanjiaClassStudentService;

    @Nested
    class Events {
        @Test
        void should_save_xiaoguanjia_student_when_receive_modified_event() {
            var classId = "classId";
            var event = new ClassStudentModifiedEvent("", classId);
            var classStudentListElement = new ClassStudentListElement(
                    "id", "studentId", 1, "20240101", "20240101", "001", "", "", "20240102", "20240102"
            );

            when(xiaoguanjiaClient.getClassStudents(classId)).thenReturn(
                    Collections.singletonList(classStudentListElement)
            );

            xiaoguanjiaClassStudentService.handleClassStudentModifiedEvent(event);
            verify(xiaoguanjiaClassStudentRepository).saveAll(List.of(classStudentListElement.toXiaoguanjiaClassStudent(classId).toEntity()));
            verify(xiaoguanjiaClassStudentRepository).deleteByClassId(classId);
        }

        @Test
        void should_sync_student_courses_when_new_student_created_after_max() {
            var classId = "classId";
            var event = new ClassStudentModifiedEvent("", classId);
            var oldStudent = XiaoguanjiaClassStudentEntity.builder()
                    .studentId("studentId1").createdAt(LocalDateTime.parse("2024-01-30T10:04:39")).status(ClassStudentStatus.ENTERED).build();
            when(xiaoguanjiaClassStudentRepository.findByClassId(classId)).thenReturn(List.of(oldStudent));
            when(xiaoguanjiaClient.getClassStudents(classId)).thenReturn(List.of(buildClassStudentListElement("studentId2", "20250131100439")));
            var courseIdInfo = new StudentCourseIdInfo("courseId2");
            when(xiaoguanjiaClient.fetchStudentCourseIds("studentId2")).thenReturn(List.of(courseIdInfo));
            var courseStudent = new CourseStudentInfo(
                "studentId2", 1, 100.0, null, "Yang", "20240101"
            );
            when(xiaoguanjiaClient.fetchCourseStudents("courseId2")).thenReturn(List.of(courseStudent));
            var courseEntity = XiaoguanjiaCourseEntity.builder().id("courseId2").classId(classId).build();
            when(xiaoguanjiaCourseRepository.findByClassId(classId)).thenReturn(List.of(courseEntity));

            xiaoguanjiaClassStudentService.handleClassStudentModifiedEvent(event);
            verify(xiaoguanjiaClassStudentRepository).deleteByClassId(classId);
            verify(xiaoguanjiaClassStudentRepository).saveAll(List.of(buildClassStudentListElement("studentId2", "20250131100439").toXiaoguanjiaClassStudent(classId).toEntity()));
            verify(xiaoguanjiaCourseStudentRepository).deleteByStudentIdAndCourseIdIn("studentId2", List.of("courseId2"));
            verify(xiaoguanjiaCourseStudentRepository).save(courseStudent.toEntity("courseId2"));
        }

        @Test
        void should_not_sync_courses_when_created_time_not_after_max() {
            var classId = "classId";
            var event = new ClassStudentModifiedEvent("", classId);
            var oldStudent = XiaoguanjiaClassStudentEntity.builder()
                    .studentId("studentId1").createdAt(LocalDateTime.parse("2024-01-30T10:04:39")).status(ClassStudentStatus.ENTERED).build();
            when(xiaoguanjiaClassStudentRepository.findByClassId(classId)).thenReturn(List.of(oldStudent));
            var earlierStudent = new ClassStudentListElement(
                    "id2", "studentId2", 1, "20250130100439", "20250130100439", "001", "", "", "20240129100439", "20240129100439"
            );
            when(xiaoguanjiaClient.getClassStudents(classId)).thenReturn(List.of(earlierStudent));

            xiaoguanjiaClassStudentService.handleClassStudentModifiedEvent(event);
            verify(xiaoguanjiaClient, never()).fetchStudentCourseIds(any());
        }

        @Test
        void should_only_save_matching_student_course() {
            var classId = "classId";
            var event = new ClassStudentModifiedEvent("", classId);
            var oldStudent = XiaoguanjiaClassStudentEntity.builder()
                    .studentId("studentId1").createdAt(LocalDateTime.parse("2024-01-30T10:04:39")).status(ClassStudentStatus.ENTERED).build();
            when(xiaoguanjiaClassStudentRepository.findByClassId(classId)).thenReturn(List.of(oldStudent));
            when(xiaoguanjiaClient.getClassStudents(classId)).thenReturn(List.of(buildClassStudentListElement("studentId2", "20250131100439")));
            var courseIdInfo = new StudentCourseIdInfo("courseId2");
            when(xiaoguanjiaClient.fetchStudentCourseIds("studentId2")).thenReturn(List.of(courseIdInfo));
            var courseEntity = com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseEntity.builder().id("courseId2").classId(classId).build();
            when(xiaoguanjiaCourseRepository.findByClassId(classId)).thenReturn(List.of(courseEntity));
            var matchingCourseStudent = new CourseStudentInfo("studentId2", 1, 100.0, null, "Yang", "20240101");
            var nonMatchingCourseStudent = new CourseStudentInfo("differentStudentId", 1, 90.0, null, "Li", "20240101");
            when(xiaoguanjiaClient.fetchCourseStudents("courseId2")).thenReturn(List.of(matchingCourseStudent, nonMatchingCourseStudent));

            xiaoguanjiaClassStudentService.handleClassStudentModifiedEvent(event);
            verify(xiaoguanjiaCourseStudentRepository).deleteByStudentIdAndCourseIdIn("studentId2", List.of("courseId2"));
            verify(xiaoguanjiaCourseStudentRepository).save(matchingCourseStudent.toEntity("courseId2"));
            verify(xiaoguanjiaCourseStudentRepository, never()).save(nonMatchingCourseStudent.toEntity("courseId2"));
        }

        private ClassStudentListElement buildClassStudentListElement(String studentId, String createTime) {
            return new ClassStudentListElement(
                    "id", 
                    studentId, 
                    1, 
                    "20250130100439", 
                    "20250130100439", 
                    "001", 
                    "", 
                    "", 
                    createTime, 
                    createTime
            );
        }
    }

    @Test
    void should_refresh_class_students() {
        var classId = "classId";
        var classStudentListElement = new ClassStudentListElement(
                "id", "studentId", 1, "20240101", "20240101", "001", "", "", "20240102", "20240102"
        );
        when(xiaoguanjiaClient.getClassStudents(classId)).thenReturn(
                Collections.singletonList(classStudentListElement)
        );

        xiaoguanjiaClassStudentService.refreshClassStudents(classId);

        verify(xiaoguanjiaClassStudentRepository).deleteByClassId(classId);
        verify(xiaoguanjiaClassStudentRepository).saveAll(List.of(classStudentListElement.toXiaoguanjiaClassStudent(classId).toEntity()));
    }
}