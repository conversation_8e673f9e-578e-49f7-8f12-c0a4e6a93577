package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfShifts;
import com.firstedu.marsladder.diting.shift.service.exception.XiaoguanjiaShiftNotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.event.ShiftAction;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ShiftActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaShiftRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncShiftsCommand;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.CREATED_AFTER;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.ERROR_CODE;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.ERR_MSG_NO_ERR;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.PAGE_SIZE;
import static com.firstedu.marsladder.diting.xiaoguanjia.ShiftStubs.MOCK_SHIFT_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ShiftStubs.MOCK_SHIFT_2;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaShiftServiceImplTest {
    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @Mock
    XiaoguanjiaShiftRepository xiaoguanjiaShiftRepository;

    @InjectMocks
    XiaoguanjiaShiftServiceImpl xiaoguanjiaShiftService;

    @Nested
    class SyncShifts {
        SyncShiftsCommand syncShiftsCommand = new SyncShiftsCommand(CREATED_AFTER, PAGE_SIZE);

        @Test
        void should_get_and_persist_xiaoguanjia_shift_to_db() {
            PageOfShifts page = new PageOfShifts(0, List.of(MOCK_SHIFT_1), ERROR_CODE, ERR_MSG_NO_ERR);
            when(xiaoguanjiaClient.getPageOfShifts(CREATED_AFTER, PAGE_SIZE, 1)).thenReturn(page);

            xiaoguanjiaShiftService.syncShifts(syncShiftsCommand);

            verify(xiaoguanjiaShiftRepository).save(MOCK_SHIFT_1.toEntity());
        }
    }

    @Nested
    class Events {
        @Test
        void should_save_xiaoguanjia_shift_when_receive_created_event() {
            ShiftActionEvent actionEvent = new ShiftActionEvent("", "shiftId", ShiftAction.CREATE);
            when(xiaoguanjiaShiftRepository.existsById("shiftId")).thenReturn(false);
            when(xiaoguanjiaClient.fetchShift("shiftId"))
                    .thenReturn(MOCK_SHIFT_1);

            xiaoguanjiaShiftService.handleShiftEvent(actionEvent);
            verify(xiaoguanjiaShiftRepository).save(MOCK_SHIFT_1.toEntity());
        }

        @Test
        void should_save_xiaoguanjia_shift_when_receive_created_event_and_entity_existed() {
            ShiftActionEvent actionEvent = new ShiftActionEvent("", "shiftId", ShiftAction.CREATE);
            when(xiaoguanjiaShiftRepository.existsById("shiftId")).thenReturn(true);

            xiaoguanjiaShiftService.handleShiftEvent(actionEvent);
            verify(xiaoguanjiaShiftRepository, times(0)).save(MOCK_SHIFT_1.toEntity());
        }

        @Test
        void should_save_xiaoguanjia_shift_when_receive_modified_event() {
            ShiftActionEvent actionEvent = new ShiftActionEvent("", "shiftId", ShiftAction.MODIFY);
            when(xiaoguanjiaShiftRepository.findById("shiftId")).thenReturn(Optional.of(MOCK_SHIFT_1.toEntity()));
            when(xiaoguanjiaClient.fetchShift("shiftId"))
                    .thenReturn(MOCK_SHIFT_2);

            xiaoguanjiaShiftService.handleShiftEvent(actionEvent);
            verify(xiaoguanjiaShiftRepository).save(MOCK_SHIFT_2.toEntity());
        }

        @Test
        void should_save_xiaoguanjia_shift_when_receive_removed_event() {
            ShiftActionEvent actionEvent = new ShiftActionEvent("", "shiftId", ShiftAction.REMOVE);
            var existedEntity = MOCK_SHIFT_1.toEntity();
            when(xiaoguanjiaShiftRepository.findById("shiftId")).thenReturn(Optional.of(existedEntity));

            xiaoguanjiaShiftService.handleShiftEvent(actionEvent);
            existedEntity.setStatus(ShiftStatus.DELETED);
            verify(xiaoguanjiaShiftRepository).save(existedEntity);
        }

        @Test
        void should_throw_exception_when_receive_removed_event() {
            ShiftActionEvent actionEvent = new ShiftActionEvent("", "shiftId", ShiftAction.REMOVE);
            when(xiaoguanjiaShiftRepository.findById("shiftId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(XiaoguanjiaShiftNotFoundException.class).isThrownBy(
                    () -> xiaoguanjiaShiftService.handleShiftEvent(actionEvent)
            );
        }
    }
}