package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncDepartmentsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaDepartmentService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.Campus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static com.firstedu.marsladder.diting.xiaoguanjia.DepartmentStubs.UPDATE_TIME_AFTER;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = XiaoguanjiaDepartmentController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser(roles = "ADMIN")
class XiaoguanjiaDepartmentControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    XiaoguanjiaDepartmentService xiaoguanjiaDepartmentService;

    @Test
    void should_return_200_when_syncing_department_data_from_xiaoguanjia() throws Exception {
        var requestBody = """
                    {
                        "updateTimeAfter": "2024-01-01",
                        "pageSize": 2
                    }
                    """;
        var request = new SyncDepartmentsRequest(UPDATE_TIME_AFTER, 2);
        var command = request.toSyncDepartmentsCommand();
        doNothing().when(xiaoguanjiaDepartmentService).syncDepartments(command);

        post("/departments/sync", requestBody)
                .andExpect(status().isOk());

        verify(xiaoguanjiaDepartmentService).syncDepartments(command);
    }

    @Test
    void should_return_all_campuses_successfully() throws Exception {
        List<Campus> campuses = List.of(
                new Campus("campus01", "Test Campus 1"),
                new Campus("campus02", "Test Campus 2")
        );
        when(xiaoguanjiaDepartmentService.getAllCampuses()).thenReturn(campuses);

        get("/xiaoguanjia-campuses")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].campusId").value("campus01"))
                .andExpect(jsonPath("$[0].campusName").value("Test Campus 1"))
                .andExpect(jsonPath("$[1].campusId").value("campus02"))
                .andExpect(jsonPath("$[1].campusName").value("Test Campus 2"));
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}