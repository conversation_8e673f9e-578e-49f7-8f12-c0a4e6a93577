package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DepartmentInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfDepartments;
import com.firstedu.marsladder.diting.exception.NotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.event.DepartmentAction;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.DepartmentActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaDepartmentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaDepartmentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.Campus;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncDepartmentsCommand;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaDepartmentServiceImplTest {

    @Mock
    XiaoguanjiaDepartmentRepository xiaoguanjiaDepartmentRepository;

    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @InjectMocks
    XiaoguanjiaDepartmentServiceImpl service;

    private static final LocalDate UPDATE_TIME_AFTER = LocalDate.of(2024, 1, 1);

    @Nested
    class SyncDepartments {
        @Test
        void should_sync_departments_successfully() {
            var command = new SyncDepartmentsCommand(UPDATE_TIME_AFTER, 2);
            var department1 = new DepartmentInfo("dept1", "Department 1", true, 1, "Dept1", "20240101120000", "20240101120000");
            var department2 = new DepartmentInfo("dept2", "Department 2", false, 1, "Dept2", "20240101120000", "20240101120000");
            var page1 = new PageOfDepartments(1, List.of(department1), 0, "");
            var page2 = new PageOfDepartments(0, List.of(department2), 0, "");

            when(xiaoguanjiaClient.getPageOfDepartments(UPDATE_TIME_AFTER, 2, 1)).thenReturn(page1);
            when(xiaoguanjiaClient.getPageOfDepartments(UPDATE_TIME_AFTER, 2, 2)).thenReturn(page2);

            service.syncDepartments(command);

            verify(xiaoguanjiaClient).getPageOfDepartments(UPDATE_TIME_AFTER, 2, 1);
            verify(xiaoguanjiaClient).getPageOfDepartments(UPDATE_TIME_AFTER, 2, 2);
            verify(xiaoguanjiaDepartmentRepository).save(department1.toEntity());
            verify(xiaoguanjiaDepartmentRepository).save(department2.toEntity());
        }

        @Test
        void should_sync_single_page_departments() {
            var command = new SyncDepartmentsCommand(UPDATE_TIME_AFTER, 1);
            var department = new DepartmentInfo("dept1", "Department 1", true, 1, "Dept1", "20240101120000", "20240101120000");
            var page = new PageOfDepartments(0, List.of(department), 0, "");

            when(xiaoguanjiaClient.getPageOfDepartments(UPDATE_TIME_AFTER, 1, 1)).thenReturn(page);

            service.syncDepartments(command);

            verify(xiaoguanjiaClient).getPageOfDepartments(UPDATE_TIME_AFTER, 1, 1);
            verify(xiaoguanjiaDepartmentRepository).save(department.toEntity());
        }

        @Test
        void should_handle_empty_page() {
            var command = new SyncDepartmentsCommand(UPDATE_TIME_AFTER, 1);
            var page = new PageOfDepartments(0, List.of(), 0, "");

            when(xiaoguanjiaClient.getPageOfDepartments(UPDATE_TIME_AFTER, 1, 1)).thenReturn(page);

            service.syncDepartments(command);

            verify(xiaoguanjiaClient).getPageOfDepartments(UPDATE_TIME_AFTER, 1, 1);
            verify(xiaoguanjiaDepartmentRepository, times(0)).save(any());
        }
    }

    @Nested
    class GetAllCampuses {
        @Test
        void should_get_all_campuses_successfully() {
            XiaoguanjiaDepartmentEntity campus1 = XiaoguanjiaDepartmentEntity.builder()
                    .id("campus01")
                    .name("Test Campus 1")
                    .isCampus(true)
                    .build();
            XiaoguanjiaDepartmentEntity campus2 = XiaoguanjiaDepartmentEntity.builder()
                    .id("campus02")
                    .name("Test Campus 2")
                    .isCampus(true)
                    .build();
            
            when(xiaoguanjiaDepartmentRepository.findAllByDeletedAndStatusAndIsCampus(false, 1, true))
                    .thenReturn(List.of(campus1, campus2));

            var campuses = service.getAllCampuses();
            
            assertThat(campuses).hasSize(2);
            assertThat(campuses.get(0)).isEqualTo(new Campus("campus01", "Test Campus 1"));
            assertThat(campuses.get(1)).isEqualTo(new Campus("campus02", "Test Campus 2"));
        }

        @Test
        void should_return_empty_list_when_no_campuses_exist() {
            when(xiaoguanjiaDepartmentRepository.findAllByDeletedAndStatusAndIsCampus(false, 1, true))
                    .thenReturn(List.of());

            var campuses = service.getAllCampuses();
            
            assertThat(campuses).isEmpty();
        }

        @Test
        void should_filter_false_isCampus_departments() {
            XiaoguanjiaDepartmentEntity campus1 = XiaoguanjiaDepartmentEntity.builder()
                    .id("campus01")
                    .name("Test Campus 1")
                    .isCampus(true)
                    .build();
            
            when(xiaoguanjiaDepartmentRepository.findAllByDeletedAndStatusAndIsCampus(false, 1, true))
                    .thenReturn(List.of(campus1));

            var campuses = service.getAllCampuses();
            
            assertThat(campuses).hasSize(1);
            assertThat(campuses.get(0)).isEqualTo(new Campus("campus01", "Test Campus 1"));
        }
    }

    @Nested
    class HandleDepartmentActionEvent {
        @Test
        void should_handle_create_event_when_department_not_exists() {
            DepartmentActionEvent actionEvent = new DepartmentActionEvent(this, "departmentId", DepartmentAction.CREATE);
            var departmentInfo = new DepartmentInfo("departmentId", "Test Department", true, 1, "Test", "20240101120000", "20240101120000");
            
            when(xiaoguanjiaDepartmentRepository.existsById("departmentId")).thenReturn(false);
            when(xiaoguanjiaClient.fetchDepartment("departmentId")).thenReturn(departmentInfo);

            service.handleDepartmentActionEvent(actionEvent);

            verify(xiaoguanjiaDepartmentRepository).existsById("departmentId");
            verify(xiaoguanjiaClient).fetchDepartment("departmentId");
            verify(xiaoguanjiaDepartmentRepository).save(departmentInfo.toEntity());
        }

        @Test
        void should_handle_create_event_when_department_exists() {
            DepartmentActionEvent actionEvent = new DepartmentActionEvent(this, "departmentId", DepartmentAction.CREATE);
            
            when(xiaoguanjiaDepartmentRepository.existsById("departmentId")).thenReturn(true);

            service.handleDepartmentActionEvent(actionEvent);

            verify(xiaoguanjiaDepartmentRepository).existsById("departmentId");
            verify(xiaoguanjiaClient, times(0)).fetchDepartment(any());
            verify(xiaoguanjiaDepartmentRepository, times(0)).save(any());
        }

        @Test
        void should_handle_modify_event_successfully() {
            DepartmentActionEvent actionEvent = new DepartmentActionEvent(this, "departmentId", DepartmentAction.MODIFY);
            var existingEntity = XiaoguanjiaDepartmentEntity.builder()
                    .id("departmentId")
                    .name("Existing Department")
                    .build();
            var departmentInfo = new DepartmentInfo("departmentId", "Updated Department", true, 1, "Updated", "20240101120000", "20240101120000");
            
            when(xiaoguanjiaDepartmentRepository.findById("departmentId")).thenReturn(Optional.of(existingEntity));
            when(xiaoguanjiaClient.fetchDepartment("departmentId")).thenReturn(departmentInfo);

            service.handleDepartmentActionEvent(actionEvent);

            verify(xiaoguanjiaDepartmentRepository).findById("departmentId");
            verify(xiaoguanjiaClient).fetchDepartment("departmentId");
            verify(xiaoguanjiaDepartmentRepository).save(departmentInfo.toEntity());
        }

        @Test
        void should_throw_exception_when_modify_event_department_not_found() {
            DepartmentActionEvent actionEvent = new DepartmentActionEvent(this, "departmentId", DepartmentAction.MODIFY);
            
            when(xiaoguanjiaDepartmentRepository.findById("departmentId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(NotFoundException.class)
                    .isThrownBy(() -> service.handleDepartmentActionEvent(actionEvent))
                    .withMessage("Failed to modify xiaoguanjia department departmentId, no such department found");

            verify(xiaoguanjiaDepartmentRepository).findById("departmentId");
            verify(xiaoguanjiaClient, times(0)).fetchDepartment(any());
        }

        @Test
        void should_handle_remove_event_successfully() {
            DepartmentActionEvent actionEvent = new DepartmentActionEvent(this, "departmentId", DepartmentAction.REMOVE);
            var existingEntity = XiaoguanjiaDepartmentEntity.builder()
                    .id("departmentId")
                    .name("Department to Remove")
                    .status(1)
                    .build();
            
            when(xiaoguanjiaDepartmentRepository.findById("departmentId")).thenReturn(Optional.of(existingEntity));

            service.handleDepartmentActionEvent(actionEvent);

            verify(xiaoguanjiaDepartmentRepository).findById("departmentId");
            verify(xiaoguanjiaDepartmentRepository).save(existingEntity);
            assertThat(existingEntity.getStatus()).isEqualTo(0);
        }

        @Test
        void should_throw_exception_when_remove_event_department_not_found() {
            DepartmentActionEvent actionEvent = new DepartmentActionEvent(this, "departmentId", DepartmentAction.REMOVE);
            
            when(xiaoguanjiaDepartmentRepository.findById("departmentId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(NotFoundException.class)
                    .isThrownBy(() -> service.handleDepartmentActionEvent(actionEvent))
                    .withMessage("Failed to remove xiaoguanjia department departmentId, no such department found");

            verify(xiaoguanjiaDepartmentRepository).findById("departmentId");
            verify(xiaoguanjiaDepartmentRepository, times(0)).save(any());
        }
    }
}