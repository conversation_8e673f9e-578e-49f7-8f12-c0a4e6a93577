package com.firstedu.marsladder.diting.xiaoguanjia;

import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassroomInfo;

import java.time.LocalDate;

public class ClassroomStubs {
    public static final LocalDate UPDATE_TIME_AFTER = LocalDate.of(2024, 1, 1);

    public static final ClassroomInfo MOCK_CLASSROOM1 = new ClassroomInfo(
            "C001",
            "CampusA",
            "Computer Science 101",
            30,
            1,
            "20240220072554",
            "20240220072554"
    );

    public static final ClassroomInfo MOCK_CLASSROOM2 = new ClassroomInfo(
            "C002",
            "CampusB",
            "Mathematics 201",
            50,
            0,
            "20240220072554",
            "20240220072554"
    );

}
