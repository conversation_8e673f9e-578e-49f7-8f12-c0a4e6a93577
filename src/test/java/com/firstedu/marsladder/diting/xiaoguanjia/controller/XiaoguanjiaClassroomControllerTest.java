package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncClassroomsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassroomService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassroomStubs.UPDATE_TIME_AFTER;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = XiaoguanjiaClassroomController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser
class XiaoguanjiaClassroomControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    XiaoguanjiaClassroomService service;

    @Test
    void should_return_200_when_syncing_classroom_data_from_xiaoguanjia() throws Exception {
        var requestBody = """
                    {
                        "updateTimeAfter": "2024-01-01",
                        "pageSize": 2
                    }
                    """;
        var request = new SyncClassroomsRequest(UPDATE_TIME_AFTER, 2);
        var command = request.toCommand();
        doNothing().when(service).syncClassrooms(command);

        post("/classrooms/sync", requestBody)
                .andExpect(status().isOk());

        verify(service).syncClassrooms(command);
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}