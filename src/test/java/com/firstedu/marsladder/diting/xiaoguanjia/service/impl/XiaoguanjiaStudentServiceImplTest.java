package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaStudentNotFoundException;
import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfStudents;
import com.firstedu.marsladder.diting.xiaoguanjia.event.StudentAction;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.StudentActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncStudentsCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentCourseIdInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseStudentInfo;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.ERROR_CODE;
import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.ERR_MSG_NO_ERR;
import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.MOCK_STUDENT_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.MOCK_STUDENT_2;
import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.PAGE_SIZE;
import static com.firstedu.marsladder.diting.xiaoguanjia.StudentStubs.START_TIME_AFTER;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaStudentServiceImplTest {
    @Mock
    XiaoguanjiaClient xiaoguanjiaClient;

    @Mock
    XiaoguanjiaStudentRepository xiaoguanjiaStudentRepository;

    @Mock
    XiaoguanjiaCourseStudentRepository xiaoguanjiaCourseStudentRepository;

    @InjectMocks
    XiaoguanjiaStudentServiceImpl xiaoguanjiaStudentService;

    @Nested
    class SyncClasses {
        SyncStudentsCommand syncStudentsCommand = new SyncStudentsCommand(START_TIME_AFTER, PAGE_SIZE);

        @Test
        void should_get_and_save_all_classes_when_there_are_more_than_one_page_of_classes() {
            var page1 = new PageOfStudents(1, List.of(MOCK_STUDENT_1), ERROR_CODE, ERR_MSG_NO_ERR);
            var page2 = new PageOfStudents(0, List.of(MOCK_STUDENT_2), ERROR_CODE, ERR_MSG_NO_ERR);
            when(xiaoguanjiaClient.getPageOfStudents(START_TIME_AFTER, PAGE_SIZE, 1)).thenReturn(page1);
            when(xiaoguanjiaClient.getPageOfStudents(START_TIME_AFTER, PAGE_SIZE, 2)).thenReturn(page2);

            xiaoguanjiaStudentService.syncStudents(syncStudentsCommand);

            verify(xiaoguanjiaClient).getPageOfStudents(START_TIME_AFTER, PAGE_SIZE, 1);
            verify(xiaoguanjiaStudentRepository).save(MOCK_STUDENT_1.toEntity());

            verify(xiaoguanjiaClient).getPageOfStudents(START_TIME_AFTER, PAGE_SIZE, 2);
            verify(xiaoguanjiaStudentRepository).save(MOCK_STUDENT_2.toEntity());
        }
    }

    @Nested
    class Events {
        @Test
        void should_save_xiaoguanjia_student_when_receive_created_event() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "studentId", StudentAction.CREATE);
            when(xiaoguanjiaStudentRepository.existsById("studentId")).thenReturn(false);
            when(xiaoguanjiaClient.fetchStudent("studentId"))
                    .thenReturn(MOCK_STUDENT_1);

            xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent);
            verify(xiaoguanjiaStudentRepository).save(MOCK_STUDENT_1.toEntity());
        }

        @Test
        void should_save_xiaoguanjia_student_when_receive_created_event_and_entity_existed() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "studentId", StudentAction.CREATE);
            when(xiaoguanjiaStudentRepository.existsById("studentId")).thenReturn(true);

            xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent);
            verify(xiaoguanjiaStudentRepository, times(0)).save(MOCK_STUDENT_1.toEntity());
        }

        @Test
        void should_save_xiaoguanjia_student_and_sync_student_courses_when_receive_modified_event() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "studentId", StudentAction.MODIFY);
            when(xiaoguanjiaClient.fetchStudent("studentId")).thenReturn(MOCK_STUDENT_1);
            when(xiaoguanjiaStudentRepository.save(MOCK_STUDENT_1.toEntity())).thenReturn(MOCK_STUDENT_1.toEntity());
            var courseIdInfo = new StudentCourseIdInfo("courseId2");
            when(xiaoguanjiaClient.fetchStudentCourseIds("studentId")).thenReturn(List.of(courseIdInfo));
            var courseStudent = new CourseStudentInfo("studentId", 1, 100.0, null, "John Doe", "20240101");
            when(xiaoguanjiaClient.fetchCourseStudents("courseId2")).thenReturn(List.of(courseStudent));
            when(xiaoguanjiaStudentRepository.findById("studentId")).thenReturn(Optional.of(MOCK_STUDENT_1.toEntity()));
            xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent);

            verify(xiaoguanjiaStudentRepository).save(MOCK_STUDENT_1.toEntity());
            verify(xiaoguanjiaCourseStudentRepository).findByCourseIdAndStudentId("courseId2", "studentId");
            verify(xiaoguanjiaCourseStudentRepository).deleteByCourseIdAndStudentId("courseId2", "studentId");
            verify(xiaoguanjiaCourseStudentRepository).save(courseStudent.toEntity("courseId2"));
        }

        @Test
        void should_throw_exception_when_modify_student_not_found() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "nonExistentStudentId", StudentAction.MODIFY);
            when(xiaoguanjiaStudentRepository.findById("nonExistentStudentId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(XiaoguanjiaStudentNotFoundException.class).isThrownBy(
                    () -> xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent)
            ).withMessageContaining("Failed to modify xiaoguanjia student nonExistentStudentId, no such class found");
        }

        @Test
        void should_only_save_matching_course_student_when_sync_course() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "studentId", StudentAction.MODIFY);
            when(xiaoguanjiaClient.fetchStudent("studentId")).thenReturn(MOCK_STUDENT_1);
            when(xiaoguanjiaStudentRepository.save(MOCK_STUDENT_1.toEntity())).thenReturn(MOCK_STUDENT_1.toEntity());
            var courseIdInfo = new StudentCourseIdInfo("courseId2");
            when(xiaoguanjiaClient.fetchStudentCourseIds("studentId")).thenReturn(List.of(courseIdInfo));
            
            // Create two course students, only one matching the target student
            var matchingCourseStudent = new CourseStudentInfo("studentId", 1, 100.0, null, "John Doe", "20240101");
            var nonMatchingCourseStudent = new CourseStudentInfo("differentStudentId", 1, 90.0, null, "Jane Doe", "20240101");
            when(xiaoguanjiaClient.fetchCourseStudents("courseId2")).thenReturn(List.of(matchingCourseStudent, nonMatchingCourseStudent));
            when(xiaoguanjiaStudentRepository.findById("studentId")).thenReturn(Optional.of(MOCK_STUDENT_1.toEntity()));
            
            xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent);

            // Should only save the matching student
            verify(xiaoguanjiaCourseStudentRepository).save(matchingCourseStudent.toEntity("courseId2"));
            verify(xiaoguanjiaCourseStudentRepository, never()).save(nonMatchingCourseStudent.toEntity("courseId2"));
        }

        @Test
        void should_save_xiaoguanjia_student_when_receive_removed_event() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "studentId", StudentAction.REMOVE);
            var existedEntity = MOCK_STUDENT_1.toEntity();
            when(xiaoguanjiaStudentRepository.findById("studentId")).thenReturn(Optional.of(existedEntity));

            xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent);
            existedEntity.setStatus(XiaoguanjiaStudentStatus.DELETED);
            verify(xiaoguanjiaStudentRepository).save(existedEntity);
        }

        @Test
        void should_throw_exception_when_receive_removed_event() {
            StudentActionEvent actionEvent = new StudentActionEvent("", "studentId", StudentAction.REMOVE);
            when(xiaoguanjiaStudentRepository.findById("studentId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(XiaoguanjiaStudentNotFoundException.class).isThrownBy(
                    () -> xiaoguanjiaStudentService.handleClassStudentEvent(actionEvent)
            );
        }
    }

}