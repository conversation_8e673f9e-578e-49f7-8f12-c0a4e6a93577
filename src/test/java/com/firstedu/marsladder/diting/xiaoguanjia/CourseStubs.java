package com.firstedu.marsladder.diting.xiaoguanjia;

import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseStudentInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseTeacherInfo;

import java.time.LocalDate;

public class CourseStubs {
    public static final LocalDate START_TIME_AFTER = LocalDate.of(2024, 1, 1);

    public static final CourseInfo MOCK_COURSE_1 = new CourseInfo(
            "courseId1",
            "shiftId",
            "classId",
            "classroomId",
            0,
            "20241116133000",
            "20241116133000",
            "20241116133000",
            "20241116143000"
    );

    public static final CourseInfo MOCK_COURSE_2 = new CourseInfo(
            "courseId2",
            "shiftId",
            "classId",
            "classroomId",
            0,
            "20241116133000",
            "20241116133000",
            "20241116133000",
            "20241116143000"
    );

    public static final CourseTeacherInfo MOCK_COURSE_TEACHER1 = new CourseTeacherInfo("EMP12345", 1);
    public static final CourseTeacherInfo MOCK_COURSE_TEACHER2 = new CourseTeacherInfo("EMP67890", 2);
    public static final CourseStudentInfo MOCK_COURSE_STUDENT1 = new CourseStudentInfo(
            "S12345",
            1,
            1.00000,
            "A001",
            "Alice",
            "20241116103808"
    );
    public static final CourseStudentInfo MOCK_COURSE_STUDENT2 = new CourseStudentInfo(
            "S67890",
            0,
            0.0000,
            "A002",
            "Bob",
            "20241116103808"
    );
}
