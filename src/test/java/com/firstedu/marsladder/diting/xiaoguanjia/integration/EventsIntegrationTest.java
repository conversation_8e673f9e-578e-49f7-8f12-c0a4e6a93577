package com.firstedu.marsladder.diting.xiaoguanjia.integration;

import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEventRepository;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@SpringBootTest(properties = "spring.flyway.clean-disabled=false")
@AutoConfigureMockMvc
@WithMockMarsLadderUser
@AutoConfigureWireMock
public class EventsIntegrationTest {
    @Autowired
    MockMvc mockMvc;

    @SpyBean
    XiaoguanjiaEventRepository xiaoguanjiaEventRepository;

    @MockBean
    FalconClient falconClient;

    String signature = "bff6ddf6b9acdc887ca62673830dc21b8601a31d";
    String timestamp = "20241210";
    String nonce = "placeholder";

    @Autowired
    Flyway flyway;

    @AfterEach
    void tearDown() {
        flyway.clean();
        flyway.migrate();
    }

    @Test
    void should_persist_xiaoguanjia_event() throws Exception {
        post("/public/xiaogj-events?signature=" + signature + "&timestamp=" + timestamp + "&nonce=" + nonce, """
                {
                  "companyid": "bf95639a-9b50-4413-bf21-8c17acf4d1ed",
                  "appid": "xgj231793",
                  "eventkey": "fin_fee_pay",
                  "eventid": "e6a31f5d-1d45-40c2-ac40-e0d184daf277",
                  "eventtime": "20241210180100000"
                }
                """)
                .andExpect(status().isOk())
                .andExpect(content().string("SUCCESS"));

        verify(xiaoguanjiaEventRepository, times(1)).save(any());
    }

    @Test
    void should_process_class_related_events() throws Exception {
        when(falconClient.getUser()).thenReturn(UserDto.builder().roles(List.of(UserRole.TUTOR)).build());
        var classCreateEvent = """
                {
                  "companyid": "bf95639a-9b50-4413-bf21-8c17acf4d1ed",
                  "appid": "xgj231793",
                  "eventkey": "edu_class_create",
                  "eventid": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                  "eventtime": "20241210180100000"
                }
                """;
        post("/public/xiaogj-events?signature=" + signature + "&timestamp=" + timestamp + "&nonce=" + nonce, classCreateEvent);

        await()
                .pollInSameThread()
                .atMost(10, TimeUnit.SECONDS)
                .untilAsserted(() -> {
                    mvcGet("/classes")
                            .andExpect(status().isOk())
                            .andExpect(content().json("""
                                    [
                                        {
                                            "id": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                                            "name": "Y1-Y6精品英文一对一 (2024)_Sophia Wang (Sijia）"
                                        }
                                    ]"""));
                });

        var classModifyEvent = """
                {
                  "companyid": "bf95639a-9b50-4413-bf21-8c17acf4d1ed",
                  "appid": "xgj231793",
                  "eventkey": "edu_class_modify",
                  "eventid": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                  "eventtime": "20241210180100000"
                }
                """;
        post("/public/xiaogj-events?signature=" + signature + "&timestamp=" + timestamp + "&nonce=" + nonce, classModifyEvent);

        await()
                .pollInSameThread()
                .atMost(10, TimeUnit.SECONDS)
                .untilAsserted(() -> {
                    mvcGet("/classes")
                            .andExpect(status().isOk())
                            .andExpect(content().json("""
                                    [
                                      {
                                        "id": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                                        "name": "xxxxxxxxxxxx"
                                      }
                                    ]"""));
                });

        var classFinishEvent = """
                {
                  "companyid": "bf95639a-9b50-4413-bf21-8c17acf4d1ed",
                  "appid": "xgj231793",
                  "eventkey": "edu_class_finish",
                  "eventid": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                  "eventtime": "20241210180100000"
                }
                """;
        post("/public/xiaogj-events?signature=" + signature + "&timestamp=" + timestamp + "&nonce=" + nonce, classFinishEvent);

        await()
                .pollInSameThread()
                .atMost(10, TimeUnit.SECONDS)
                .untilAsserted(() -> {
                    mvcGet("/classes")
                            .andExpect(status().isOk())
                            .andExpect(content().json("""
                                    []
                                    """));
                });

        // edu_class_remove
        var classRemoveEvent = """
                {
                  "companyid": "bf95639a-9b50-4413-bf21-8c17acf4d1ed",
                  "appid": "xgj231793",
                  "eventkey": "edu_class_remove",
                  "eventid": "66f275fd-d959-4980-9330-a270f4c8b3cb",
                  "eventtime": "20241210180100000"
                }
                """;
        post("/public/xiaogj-events?signature=" + signature + "&timestamp=" + timestamp + "&nonce=" + nonce, classRemoveEvent);

        await()
                .pollInSameThread()
                .atMost(10, TimeUnit.SECONDS)
                .untilAsserted(() -> {
                    mvcGet("/classes")
                            .andExpect(status().isOk())
                            .andExpect(content().json("""
                                    []
                                    """));
                });
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions mvcGet(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}
