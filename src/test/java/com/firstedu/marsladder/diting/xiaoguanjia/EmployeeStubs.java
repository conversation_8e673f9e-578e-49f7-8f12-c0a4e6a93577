package com.firstedu.marsladder.diting.xiaoguanjia;

import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.EmployeeInfo;

import java.time.LocalDate;

public class EmployeeStubs {
    public static final LocalDate START_TIME_AFTER = LocalDate.of(2024, 1, 1);
    public static final int PAGE_SIZE = 20;

    public static final EmployeeInfo MOCK_EMPLOYEE_1 = new EmployeeInfo(
            "employeeId1", "nick_name_1", "nick_name_1", 1, "20240201010101", "20240201010101"
    );

    public static final EmployeeInfo MOCK_EMPLOYEE_2 = new EmployeeInfo(
            "employeeId2", "nick_name_2", "nick_name_1", 1, "20240201010101", "20240201010101"
    );

    public static final int ERROR_CODE = 0;
    public static final String ERR_MSG_NO_ERR = "";

}
