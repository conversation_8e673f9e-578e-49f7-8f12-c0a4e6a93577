package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.XiaoguanjiaEventRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaCommonService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.*;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = XiaoguanjiaCommonController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser
class XiaoguanjiaCommonControllerTest {

    @Autowired
    MockMvc mockMvc;

    @MockBean
    XiaoguanjiaCommonService xiaoguanjiaCommonService;

    @Autowired
    ObjectMapper objectMapper;

    @Test
    void should_get_xiaoguanjia_access_token_successfully() throws Exception {
        String mockAccessToken = "mockAccessToken";
        when(xiaoguanjiaCommonService.getAccessToken()).thenReturn(mockAccessToken);

        mockMvc.perform(get("/xiaogj-access-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value(mockAccessToken));

        verify(xiaoguanjiaCommonService, times(1)).getAccessToken();
    }

    @Test
    void should_save_xiaoguanjia_event_successfully() throws Exception {
        XiaoguanjiaEventRequest request = new XiaoguanjiaEventRequest("companyId", "appid", "eventKey", "eventId", "eventTime");
        String signature = "910260c59bd8115c554a55054fb0388c2efc4fdd";
        String timestamp = "*********";
        String nonce = "randomNonce";

        doNothing().when(xiaoguanjiaCommonService).saveEvent(any());

        mockMvc.perform(post("/public/xiaogj-events")
                        .param("signature", signature)
                        .param("timestamp", timestamp)
                        .param("nonce", nonce)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        verify(xiaoguanjiaCommonService, times(1)).saveEvent(any());
    }
}
