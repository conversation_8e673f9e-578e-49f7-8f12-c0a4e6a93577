package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassTeachersResponse.ClassTeacherListElement;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassTeacherRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class XiaoguanjiaClassTeacherServiceImplTest {

    @Mock
    private XiaoguanjiaClient xiaoguanjiaClient;
    @Mock
    private XiaoguanjiaClassTeacherRepository xiaoguanjiaClassTeacherRepository;

    @InjectMocks
    private XiaoguanjiaClassTeacherServiceImpl xiaoguanjiaClassTeacherService;

    @Test
    void should_save_class_teachers_to_db() {
        ClassTeacherListElement classTeacher = new ClassTeacherListElement("20241226165100", "teacherId", 1, "subjectId", List.of());
        when(xiaoguanjiaClassTeacherRepository.findByClassId("classId")).thenReturn(null);
        when(xiaoguanjiaClient.getClassTeachers("classId")).thenReturn(List.of(classTeacher));

        xiaoguanjiaClassTeacherService.refreshClassTeachers("classId");

        verify(xiaoguanjiaClassTeacherRepository).save(classTeacher.toEntity("classId"));
        verify(xiaoguanjiaClassTeacherRepository).deleteByClassId("classId");
    }
}