package com.firstedu.marsladder.diting.xiaoguanjia;

import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentInfo;

import java.time.LocalDate;

public class StudentStubs {
    public static final LocalDate START_TIME_AFTER = LocalDate.of(2024, 1, 1);
    public static final int PAGE_SIZE = 20;

    public static final StudentInfo MOCK_STUDENT_1 = new StudentInfo(
            "1", 1, "C1001", "<PERSON> Doe", "<PERSON>", 1, "1234567890", "123 Main St",
            "johndoe", "http://example.com/img1.jpg", "A001", "ID123456", "20000101", "20000101", "XYZ School",
            "Class 1", "20240101", "20241231", "M001", "20240501", "T001", "20240101", "20240601",
            "C1002", "I001", "20240101", 0, 1, "20240501", "20240101", "20241231", "20240501",
            0, "None", null, "describe", 1, "S001", "Point 100", "20240101010101", "20241217000000"
    );

    public static final StudentInfo MOCK_STUDENT_2 = new StudentInfo(
            "2", 1, "C2002", "Jane Smith", "Jane", 2, "0987654321", "456 Elm St",
            "janesmith", "http://example.com/img2.jpg", "B002", "ID987654", "19991231", "20000101", "ABC School",
            "Class 2", "20240201", "20241231", "M002", "20240601", "T002", "20240201", "20240701",
            "C2002", "I002", "20240201", 1, 2, "20240601", "20240201", "20241231", "20240601",
            1, "Issue with class", null, "describe", 0, "S002", "Point 200", "20240201010101", "20241217000000"
    );

    public static final int ERROR_CODE = 0;
    public static final String ERR_MSG_NO_ERR = "";

}
