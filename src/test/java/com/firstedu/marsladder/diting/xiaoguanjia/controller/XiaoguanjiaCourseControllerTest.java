package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncCoursesRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaCourseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static com.firstedu.marsladder.diting.xiaoguanjia.CourseStubs.START_TIME_AFTER;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = XiaoguanjiaCourseController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser
class XiaoguanjiaCourseControllerTest {
    @Autowired
    MockMvc mockMvc;

    @MockBean
    XiaoguanjiaCourseService xiaoguanjiaCourseService;

    @Test
    void should_return_200_when_syncing_course_data_from_xiaoguanjia() throws Exception {
        var requestBody = """
                    {
                        "startTimeAfter": "2024-01-01",
                        "pageSize": 2
                    }
                    """;
        var request = new SyncCoursesRequest(START_TIME_AFTER, 2);
        var syncClassroomCommand = request.toSyncCoursesCommand();
        doNothing().when(xiaoguanjiaCourseService).syncCourses(syncClassroomCommand);

        post("/courses/sync", requestBody)
                .andExpect(status().isOk());

        verify(xiaoguanjiaCourseService).syncCourses(syncClassroomCommand);
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}