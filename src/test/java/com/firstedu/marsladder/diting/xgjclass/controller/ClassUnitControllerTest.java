package com.firstedu.marsladder.diting.xgjclass.controller;

import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.xgjclass.exception.ClassUnitAlreadyCommentedException;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;
import com.firstedu.marsladder.diting.xgjclass.service.impl.ClassUnitServiceImpl;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.mockito.Mockito.*;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = ClassUnitController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser(name = "childUserId", roles = "REALUS_STUDENT")
class ClassUnitControllerTest {

    @Autowired
    MockMvc mockMvc;

    @MockBean
    ClassUnitServiceImpl classUnitService;

    @Nested
    class CreateComment {
        String commentId = "commentId";
        String classUnitId = "classUnitId";
        String attendedBy = "childUserId";
        String commentedBy = "childUserId";
        float score = 4.5F;
        String comment = "good good";

        @Test
        void should_return_201_when_created_comment() throws Exception {
            when(classUnitService.comment(new ClassUnitComment(null, commentedBy, classUnitId, attendedBy, score, comment)))
                    .thenReturn(new ClassUnitComment(commentId, commentedBy, classUnitId, attendedBy, score, comment));

            post("/class-units/" + classUnitId + "/comment", """
                    {
                      "attendedBy": "childUserId",
                      "score": 4.5,
                      "comment": "good good"
                    }""")
                    .andExpect(status().isCreated())
                    .andExpect(content().json("""
                            {
                              "id": "commentId",
                              "classUnitId": "classUnitId",
                              "score": 4.5,
                              "comment": "good good"
                            }
                            """, true));
        }

        @Test
        void should_return_201_when_comment_request_only_has_score() throws Exception {
            when(classUnitService.comment(new ClassUnitComment(null, commentedBy, classUnitId, attendedBy, score, null)))
                    .thenReturn(new ClassUnitComment(commentId, commentedBy, classUnitId, attendedBy, score, null));

            post("/class-units/" + classUnitId + "/comment", """
                    {
                      "attendedBy": "childUserId",
                      "score": 4.5
                    }
                    """)
                    .andExpect(status().isCreated())
                    .andExpect(content().json("""
                            {
                              "id": "commentId",
                              "classUnitId": "classUnitId",
                              "score": 4.5,
                              "comment": null
                            }
                            """, true));
        }

        @Test
        void should_return_400_when_bad_request() throws Exception {
            post("/class-units/" + classUnitId + "/comment", """
                    {
                        "attendedBy": "childUserId",
                        "comment": "only comment no score"
                    }
                    """).andExpect(status().isBadRequest());

            verify(classUnitService, never()).comment(any());
        }

        @Test
        void should_return_400_when_classunit_already_commented() throws Exception {
            doThrow(new ClassUnitAlreadyCommentedException("already commented")).when(
                    classUnitService).comment(any());

            post("/class-units/" + classUnitId + "/comment", """
                    {
                        "attendedBy": "childUserId",
                        "comment": "only comment no score"
                    }
                    """).andExpect(status().isBadRequest());
        }
    }

    @Nested
    class GetComments {
        String classUnitId = "classUnitId";
        String attendedBy = "childUserId";
        String commentedBy = "childUserId";
        float score = 4.5F;
        String comment = "good good";

        @Test
        void should_return_200_when_getting_comment() throws Exception {
            var commentId = "commentId";
            when(classUnitService.getComment(classUnitId, attendedBy))
                    .thenReturn(new ClassUnitComment(commentId, attendedBy, classUnitId, attendedBy, score, comment));

            get("/class-units/" + classUnitId + "/comment?attendedBy=childUserId")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            {
                              "id": "commentId",
                              "classUnitId": "classUnitId",
                              "score": 4.5,
                              "comment": "good good"
                            }
                            """));
        }

        @Test
        void should_return_200_when_comment_not_found() throws Exception {
            when(classUnitService.getComment(classUnitId, attendedBy))
                    .thenReturn(ClassUnitComment.ofEmpty(classUnitId, attendedBy));

            get("/class-units/" + classUnitId + "/comment?attendedBy=childUserId")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            {
                              "id": null,
                              "classUnitId": "classUnitId",
                              "score": null,
                              "comment": null
                            }
                            """));
        }
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}