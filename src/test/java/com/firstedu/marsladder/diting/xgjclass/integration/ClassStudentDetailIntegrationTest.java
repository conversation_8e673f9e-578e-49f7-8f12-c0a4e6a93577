package com.firstedu.marsladder.diting.xgjclass.integration;

import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(properties = "spring.flyway.clean-disabled=false")
@AutoConfigureMockMvc
@WithMockMarsLadderUser
@AutoConfigureWireMock
public class ClassStudentDetailIntegrationTest {

    @Autowired
    MockMvc mockMvc;

    @Autowired
    Flyway flyway;

    @AfterEach
    void tearDown() {
        flyway.clean();
        flyway.migrate();
    }

    @MockBean
    FalconClient falconClient;

    @Test
    void should_get_student_class_details_when_student_is_in_class() throws Exception {
        assumeUserBoundToStudent("userId", "57ce7b60-7bd1-44b3-97de-4d56b99edf8f");
        post("/classes/sync", """
                {
                   "createdAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        post("/shifts/sync", """
                {
                   "updatedAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        post("/employees/sync", """
                {
                   "startTimeAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        get("/classes/me")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                          {
                            "id": "6b1eaf22-cc5f-4dd0-8913-78414a985d73",
                            "xiaogjStudentId": "57ce7b60-7bd1-44b3-97de-4d56b99edf8f",
                            "marsladderUserId": "userId",
                            "gradeId": "00000000-0000-0000-0000-000000000000",
                            "status": "IN_PROGRESS",
                            "subjectId": "00000000-0000-0000-0000-000000000000",
                            "courseId": "a7a8c084-391b-4ad9-a624-e82934f8db3a",
                            "courseName": "VCE精品一对一(2024)",
                            "classId": "251a3cf7-a099-426e-af9b-2139c60ab917",
                            "className": "VCE精品一对一(2024)_Shengjie Tong(Jacky)网课",
                            "classCreatedAt": 1722989905,
                            "teacherName": "Anson So",
                            "registeredAt": 1722989905
                          }
                        ]""", true));
    }

    @Test
    void should_get_student_class_details_when_student_is_no_longer_in_class() throws Exception {
        assumeUserBoundToStudent("userId", "babb2ecf-61af-4954-ab5c-8aa0808834b6");
        post("/classes/sync", """
                {
                   "createdAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        post("/shifts/sync", """
                {
                   "updatedAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        post("/employees/sync", """
                {
                   "startTimeAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        get("/classes/me")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                          {
                            "id": "229d08d7-8d2d-455e-a622-afab3afc6cbe",
                            "marsladderUserId": "userId",
                            "xiaogjStudentId": "babb2ecf-61af-4954-ab5c-8aa0808834b6",
                            "status": "REMOVED",
                            "gradeId": "00000000-0000-0000-0000-000000000000",
                            "subjectId": "00000000-0000-0000-0000-000000000000",
                            "courseId": "d9d700db-1805-4f36-a967-686303852a6e",
                            "courseName": "Y1-Y6精品英文一对一 (2024)",
                            "classId": "fb6c27f9-3f60-44fd-a28e-6f17c67caa16",
                            "className": "Y1-Y6精品一对一 (2024)_Enoch Lian",
                            "classCreatedAt": 1708385154,
                            "teacherName": "Katherine Qin",
                            "registeredAt": 1708385154
                          }
                        ]""", true));
    }

    private void assumeUserBoundToStudent(String userId, String studentId) {
        UserDto userDto = new UserDto(userId, "email", List.of(UserRole.REALUS_STUDENT),
                new RealusStudentProfile(userId, "nickNmae", "avatat", studentId), null);
        when(falconClient.getUser()).thenReturn(userDto);
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}
