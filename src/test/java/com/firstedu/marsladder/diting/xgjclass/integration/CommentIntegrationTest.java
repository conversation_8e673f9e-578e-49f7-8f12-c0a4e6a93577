package com.firstedu.marsladder.diting.xgjclass.integration;

import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(properties = "spring.flyway.clean-disabled=false")
@AutoConfigureMockMvc
@AutoConfigureWireMock
@WithMockMarsLadderUser(name = "userId", roles = "REALUS_STUDENT")
public class CommentIntegrationTest {
    @Autowired
    MockMvc mockMvc;

    @Autowired
    Flyway flyway;

    @AfterEach
    void tearDown() {
        flyway.clean();
        flyway.migrate();
    }

    @Test
    void should_create_comment_on_class_unit() throws Exception {
        post("/courses/sync", """
                {
                  "startTimeAfter": "2024-01-01",
                  "pageSize": 100
                }""");
        post("/shifts/sync", """
                {
                   "updatedAfter": "2024-10-01",
                   "pageSize": 2
                }
                """);

        String classId = "fb6c27f9-3f60-44fd-a28e-6f17c67caa16";
        String classUnitId = "fecef799-2b7b-46e3-9f5a-fa236a012785";

        // no comments before commenting
        get("/classes/" + classId + "/comments?attendedBy=" + "userId")
                .andExpect(status().isOk())
                .andExpect(content().json("""
                        [
                            {
                              "id": null,
                              "classUnitId": "fecef799-2b7b-46e3-9f5a-fa236a012785",
                              "score": null,
                              "comment": null
                            }
                        ]
                        """, true
                ));

        // create comment
        post("/class-units/" + classUnitId + "/comment", """
                        {
                          "attendedBy": "userId",
                          "score": 4.5,
                          "comment": "good good"
                        }
                """).andExpect(status().isCreated())
                .andExpect(jsonPath("id").isNotEmpty())
                .andExpect(content().json("""
                        {
                          "classUnitId": "fecef799-2b7b-46e3-9f5a-fa236a012785",
                          "score": 4.5,
                          "comment": "good good"
                        }
                        """));

        // should only create one comment each class unit
        post("/class-units/" + classUnitId + "/comment", """
                        {
                          "attendedBy": "userId",
                          "score": 4.5,
                          "comment": "good good"
                        }
                """).andExpect(status().isBadRequest());

        // get by direct class unit id
        get("/class-units/" + classUnitId + "/comment?attendedBy=userId")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").isNotEmpty())
                .andExpect(jsonPath("$.classUnitId").value(classUnitId))
                .andExpect(content().json("""
                        {
                          "score": 4.5,
                          "comment": "good good"
                        }
                        """));

        // get all by class id
        get("/classes/" + classId + "/comments?attendedBy=" + "userId")
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").isNotEmpty())
                .andExpect(jsonPath("$[0].classUnitId").value(classUnitId))
                .andExpect(content().json("""
                        [
                            {
                              "classUnitId": "fecef799-2b7b-46e3-9f5a-fa236a012785",
                              "score": 4.5,
                              "comment": "good good"
                            }
                        ]
                        """
                ));
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}
