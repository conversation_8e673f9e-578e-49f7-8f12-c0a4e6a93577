package com.firstedu.marsladder.diting.xgjclass.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.firstedu.marsladder.diting.classroom.service.domain.Classroom;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusParentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.course.service.CourseService;
import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import com.firstedu.marsladder.diting.security.AmzOidcBearerTokenResolver;
import com.firstedu.marsladder.diting.security.JwtAuthenticationTokenConverter;
import com.firstedu.marsladder.diting.security.WebMvcTestConfiguration;
import com.firstedu.marsladder.diting.security.WebSecurityConfig;
import com.firstedu.marsladder.diting.security.custommockuser.WithMockMarsLadderUser;
import com.firstedu.marsladder.diting.utils.DatetimeProvider;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.Status;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.TaskFeedbackRequest;
import com.firstedu.marsladder.diting.xgjclass.service.ClassService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassStudentService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassUnitService;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassStudentDetail;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;
import com.firstedu.marsladder.diting.xgjclass.service.domain.StudentClassTasksFeedBack;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDateTime;
import java.util.List;

import static com.firstedu.marsladder.diting.xgjclass.controller.ClassController.computeDisplayedStatus;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_TIME;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.XIAOGUANJIA_CLASS_1_HEADMASTER_ID;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.XIAOGUANJIA_CLASS_ID_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.XIAOGUANJIA_CLASS_NAME_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.GradeStubs.GRADE_ID_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ShiftStubs.SHIFT_ID_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.SubjectStubs.SUBJECT_ID_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus.ENTERED;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus.REMOVED;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus.DELETED;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus.DISABLED;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus.ENABLED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = ClassController.class,
        excludeFilters = @ComponentScan.Filter(
                type = ASSIGNABLE_TYPE,
                classes = {JwtAuthenticationTokenConverter.class, AmzOidcBearerTokenResolver.class})
)
@Import({WebSecurityConfig.class, WebMvcTestConfiguration.class})
@WithMockMarsLadderUser(roles = {"REALUS_STUDENT", "REALUS_PARENT"})
class ClassControllerTest {
    @Autowired
    MockMvc mockMvc;

    @Autowired
    ObjectMapper objectMapper;

    @MockBean
    ClassService classService;

    @MockBean
    ClassStudentService classStudentService;

    @MockBean
    CourseService courseService;

    @MockBean
    ClassUnitService classUnitService;

    @MockBean
    FalconClient falconClient;

    @MockBean
    DatetimeProvider datetimeProvider;

    @Nested
    class GetClasses {
        @Test
        void should_return_200_when_getting_all_classes_when_tutor() throws Exception {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.TUTOR, UserRole.CONSUMER))
                            .build()
            );
            when(classService.getClasses())
                    .thenReturn(List.of(new XiaoguanjiaClass(
                            XIAOGUANJIA_CLASS_ID_1,
                            XIAOGUANJIA_CLASS_NAME_1,
                            XIAOGUANJIA_CLASS_1_HEADMASTER_ID,
                            SUBJECT_ID_1,
                            SHIFT_ID_1,
                            false,
                            MOCK_TIME,
                            GRADE_ID_1)));

            get("/classes")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "fb6c27f9-3f60-44fd-a28e-6f17c67caa16",
                                "name": "Y1-Y6精品一对一 (2024)_Enoch Lian"
                              }
                            ]
                            """));

            verify(classService).getClasses();
        }

        @Test
        void should_return_200_when_getting_all_classes_when_teacher() throws Exception {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.TEACHER, UserRole.CONSUMER))
                            .build()
            );
            when(classService.getClassesByCampusOfTeacher())
                    .thenReturn(List.of(new XiaoguanjiaClass(
                            XIAOGUANJIA_CLASS_ID_1,
                            XIAOGUANJIA_CLASS_NAME_1,
                            XIAOGUANJIA_CLASS_1_HEADMASTER_ID,
                            SUBJECT_ID_1,
                            SHIFT_ID_1,
                            false,
                            MOCK_TIME,
                            GRADE_ID_1)));

            get("/classes")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "fb6c27f9-3f60-44fd-a28e-6f17c67caa16",
                                "name": "Y1-Y6精品一对一 (2024)_Enoch Lian"
                              }
                            ]
                            """));

            verify(classService).getClassesByCampusOfTeacher();
        }

        @Test
        void should_return_200_when_getting_all_classes_when_other_role() throws Exception {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.CONSUMER))
                            .build()
            );

            get("/classes")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                            ]
                            """));
        }

    }

    @Nested
    class GetMyClasses {
        @Test
        void should_return_200_when_get_classes_with_student_logged_in() throws Exception {
            assumeStudentExists("userId", "studentId");
            when(classStudentService.getAllClassStudentDetailsByStudentId("studentId"))
                    .thenReturn(List.of(new ClassStudentDetail(
                            "gradeId",
                            "subjectId",
                            "shiftId",
                            "General Mathematics VCE 模拟考试（2025）",
                            ENABLED,
                            "classId",
                            "className",
                            MOCK_TIME,
                            false,
                            "Nick Zhang",
                            "classStudentId",
                            ENTERED,
                            MOCK_TIME
                    )));

            get("/classes/me")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "classStudentId",
                                "xiaogjStudentId": "studentId",
                                "marsladderUserId": "userId",
                                "gradeId": "gradeId",
                                "subjectId": "subjectId",
                                "courseId": "shiftId",
                                "courseName": "General Mathematics VCE 模拟考试（2025）",
                                "classId": "classId",
                                "className": "className",
                                "classCreatedAt": 1734912000,
                                "teacherName": "Nick Zhang",
                                "registeredAt": 1734912000,
                                "status": "IN_PROGRESS",
                              }
                            ]
                            """, true));
        }

        @Test
        void should_return_200_and_filtered_classes_with_student_logged_in() throws Exception {
            assumeStudentExists("userId", "studentId");
            when(classStudentService.getAllClassStudentDetailsByStudentId("studentId"))
                    .thenReturn(List.of(new ClassStudentDetail(
                            "gradeId",
                            "subjectId",
                            "shiftId",
                            "General Mathematics VCE 模拟考试（2025）",
                            ENABLED,
                            "classId",
                            "className",
                            MOCK_TIME,
                            false,
                            "Nick Zhang",
                            "classStudentId",
                            ENTERED,
                            MOCK_TIME
                    )));

            get("/classes/me?courseStatus=IN_PROGRESS")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "classStudentId",
                                "xiaogjStudentId": "studentId",
                                "marsladderUserId": "userId",
                                "gradeId": "gradeId",
                                "subjectId": "subjectId",
                                "courseId": "shiftId",
                                "courseName": "General Mathematics VCE 模拟考试（2025）",
                                "classId": "classId",
                                "className": "className",
                                "classCreatedAt": 1734912000,
                                "teacherName": "Nick Zhang",
                                "registeredAt": 1734912000,
                                "status": "IN_PROGRESS",
                              }
                            ]
                            """, true));
        }

        @Test
        void should_return_200_when_get_classes_with_parent_logged_in() throws Exception {
            assumeParentExists("userId", List.of("studentId1"));
            when(classStudentService.getAllClassStudentDetailsByStudentId("studentId1"))
                    .thenReturn(List.of(new ClassStudentDetail(
                            "gradeId",
                            "subjectId",
                            "shiftId",
                            "General Mathematics VCE 模拟考试（2025）",
                            ENABLED,
                            "classId",
                            "className",
                            MOCK_TIME,
                            false,
                            "Nick Zhang",
                            "classStudentId",
                            ENTERED,
                            MOCK_TIME
                    )));

            get("/classes/me")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "classStudentId",
                                "xiaogjStudentId": "studentId1",
                                "marsladderUserId": "userId",
                                "gradeId": "gradeId",
                                "subjectId": "subjectId",
                                "courseId": "shiftId",
                                "courseName": "General Mathematics VCE 模拟考试（2025）",
                                "classId": "classId",
                                "className": "className",
                                "classCreatedAt": 1734912000,
                                "teacherName": "Nick Zhang",
                                "registeredAt": 1734912000,
                                "status": "IN_PROGRESS",
                              }
                            ]
                            """, true));
        }

        @Test
        void should_return_200_and_filtered_classes_with_parent_logged_in() throws Exception {
            assumeParentExists("userId", List.of("studentId1"));
            when(classStudentService.getAllClassStudentDetailsByStudentId("studentId1"))
                    .thenReturn(List.of(new ClassStudentDetail(
                            "gradeId",
                            "subjectId",
                            "shiftId",
                            "General Mathematics VCE 模拟考试（2025）",
                            ENABLED,
                            "classId",
                            "className",
                            MOCK_TIME,
                            false,
                            "Nick Zhang",
                            "classStudentId",
                            ENTERED,
                            MOCK_TIME
                    )));

            get("/classes/me?courseStatus=IN_PROGRESS")
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                              {
                                "id": "classStudentId",
                                "xiaogjStudentId": "studentId1",
                                "marsladderUserId": "userId",
                                "gradeId": "gradeId",
                                "subjectId": "subjectId",
                                "courseId": "shiftId",
                                "courseName": "General Mathematics VCE 模拟考试（2025）",
                                "classId": "classId",
                                "className": "className",
                                "classCreatedAt": 1734912000,
                                "teacherName": "Nick Zhang",
                                "registeredAt": 1734912000,
                                "status": "IN_PROGRESS",
                              }
                            ]
                            """, true));
        }

        @Test
        void should_set_courseStatus_based_on_student_class_shift() {
            assertThat(computeDisplayedStatus(classStudentDetail(ENABLED, false, ENTERED))).isEqualTo(Status.IN_PROGRESS);
            assertThat(computeDisplayedStatus(classStudentDetail(ENABLED, false, REMOVED))).isEqualTo(Status.REMOVED);
            assertThat(computeDisplayedStatus(classStudentDetail(ENABLED, true, ENTERED))).isEqualTo(Status.COMPLETED);
            assertThat(computeDisplayedStatus(classStudentDetail(ENABLED, true, REMOVED))).isEqualTo(Status.COMPLETED);
            assertThat(computeDisplayedStatus(classStudentDetail(DELETED, true, REMOVED))).isEqualTo(Status.COMPLETED);
            assertThat(computeDisplayedStatus(classStudentDetail(DISABLED, true, REMOVED))).isEqualTo(Status.COMPLETED);
        }

        private static ClassStudentDetail classStudentDetail(ShiftStatus shiftStatus, Boolean isClassFinished, ClassStudentStatus classStudentStatus) {
            return ClassStudentDetail
                    .builder()
                    .shiftStatus(shiftStatus)
                    .isClassFinished(isClassFinished)
                    .classStudentStatus(classStudentStatus)
                    .build();
        }
    }

    @Nested
    class GetClassComments {
        @Test
        void should_return_200_when_get_all_comments_of_class() throws Exception {
            var userId = "id";
            var classId = "classId";
            var classUnitId = "classUnitId";
            when(courseService.getCourseSchedules(CourseScheduleFilter.builder().classId(classId).build()))
                    .thenReturn(List.of(CourseSchedule.builder().courseId(classUnitId).build()));
            when(classUnitService.getComments(List.of(classUnitId), userId))
                    .thenReturn(List.of(ClassUnitComment.builder().id("commentId").classUnitId(classUnitId).score(3.5f).comment("comment").build()));

            get("/classes/" + classId + "/comments?attendedBy=" + userId)
                    .andExpect(status().isOk())
                    .andExpect(content().json("""
                            [
                                {
                                  "id": "commentId",
                                  "classUnitId": "classUnitId",
                                  "score": 3.5,
                                  "comment": "comment",
                                }
                            ]
                            """));
        }
    }

    @Nested
    class GetMarsLadderClassrooms {
        @Test
        void should_return_200_when_get_marsladder_classrooms() throws Exception {
            when(classService.getMarsladderClassrooms("652cfaf7-2263-40c4-af07-d00605603309"))
                    .thenReturn(List.of(new Classroom("id")));

            get("/classes/652cfaf7-2263-40c4-af07-d00605603309/marsladder-classrooms")
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$[0].classroomId").value("id"));
        }
    }

    @Nested
    class GetStudentClassTasksFeedBack {
        @Test
        void should_return_200_when_get_marsladder_classrooms() throws Exception {
            when(classService.getStudentClassTasksFeedBack("classId", "studentId"))
                    .thenReturn(List.of(new StudentClassTasksFeedBack("id", "classId", "studentId", "userId", "A", "hao hao hao", "好好好", 12312321L, 12312321L)));

            get("/classes/classId/students/studentId/task-feedback")
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$[0].classId").value("classId"))
                    .andExpect(jsonPath("$[0].xiaogjStudentId").value("studentId"))
                    .andExpect(jsonPath("$[0].overallGrading").value("A"))
                    .andExpect(jsonPath("$[0].comment").value("hao hao hao"));
        }
    }

    @Nested
    class GenerateBoundedClassTasksPhaseFeedback {
        @Test
        void should_return_200_when_generate_bounded_class_tasks_phase_feed_back() throws Exception {
            var firstDayOfMonth = LocalDateTime.of(2025, 1, 1, 12, 0, 0);
            var nowTime = LocalDateTime.of(2025, 1, 31, 12, 0, 0);
            mockMvc.perform(
                    MockMvcRequestBuilders.post("/all-bounded-classes/tasks/phase-feedback")
                            .param("startTime", "2025-01-01T12:00:00")
                            .param("endTime", "2025-01-31T12:00:00")
                            .contentType(APPLICATION_JSON))
                    .andExpect(status().isOk());

            verify(classService).generateTaskFeedbacksForBoundedClasses(firstDayOfMonth, nowTime);
        }

        @Test
        void should_return_200_when_generate_bounded_class_tasks_phase_feed_back_from_cron() throws Exception {
            var firstDayOfMonth = LocalDateTime.of(2024, 1, 1, 1, 1);
            var nowTime = LocalDateTime.of(2024, 1, 31, 1, 1);
            when(datetimeProvider.now()).thenReturn(nowTime);
            when(datetimeProvider.getFirstDayOfCurrentMonth()).thenReturn(firstDayOfMonth);
            mockMvc.perform(
                            MockMvcRequestBuilders.post("/all-bounded-classes/tasks/phase-feedback")
                                    .contentType(APPLICATION_JSON))
                    .andExpect(status().isOk());

            verify(classService).generateTaskFeedbacksForBoundedClasses(firstDayOfMonth, nowTime);
        }

        @Test
        void should_return_200_when_generate_tasks_phase_feed_back_for_student_in_class() throws Exception {
            var startTime = LocalDateTime.of(2025, 3, 1, 0, 0, 0);
            var endTime = LocalDateTime.of(2025, 3, 31, 12, 0, 0);
            var taskFeedbackRequest = new TaskFeedbackRequest("studentId", startTime, endTime);

            post("/classes/classId/tasks/phase-feedback", objectMapper.writeValueAsString(taskFeedbackRequest))
                    .andExpect(status().isOk());
            verify(classService).generateTaskFeedbackForStudentInClass("classId", "studentId", startTime, endTime);
        }
    }

    private void assumeStudentExists(String userId, String studentId) {
        UserDto userDto = new UserDto(userId, "email", List.of(UserRole.REALUS_STUDENT),
                new RealusStudentProfile("userId", "nickNmae", "avatat", studentId), null);
        when(falconClient.getUser()).thenReturn(userDto);
    }

    private void assumeParentExists(String userId, List<String> studentIdList) {
        UserDto userDto = new UserDto(userId, "email", List.of(UserRole.REALUS_PARENT), null,
                new RealusParentProfile("avatar", studentIdList.stream().map(studentId -> new RealusStudentProfile(userId, "nickName", "avatar", studentId)).toList()));
        when(falconClient.getUser()).thenReturn(userDto);
    }

    private ResultActions post(String url, String requestBody) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.post(url)
                        .contentType(APPLICATION_JSON)
                        .content(requestBody));
    }

    private ResultActions get(String url) throws Exception {
        return mockMvc.perform(
                MockMvcRequestBuilders.get(url)
                        .accept(MediaType.APPLICATION_JSON));
    }
}