package com.firstedu.marsladder.diting.xgjclass.service.impl;

import com.firstedu.marsladder.diting.campus.repository.SchoolCampusBindingsRepository;
import com.firstedu.marsladder.diting.campus.repository.entity.SchoolCampusBindingsEntity;
import com.firstedu.marsladder.diting.classroom.controller.dto.MarsladderClassroom;
import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.classroom.repository.ClassroomBindingsRepository;
import com.firstedu.marsladder.diting.classroom.repository.entity.ClassroomBindingsEntity;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.ClassPerformance;
import com.firstedu.marsladder.diting.client.falcon.dto.ClassPerformanceByType;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatistic;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticByType;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticForAiFeedback;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticRequest;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusParentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.SchoolCampusDto;
import com.firstedu.marsladder.diting.client.falcon.dto.TeacherProfileDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.client.falcon.dto.XiaogjClassInfo;
import com.firstedu.marsladder.diting.client.kuixing.KuixingClient;
import com.firstedu.marsladder.diting.client.kuixing.dto.AiFeedbackDto;
import com.firstedu.marsladder.diting.client.nezha.NezhaClient;
import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatistic;
import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatisticForAiFeedback;
import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatisticRequest;
import com.firstedu.marsladder.diting.client.nezha.dto.TaskType;
import com.firstedu.marsladder.diting.xgjclass.repository.StudentClassTasksPhaseFeedbackRepository;
import com.firstedu.marsladder.diting.xgjclass.repository.entity.StudentClassTasksPhaseFeedbackEntity;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaGradeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaSubjectRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.classroom.ClassroomStubs.CLASSROOM_ID1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_CLASS_1;
import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_TIME;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus.ENABLED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ClassServiceImplTest {

    @Mock
    private XiaoguanjiaClassRepository xiaoguanjiaClassRepository;

    @Mock
    private ClassroomBindingsRepository classroomBindingsRepository;

    @Mock
    private StudentClassTasksPhaseFeedbackRepository studentClassTasksPhaseFeedbackRepository;

    @Mock
    private FalconClient falconClient;

    @Mock
    private KuixingClient kuixingClient;

    @Mock
    private NezhaClient nezhaClient;

    @Mock
    private XiaoguanjiaGradeRepository xiaoguanjiaGradeRepository;

    @Mock
    private XiaoguanjiaSubjectRepository xiaoguanjiaSubjectRepository;

    @Mock
    private SchoolCampusBindingsRepository schoolCampusBindingsRepository;

    @InjectMocks
    ClassServiceImpl classService;

    @Nested
    class GetClasses {
        @Test
        void should_get_all_classes() {
            when(xiaoguanjiaClassRepository.findAllByIsFinishedAndDeletedAndStatus(false, false, 1)).thenReturn(List.of(MOCK_CLASS_1.toEntity()));

            var classes = classService.getClasses();

            assertThat(classes).hasSize(1);
            var xiaogjClass = classes.getFirst();
            assertThat(xiaogjClass.id()).isEqualTo(MOCK_CLASS_1.classId());
            assertThat(xiaogjClass.name()).isEqualTo(MOCK_CLASS_1.name());
            assertThat(xiaogjClass.headMasterId()).isEqualTo(MOCK_CLASS_1.headmasterId());
            assertThat(xiaogjClass.shiftId()).isEqualTo(MOCK_CLASS_1.shiftId());
            assertThat(xiaogjClass.isFinished()).isEqualTo(false);
        }
    }

    @Nested
    class GetClassesByCampusOfTeacher {
        @Test
        void should_get_all_classes_when_have_binding_xiaogj_department() {
            when(falconClient.getTeacherProfile()).thenReturn(
                    new TeacherProfileDto(
                            "teacherId",
                            "name",
                            "123",
                            "123.png",
                            "http://",
                            "school",
                            "0",
                            List.of(new SchoolCampusDto(
                                    "schoolCampusId1",
                                    "schoolCampus",
                                    false
                            ))
                    )
            );
            when(schoolCampusBindingsRepository.findByschoolCampusIdIn(List.of("schoolCampusId1")))
                    .thenReturn(List.of(
                            new SchoolCampusBindingsEntity(
                                    "id",
                                    "schoolCampusId1",
                                    "departmentId1",
                                    LocalDateTime.now(),
                                    LocalDateTime.now()
                            )
                    ));

            when(xiaoguanjiaClassRepository.findAllByIsFinishedAndDeletedAndStatusAndCampusIdIn(false, false, 1, List.of("departmentId1"))).thenReturn(List.of(MOCK_CLASS_1.toEntity()));

            var classes = classService.getClassesByCampusOfTeacher();
            assertThat(classes).hasSize(1);
            var xiaogjClass = classes.getFirst();
            assertThat(xiaogjClass.id()).isEqualTo(MOCK_CLASS_1.classId());
            assertThat(xiaogjClass.name()).isEqualTo(MOCK_CLASS_1.name());
            assertThat(xiaogjClass.headMasterId()).isEqualTo(MOCK_CLASS_1.headmasterId());
            assertThat(xiaogjClass.shiftId()).isEqualTo(MOCK_CLASS_1.shiftId());
            assertThat(xiaogjClass.isFinished()).isEqualTo(false);
        }

        @Test
        void should_get_all_classes_when_no_binding_xiaogj_department() {
            when(falconClient.getTeacherProfile()).thenReturn(
                    new TeacherProfileDto(
                            "teacherId",
                            "name",
                            "123",
                            "123.png",
                            "http://",
                            "school",
                            "0",
                            Collections.emptyList()
                    )
            );
            when(schoolCampusBindingsRepository.findByschoolCampusIdIn(Collections.emptyList()))
                    .thenReturn(Collections.emptyList());

            when(xiaoguanjiaClassRepository.findAllByIsFinishedAndDeletedAndStatus(false, false, 1)).thenReturn(List.of(MOCK_CLASS_1.toEntity()));

            var classes = classService.getClassesByCampusOfTeacher();
            assertThat(classes).hasSize(1);
            var xiaogjClass = classes.getFirst();
            assertThat(xiaogjClass.id()).isEqualTo(MOCK_CLASS_1.classId());
            assertThat(xiaogjClass.name()).isEqualTo(MOCK_CLASS_1.name());
            assertThat(xiaogjClass.headMasterId()).isEqualTo(MOCK_CLASS_1.headmasterId());
            assertThat(xiaogjClass.shiftId()).isEqualTo(MOCK_CLASS_1.shiftId());
            assertThat(xiaogjClass.isFinished()).isEqualTo(false);
        }
    }

    @Nested
    class GetClassById {
        @Test
        void should_get_class_by_classId() {
            when(xiaoguanjiaClassRepository.findById("classId")).thenReturn(Optional.ofNullable(XiaoguanjiaClassEntity.builder().id("classId").build()));
            XiaoguanjiaClass foundClass = classService.getXiaoguanjiaClassByClassId("classId");

            assertThat(foundClass.id()).isEqualTo("classId");
        }

        @Test
        void should_throw_when_class_not_found() {
            when(xiaoguanjiaClassRepository.findById("classId")).thenReturn(Optional.empty());

            assertThatExceptionOfType(XiaoguanjiaClassNotExistException.class)
                    .isThrownBy(() -> classService.getXiaoguanjiaClassByClassId("classId"));
        }
    }

    @Nested
    class GetMarsLadderClassrooms {
        @Test
        void should_return_marsladder_classrooms_by_classId_successfully() {
            when(classroomBindingsRepository.findByXiaoguanjiaClassId("classId"))
                    .thenReturn(List.of(new ClassroomBindingsEntity(null, CLASSROOM_ID1, List.of("classId"), null, null)));

            var classroomList = classService.getMarsladderClassrooms("classId");
            assertThat(classroomList.size()).isEqualTo(1);
            assertThat(classroomList.getFirst().classroomId()).isEqualTo(CLASSROOM_ID1);
        }
    }

    @Nested
    class GetStudentClassTasksFeedback {
        @Test
        void should_return_student_tasks_feedback_successfully_when_profile_is_realus_student() {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_STUDENT))
                            .realusStudentProfile(RealusStudentProfile.builder().xiaogjStudentId("studentId").userId("userId").build())
                            .build());
            when(studentClassTasksPhaseFeedbackRepository.findByMarsladderStudentIdAndClassIdOrderByStartTimeDesc("userId", "classId"))
                    .thenReturn(List.of(
                            new StudentClassTasksPhaseFeedbackEntity("id", "classId", List.of("classroomId"), "userId", "A", "hao hao hao", "好好好",
                                    LocalDateTime.of(2024, 6, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 7, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 6, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 6, 7, 8, 0, 0)),
                            new StudentClassTasksPhaseFeedbackEntity("id1", "classId1", List.of("classroomId2"), "userId", "A", "hao hao hao", "好好好",
                                    LocalDateTime.of(2024, 7, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 8, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 8, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 8, 7, 8, 0, 0))));

            var studentClassTasksFeedBack = classService.getStudentClassTasksFeedBack("classId", "studentId");
            assertThat(studentClassTasksFeedBack.size()).isEqualTo(2);
            assertThat(studentClassTasksFeedBack.getFirst().id()).isEqualTo("id");
            assertThat(studentClassTasksFeedBack.getFirst().classId()).isEqualTo("classId");
            assertThat(studentClassTasksFeedBack.getFirst().marsladderStudentId()).isEqualTo("userId");
            assertThat(studentClassTasksFeedBack.getFirst().xiaogjStudentId()).isEqualTo("studentId");
            assertThat(studentClassTasksFeedBack.getFirst().overallGrading()).isEqualTo("A");

            assertThat(studentClassTasksFeedBack.get(1).id()).isEqualTo("id1");
            assertThat(studentClassTasksFeedBack.get(1).classId()).isEqualTo("classId1");
            assertThat(studentClassTasksFeedBack.get(1).marsladderStudentId()).isEqualTo("userId");
            assertThat(studentClassTasksFeedBack.get(1).xiaogjStudentId()).isEqualTo("studentId");
            assertThat(studentClassTasksFeedBack.get(1).overallGrading()).isEqualTo("A");
        }

        @Test
        void should_return_student_tasks_feedback_successfully_when_profile_is_realus_parent() {
            UserDto userDto = new UserDto("parentId", "email", List.of(UserRole.REALUS_PARENT), null,
                    new RealusParentProfile("avatar", List.of(new RealusStudentProfile("userId", "nickName", "avatar", "studentId"))));
            when(falconClient.getUser()).thenReturn(userDto);
            when(studentClassTasksPhaseFeedbackRepository.findByMarsladderStudentIdAndClassIdOrderByStartTimeDesc("userId", "classId"))
                    .thenReturn(List.of(
                            new StudentClassTasksPhaseFeedbackEntity("id", "classId", List.of("classroomId"), "userId", "A", "hao hao hao", "好好好",
                                    LocalDateTime.of(2024, 6, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 7, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 6, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 6, 7, 8, 0, 0)),
                            new StudentClassTasksPhaseFeedbackEntity("id1", "classId1", List.of("classroomId2"), "userId", "A", "hao hao hao", "好好好",
                                    LocalDateTime.of(2024, 7, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 8, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 8, 7, 8, 0, 0),
                                    LocalDateTime.of(2024, 8, 7, 8, 0, 0))));

            var studentClassTasksFeedBack = classService.getStudentClassTasksFeedBack("classId", "studentId");
            assertThat(studentClassTasksFeedBack.size()).isEqualTo(2);
            assertThat(studentClassTasksFeedBack.getFirst().id()).isEqualTo("id");
            assertThat(studentClassTasksFeedBack.getFirst().classId()).isEqualTo("classId");
            assertThat(studentClassTasksFeedBack.getFirst().marsladderStudentId()).isEqualTo("userId");
            assertThat(studentClassTasksFeedBack.getFirst().xiaogjStudentId()).isEqualTo("studentId");
            assertThat(studentClassTasksFeedBack.getFirst().overallGrading()).isEqualTo("A");

            assertThat(studentClassTasksFeedBack.get(1).id()).isEqualTo("id1");
            assertThat(studentClassTasksFeedBack.get(1).classId()).isEqualTo("classId1");
            assertThat(studentClassTasksFeedBack.get(1).marsladderStudentId()).isEqualTo("userId");
            assertThat(studentClassTasksFeedBack.get(1).xiaogjStudentId()).isEqualTo("studentId");
            assertThat(studentClassTasksFeedBack.get(1).overallGrading()).isEqualTo("A");
        }

        @Test
        void should_return_student_tasks_feedback_successfully_when_not_founded_marsladder_user_id() {
            when(falconClient.getUser()).thenReturn(
                    UserDto.builder()
                            .roles(List.of(UserRole.REALUS_STUDENT))
                            .realusStudentProfile(RealusStudentProfile.builder().xiaogjStudentId("studentId").userId("userId").build())
                            .build());

            var studentClassTasksFeedBack = classService.getStudentClassTasksFeedBack("classId", "mockStudentId");
            assertThat(studentClassTasksFeedBack.size()).isEqualTo(0);
        }
    }

    @Nested
    class GenerateClassTasksPhaseFeedback {
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 1, 1, 1);
        LocalDateTime endTime = LocalDateTime.of(2024, 1, 31, 1, 1, 1);

        @Test
        void should_return_student_tasks_feedback_successfully() {
            var classroomBindingsEntities = List.of(
                    new ClassroomBindingsEntity("id1", "classroomId1", List.of("xiaogjClassId1"), null, null),
                    new ClassroomBindingsEntity("id2", "classroomId2", List.of("xiaogjClassId1"), null, null)
            );
            when(classroomBindingsRepository.findAll(PageRequest.of(0, 50)))
                    .thenReturn(new PageImpl<>(classroomBindingsEntities, PageRequest.of(0, 50), classroomBindingsEntities.size()));

            when(falconClient.getClassroom("classroomId1"))
                    .thenReturn(new MarsladderClassroom("classroomId1", "00000024-0004-0028-0000-000000000001"));
            when(falconClient.getClassroom("classroomId2"))
                    .thenReturn(new MarsladderClassroom("classroomId2", "00000024-0004-0026-0000-000000000001"));

            when(xiaoguanjiaClassRepository.findById("xiaogjClassId1"))
                    .thenReturn(Optional.of(XiaoguanjiaClassEntity.builder().subjectId("0001").gradeId("001").build()));

            when(xiaoguanjiaSubjectRepository.findByIdAndStatus("0001", ENABLED))
                    .thenReturn(Optional.of(new XiaoguanjiaSubjectEntity(
                            "0001",
                            "math",
                            ENABLED,
                            MOCK_TIME,
                            MOCK_TIME
                    )));
            when(xiaoguanjiaGradeRepository.findByIdAndStatus("001", ENABLED))
                    .thenReturn(Optional.of(new XiaoguanjiaGradeEntity(
                            "001",
                            "Y6",
                            ENABLED,
                            MOCK_TIME,
                            MOCK_TIME
                    )));

            var task1 = new MathTaskStatistic("taskId1", "classroomId1", List.of("subTopic"), 80d, 90d, false, "2024-01-01");
            var task2 = new MathTaskStatistic("taskId2", "classroomId1", List.of("subTopic"), 50d, 90d, false, "2024-01-01");
            var task12 = new MathTaskStatistic("taskId3", "classroomId1", List.of("subTopic"), 50d, 90d, false, "2024-01-01");

            var classPerformance = new ClassPerformanceByType(new ClassPerformance(80d), new ClassPerformance(40d));
            var taskStaticDto1 = createMathTaskStatisticForAiFeedback("userId1", List.of(task1), classPerformance);
            var taskStaticDto2 = createMathTaskStatisticForAiFeedback("userId2", List.of(task2), classPerformance);
            var taskStaticDto12 = createMathTaskStatisticForAiFeedback("userId2", List.of(task12), classPerformance);

            when(falconClient.getContentForGenerateTaskFeedback(List.of("classroomId1"), startTime, endTime))
                    .thenReturn(List.of(taskStaticDto1, taskStaticDto2, taskStaticDto12));
            when(kuixingClient.getAiFeedbackForMath(new MathTaskStatisticRequest(new XiaogjClassInfo("math", "Y6"), new MathTaskStatisticByType(List.of(task1), List.of()), classPerformance)))
                    .thenReturn(createAiFeedbackDto("feedback1"));
            when(kuixingClient.getAiFeedbackForMath(new MathTaskStatisticRequest(new XiaogjClassInfo("math", "Y6"), new MathTaskStatisticByType(List.of(task2), List.of()), classPerformance)))
                    .thenReturn(ResponseEntity.badRequest().build());
            when(kuixingClient.getAiFeedbackForMath(new MathTaskStatisticRequest(new XiaogjClassInfo("math", "Y6"), new MathTaskStatisticByType(List.of(task12), List.of()), classPerformance)))
                    .thenThrow(new RuntimeException("Bad request occurred while getting AI feedback"));

            var task3 = new MultiSubjectTaskStatistic("task3", TaskType.HOMEWORK, "classroomId2", 100d, List.of(), false, "A", null, "2024-01-01");
            var task4 = new MultiSubjectTaskStatistic("task4", TaskType.HOMEWORK, "classroomId2", 100d, List.of(), false, "A", null, "2024-01-01");
            var task34 = new MultiSubjectTaskStatistic("task34", TaskType.HOMEWORK, "classroomId2", 100d, List.of(), false, "A", null, "2024-01-01");
            var taskStaticDto4 = createMultiSubjectTaskStatisticForAiFeedback("userId3", List.of(task4), classPerformance);
            var taskStaticDto3 = createMultiSubjectTaskStatisticForAiFeedback("userId3", List.of(task3), classPerformance);
            var taskStaticDto34 = createMultiSubjectTaskStatisticForAiFeedback("userId3", List.of(task34), classPerformance);

            when(nezhaClient.getContentForGenerateTaskFeedback(List.of("classroomId2"), startTime, endTime))
                    .thenReturn(List.of(taskStaticDto3, taskStaticDto4, taskStaticDto34));
            when(kuixingClient.getAiFeedbackForMultiSubject(new MultiSubjectTaskStatisticRequest(new XiaogjClassInfo("math", "Y6"), List.of(task4), classPerformance)))
                    .thenReturn(createAiFeedbackDto("feedback4"));
            when(kuixingClient.getAiFeedbackForMultiSubject(new MultiSubjectTaskStatisticRequest(new XiaogjClassInfo("math", "Y6"), List.of(task3), classPerformance)))
                    .thenReturn(ResponseEntity.badRequest().build());
            when(kuixingClient.getAiFeedbackForMultiSubject(new MultiSubjectTaskStatisticRequest(new XiaogjClassInfo("math", "Y6"), List.of(task34), classPerformance)))
                    .thenThrow(new RuntimeException("Bad request occurred while getting AI feedback"));

            classService.generateTaskFeedbacksForBoundedClasses(startTime, endTime);

            verify(studentClassTasksPhaseFeedbackRepository).save(
                    new StudentClassTasksPhaseFeedbackEntity(null, "xiaogjClassId1", List.of("classroomId1"), "userId1", "B", "feedback1", "中文feedback", null, null, null, null)
            );
            verify(studentClassTasksPhaseFeedbackRepository).save(
                    new StudentClassTasksPhaseFeedbackEntity(null, "xiaogjClassId1", List.of("classroomId2"), "userId3", "B", "feedback4", "中文feedback", null, null, null, null)
            );
        }

        @Nested
        class GenerateTaskFeedbackForStudentInClass {
            LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 1, 1, 1);
            LocalDateTime endTime = LocalDateTime.of(2024, 1, 31, 1, 1, 1);

            @Test
            void should_return_student_task_feedback_successfully() {
                var classroomBindingsEntities = List.of(
                        new ClassroomBindingsEntity("id1", "classroomId1", List.of("classId"), null, null),
                        new ClassroomBindingsEntity("id2", "classroomId2", List.of("classId"), null, null)
                );
                when(classroomBindingsRepository.findByXiaoguanjiaClassId("classId")).thenReturn(classroomBindingsEntities);
                when(falconClient.getClassroom("classroomId1"))
                        .thenReturn(new MarsladderClassroom("classroomId1", "00000024-0004-0028-0000-000000000001"));
                when(falconClient.getClassroom("classroomId2"))
                        .thenReturn(new MarsladderClassroom("classroomId2", "00000024-0004-0026-0000-000000000001"));

                classService.generateTaskFeedbackForStudentInClass("classId", "studentId", startTime, endTime);

                verify(falconClient).getContentForGenerateTaskFeedback(List.of("classroomId1"), startTime, endTime);
                verify(nezhaClient).getContentForGenerateTaskFeedback(List.of("classroomId2"), startTime, endTime);
            }
        }

        private MathTaskStatisticForAiFeedback createMathTaskStatisticForAiFeedback(String userId, List<MathTaskStatistic> tasks, ClassPerformanceByType classPerformance) {
            return new MathTaskStatisticForAiFeedback(userId, new MathTaskStatisticByType(tasks, List.of()), classPerformance, null, null);
        }

        private MultiSubjectTaskStatisticForAiFeedback createMultiSubjectTaskStatisticForAiFeedback(String userId, List<MultiSubjectTaskStatistic> tasks, ClassPerformanceByType classPerformance) {
            return new MultiSubjectTaskStatisticForAiFeedback(userId, tasks, classPerformance, null, null);
        }

        private ResponseEntity<AiFeedbackDto> createAiFeedbackDto(String feedback) {
            return ResponseEntity.ok(AiFeedbackDto.builder().overallGrading("B").feedbackEn(feedback).feedbackCn("中文feedback").build());
        }
    }

}