package com.firstedu.marsladder.diting.xgjclass.service.impl;

import com.firstedu.marsladder.diting.xgjclass.exception.ClassUnitAlreadyCommentedException;
import com.firstedu.marsladder.diting.xgjclass.repository.ClassUnitCommentRepository;
import com.firstedu.marsladder.diting.xgjclass.repository.entity.ClassUnitCommentEntity;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_TIME;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ClassUnitServiceImplTest {

    @Mock
    private ClassUnitCommentRepository classUnitCommentRepository;

    @InjectMocks
    private ClassUnitServiceImpl classUnitService;

    @Nested
    class Comment {
        String userId = "72018217-03f7-44f2-8d3d-be1c906c7a23";
        String classUnitId = "bb9a4903-7092-420d-b740-870239594f37";
        String classUnitId1 = "bb9a4903-7092-420d-b740-870239594f37";
        String attendedBy = userId;
        float score = 4.5f;
        String comment = "comment";
        String commentId = "commentId";

        @Test
        void should_create_comment_when_not_commented_yet() {
            when(classUnitCommentRepository.findByAttendedByAndClassUnitId(userId, classUnitId))
                    .thenReturn(Optional.empty());

            var classUnitComment = new ClassUnitComment(null, userId, classUnitId, attendedBy, score, comment);
            when(classUnitCommentRepository.save(classUnitComment.toEntity())).thenReturn(
                    new ClassUnitCommentEntity(
                            commentId,
                            classUnitId,
                            attendedBy,
                            attendedBy,
                            score,
                            comment,
                            MOCK_TIME,
                            MOCK_TIME)
            );

            var commentCreated = classUnitService.comment(classUnitComment);

            assertThat(commentCreated.id()).isEqualTo(commentId);
            assertThat(commentCreated.commentedBy()).isEqualTo(attendedBy);
            assertThat(commentCreated.classUnitId()).isEqualTo(classUnitId);
            assertThat(commentCreated.attendedBy()).isEqualTo(attendedBy);
            assertThat(commentCreated.score()).isEqualTo(score);
            assertThat(commentCreated.comment()).isEqualTo(comment);
        }

        @Test
        void should_not_create_new_comment_on_class_unit_if_already_commented() {
            var existingComment = ClassUnitCommentEntity.builder().classUnitId(classUnitId).attendedBy(userId).build();
            when(classUnitCommentRepository.findByAttendedByAndClassUnitId(userId, classUnitId))
                    .thenReturn(Optional.of(existingComment));

            assertThatExceptionOfType(ClassUnitAlreadyCommentedException.class).isThrownBy(
                    () -> classUnitService.comment(new ClassUnitComment(null, userId, classUnitId, attendedBy, score, comment))
            );

            verify(classUnitCommentRepository, never()).save(any());
        }

        @Test
        void should_get_comment_given_class_unit_id_and_attended_by_user_id() {
            when(classUnitCommentRepository.findByAttendedByAndClassUnitId(attendedBy, classUnitId))
                    .thenReturn(Optional.of(ClassUnitCommentEntity.builder().score(score).comment(comment).build()));

            var classUnitComment = classUnitService.getComment(classUnitId, attendedBy);

            assertThat(classUnitComment.score()).isEqualTo(score);
            assertThat(classUnitComment.comment()).isEqualTo(comment);
        }

        @Test
        void should_get_empty_class_unit_comment_when_class_unit_is_not_commented() {
            when(classUnitCommentRepository.findByAttendedByAndClassUnitId(attendedBy, classUnitId))
                    .thenReturn(Optional.empty());

            var comment = classUnitService.getComment(classUnitId, attendedBy);

            assertThat(comment.score()).isNull();
            assertThat(comment.comment()).isNull();
        }

        @Test
        void should_get_comments_given_class_unit_ids_and_attended_by_user_id() {
            when(classUnitCommentRepository.findByAttendedByAndClassUnitIdIn(attendedBy, List.of(classUnitId, classUnitId1)))
                    .thenReturn(List.of(ClassUnitCommentEntity.builder().classUnitId(classUnitId).score(score).comment(comment).build()));

            var classUnitComment = classUnitService.getComments(List.of(classUnitId, classUnitId1), attendedBy);

            assertThat(classUnitComment.getFirst().score()).isEqualTo(score);
            assertThat(classUnitComment.getFirst().comment()).isEqualTo(comment);

            assertThat(classUnitComment.get(1).score()).isEqualTo(score);
            assertThat(classUnitComment.get(1).comment()).isEqualTo(comment);
        }
    }
}