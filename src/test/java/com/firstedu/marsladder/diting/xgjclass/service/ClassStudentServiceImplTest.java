package com.firstedu.marsladder.diting.xgjclass.service;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.shift.service.domain.Shift;
import com.firstedu.marsladder.diting.shift.service.exception.XiaoguanjiaShiftNotFoundException;
import com.firstedu.marsladder.diting.shift.service.impl.ShiftServiceImpl;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassStudentDetail;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import com.firstedu.marsladder.diting.xgjclass.service.impl.ClassStudentServiceImpl;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static com.firstedu.marsladder.diting.xiaoguanjia.ClassStubs.MOCK_TIME;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus.ENTERED;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus.REMOVED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ClassStudentServiceImplTest {
    @Mock
    ClassService classService;

    @Mock
    ClassTeacherService classTeacherService;

    @Mock
    ShiftServiceImpl shiftService;

    @Mock
    XiaoguanjiaClassStudentRepository xiaoguanjiaClassStudentRepository;

    @InjectMocks
    ClassStudentServiceImpl classStudentService;

    @Nested
    class GetMyStudentClasses {
        String shiftId = "shiftId";
        String shiftName = "shiftName";
        String className = "className";
        String teacherName = "teacherName";
        String studentId = "studentId";
        String classId = "fb6c27f9-3f60-44fd-a28e-6f17c67caa16";
        String classStudentId = "classStudentId";
        String gradeId = "gradeId";
        String subjectId = "subjectId";
        boolean isClassFinished = false;
        LocalDateTime registedAt = MOCK_TIME;

        @Test
        void should_get_my_class_student_details() {
            assumeClassStudentExists(classStudentId, classId, studentId, registedAt);
            assumeClassExists(classId, isClassFinished);
            assumeShiftExists(shiftId, shiftName);
            assumeTeacherExists(classId, teacherName);

            List<ClassStudentDetail> studentClassDetails = classStudentService.getAllClassStudentDetailsByStudentId(studentId);

            assertThat(studentClassDetails)
                    .isEqualTo(List.of(new ClassStudentDetail(
                            gradeId,
                            subjectId,
                            shiftId,
                            shiftName,
                            ShiftStatus.ENABLED,
                            classId,
                            className,
                            MOCK_TIME,
                            isClassFinished,
                            teacherName,
                            classStudentId,
                            ENTERED,
                            registedAt
                    )));
        }

        @Test
        void should_return_empty_list_when_class_not_found() {
            assumeClassStudentExists(classStudentId, classId, studentId, registedAt);
            when(classService.getXiaoguanjiaClassByClassId(classId))
                    .thenThrow(new XiaoguanjiaClassNotExistException("Class not found with id: " + classId));

            List<ClassStudentDetail> studentClassDetails = classStudentService.getAllClassStudentDetailsByStudentId(studentId);

            assertThat(studentClassDetails).isEmpty();
        }

        @Test
        void should_return_empty_list_when_shift_not_found() {
            assumeClassStudentExists(classStudentId, classId, studentId, registedAt);
            assumeClassExists(classId, isClassFinished);
            when(shiftService.findShiftByShiftId(shiftId))
                    .thenThrow(new XiaoguanjiaShiftNotFoundException("Shift not found with id: " + shiftId));

            List<ClassStudentDetail> studentClassDetails = classStudentService.getAllClassStudentDetailsByStudentId(studentId);

            assertThat(studentClassDetails).isEmpty();
        }

        private void assumeTeacherExists(String classId, String teacherName) {
            when(classTeacherService.getTeacherName(classId)).thenReturn(teacherName);
        }

        private void assumeShiftExists(String shiftId, String shiftName) {
            when(shiftService.findShiftByShiftId(shiftId)).thenReturn(new Shift(shiftId, shiftName, ShiftStatus.ENABLED));
        }

        private void assumeClassStudentExists(String classStudentId, String classId, String studentId, LocalDateTime registedAt) {
            when(xiaoguanjiaClassStudentRepository.findByStudentId(studentId)).thenReturn(
                    List.of(new XiaoguanjiaClassStudentEntity(classStudentId, classId, studentId, ENTERED, registedAt, MOCK_TIME, "causeId", "outMemo", "outType", MOCK_TIME, MOCK_TIME))
            );
        }

        private void assumeClassExists(String classId, Boolean isFinished) {
            when(classService.getXiaoguanjiaClassByClassId(classId))
                    .thenReturn(new XiaoguanjiaClass(
                            classId,
                            className,
                            null,
                            subjectId,
                            shiftId,
                            isFinished,
                            MOCK_TIME,
                            gradeId));
        }
    }

    @Nested
    class GetClassStudentsByStudentId {
        @Test
        void should_get_class_students() {
            var latestRecordCreatedAt = LocalDateTime.of(2024, 12, 1, 0, 0, 0);
            var removedEntity = XiaoguanjiaClassStudentEntity
                    .builder()
                    .id("id1")
                    .classId("mockClassId")
                    .studentId("studentId")
                    .status(REMOVED)
                    .createdAt(latestRecordCreatedAt)
                    .build();

            var studentClassJoinedAt = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
            var enteredEntity = XiaoguanjiaClassStudentEntity
                    .builder()
                    .id("id2")
                    .classId("mockClassId")
                    .studentId("studentId")
                    .status(ENTERED)
                    .createdAt(studentClassJoinedAt)
                    .build();

            when(xiaoguanjiaClassStudentRepository.findByStudentId("studentId"))
                    .thenReturn(List.of(removedEntity, enteredEntity));

            var classStudentList = classStudentService.getLatestClassStudentRecordsInAllClasses("studentId");

            assertThat(classStudentList).hasSize(1);
            var classStudentRecord = classStudentList.getFirst();
            assertThat(classStudentRecord.id()).isEqualTo("id1");
            assertThat(classStudentRecord.classId()).isEqualTo("mockClassId");
            assertThat(classStudentRecord.status()).isEqualTo(REMOVED);
            assertThat(classStudentRecord.classJoinedAt()).isEqualTo(studentClassJoinedAt);
        }
    }

}