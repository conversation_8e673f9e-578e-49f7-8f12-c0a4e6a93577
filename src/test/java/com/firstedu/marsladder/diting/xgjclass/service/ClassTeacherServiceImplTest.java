package com.firstedu.marsladder.diting.xgjclass.service;

import com.firstedu.marsladder.diting.employee.service.EmployeeService;
import com.firstedu.marsladder.diting.employee.service.domain.Employee;
import com.firstedu.marsladder.diting.xgjclass.service.impl.ClassTeacherServiceImpl;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassTeacherRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassTeacherEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.TeacherRole;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ClassTeacherServiceImplTest {
    @Mock
    private EmployeeService employeeService;

    @Mock
    private XiaoguanjiaClassTeacherRepository xiaoguanjiaClassTeacherRepository;

    @InjectMocks
    ClassTeacherServiceImpl classTeacherService;

    @Test
    void should_get_teacher_name() {
        when(xiaoguanjiaClassTeacherRepository.findByClassIdAndRole("classId", TeacherRole.TEACHER))
                .thenReturn(XiaoguanjiaClassTeacherEntity.builder().employeeId("headTeacherId").build());
        when(employeeService.getEmployeeById("headTeacherId")).thenReturn(new Employee("headTeacherId", "Nick"));

        String headTeacherName = classTeacherService.getTeacherName("classId");

        assertThat(headTeacherName).isEqualTo("Nick");
    }

    @Test
    void should_return_null_for_teacher_name_when_head_teacher_not_set_for_class() {
        when(xiaoguanjiaClassTeacherRepository.findByClassIdAndRole("classId", TeacherRole.TEACHER))
                .thenReturn(null);

        assertThat(classTeacherService.getTeacherName("classId")).isEqualTo(null);
    }

}