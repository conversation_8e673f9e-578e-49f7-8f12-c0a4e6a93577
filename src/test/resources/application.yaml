spring:
  application:
    name: diting
  flyway:
    locations: filesystem:/tmp/diting-db-migration/sql
  datasource:
    url: jdbc:tc:mysql:8.0.36:///diting?TC_REUSABLE=true
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_5XijcEWvj/.well-known/jwks.json

redis:
  url:
    host: ${embedded.redis.host}
    port: ${embedded.redis.port}


klaviyo:
  host: placeholder
  private-api-key: placeholder
  realus-lead-all-list: placeholder
  vce-mock-lead-all-list: placeholder

sso:
  logoutEndpoint: logoutEndpoint
  clientId: clientId
  logoutUri: logoutUri

client:
  falcon:
    url: http://localhost:8080
  xiaoguanjia:
    url: http://localhost:8080
    appid: appid
    secret: secret
    event-token: event-token
