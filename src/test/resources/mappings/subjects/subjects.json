{"request": {"method": "GET", "url": "/base/get_dict?access_token=placeholder&type=SHIFT_SUBJECT"}, "response": {"status": 200, "jsonBody": {"data": [{"id": "850a3961-0461-4524-b7d5-56a00cef1101", "createtime": "20160708160528", "updatetime": "20180221110054", "value": "数学", "code": "", "issys": 0, "describe": "", "status": 1}, {"id": "41b558a3-77dc-480b-bfed-00eefb86bd02", "createtime": "20160708160528", "updatetime": "20180221110057", "value": "英语", "code": "", "issys": 0, "describe": "", "status": 1}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}