{"request": {"method": "GET", "url": "/edu/get_course_batch?access_token=placeholder&updatetime=20240101&timeflag=2&offset=1&size=100"}, "response": {"status": 200, "jsonBody": {"has_more": 0, "data": [{"course_id": "fecef799-2b7b-46e3-9f5a-fa236a012785", "createtime": "20240227153742", "updatetime": "20240303074352", "shift_id": "d9d700db-1805-4f36-a967-686303852a6e", "class_id": "fb6c27f9-3f60-44fd-a28e-6f17c67caa16", "starttime": "20240303100000", "endtime": "20240303120000", "classroom_id": "a279e605-7c3d-4d6e-bf0b-8ef5c0a52d8f", "isfinished": 1, "isnotify": 0, "is_reverse_begin": 0, "plan_id": "465b203f-24ab-4ca1-a68f-44f6210ad789", "content": "", "confirm_time": "20240303074352", "confirm_user_id": "32b3d392-0da3-46d9-9c01-ddbcb0bb95b4", "shift_subject_id": "00000000-0000-0000-0000-000000000000", "shift_schedule_id": "00000000-0000-0000-0000-000000000000", "shift_amount": 0, "is_send_worktask": 0, "is_dynamic_consume": 0, "standard_minutes": 0.0, "describe": "", "type": 1}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}