{"request": {"method": "GET", "url": "/edu/get_class_student_list?access_token=placeholder&class_id=66f275fd-d959-4980-9330-a270f4c8b3cb"}, "response": {"status": 200, "jsonBody": {"has_more": 1, "data": [{"createtime": "20240731163014", "updatetime": "20241218235959", "student_id": "f1d6980e-0bb5-4a69-b498-d14285ee912c", "status": 1, "indate": "20240731000000", "outdate": "20241217235959", "out_cause_id": "00000000-0000-0000-0000-000000000000", "out_memo": "", "out_type": "", "id": "3d3b29c8-bf34-460f-87e9-8322fac8bbf4"}, {"createtime": "20241218235959", "updatetime": "20241218235959", "student_id": "f1d6980e-0bb5-4a69-b498-d14285ee912c", "status": 0, "indate": "20241218000000", "outdate": "20990101000000", "out_cause_id": "00000000-0000-0000-0000-000000000000", "out_memo": "结业", "out_type": "Finish", "id": "4a39ee20-78ba-41d7-bdab-ce6b0f558e11"}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}