{"request": {"method": "GET", "url": "/edu/get_class_student_list?access_token=placeholder&class_id=251a3cf7-a099-426e-af9b-2139c60ab917"}, "response": {"status": 200, "jsonBody": {"has_more": 1, "data": [{"createtime": "20240807081825", "updatetime": "20240807081825", "student_id": "57ce7b60-7bd1-44b3-97de-4d56b99edf8f", "status": 1, "indate": "20240807000000", "outdate": "20990101000000", "out_cause_id": "00000000-0000-0000-0000-000000000000", "out_memo": "", "out_type": "", "id": "6b1eaf22-cc5f-4dd0-8913-78414a985d73"}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}