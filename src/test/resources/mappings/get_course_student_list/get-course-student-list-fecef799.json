{"request": {"method": "GET", "url": "/edu/get_course_student_list?access_token=placeholder&course_id=fecef799-2b7b-46e3-9f5a-fa236a012785"}, "response": {"status": 200, "jsonBody": {"data": [{"createtime": "20240303074352", "student_id": "babb2ecf-61af-4954-ab5c-8aa0808834b6", "isattend": 1, "cost": 120.0, "mend_course_id": "00000000-0000-0000-0000-000000000000", "student_absentcause_id": "00000000-0000-0000-0000-000000000000", "master_id": "bca273ce-d33a-4053-9e63-d721f4f60831", "istry": 0, "ismend": 0, "isnotify": 0, "isadjusted": 0, "adjust_course_id": "00000000-0000-0000-0000-000000000000", "confirm_time": "20240303074352", "confirm_user_id": "32b3d392-0da3-46d9-9c01-ddbcb0bb95b4", "leave_describe": "", "leave_date": "00010101000000", "describe": "", "studetname": "<PERSON><PERSON>"}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}