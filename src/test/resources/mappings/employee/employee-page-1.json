{"request": {"method": "GET", "url": "/org/get_employee_batch?access_token=placeholder&updatetime=20241001&offset=1&size=2"}, "response": {"status": 200, "jsonBody": {"has_more": 0, "data": [{"employee_id": "eb1dfa53-9777-4aa2-8d61-f2b4945314cd", "createtime": "20241114063719", "updatetime": "20241114063833", "name": "测试老师", "nickname": "测试老师", "serial": "E0340", "username": "测试老师@firstedu", "levelstring": "|", "headimg": "", "idnumber": "", "sex": 1, "contact": "", "email": "", "qq": "", "smstel": "", "postiontype": 0, "isemployee": 1, "be_employee_date": "00010101", "is_class_teacher": 0, "is_login_user": 1, "is_binding_dingtalk": 0, "shift_subject_id": "00000000-0000-0000-0000-000000000000", "level_id": "00000000-0000-0000-0000-000000000000", "indate": "20241114", "outdate": "00010101", "outcause_id": "00000000-0000-0000-0000-000000000000", "status": 1, "describe": "", "user_status": 1, "field1": "", "field2": "", "field3": "", "field4": "", "field5": "", "field6": "", "field7": "", "field8": "", "field9": "", "field10": ""}, {"employee_id": "19fe3071-893b-44ed-b26c-c54b6e29b3fe", "createtime": "20220929141342", "updatetime": "20241219102127", "name": "<PERSON><PERSON>", "nickname": "<PERSON><PERSON>", "serial": "E0213", "username": "<PERSON><PERSON>@firstedu", "levelstring": "|", "headimg": "", "idnumber": "", "sex": 1, "contact": "", "email": "", "qq": "", "smstel": "", "postiontype": 1, "isemployee": 1, "be_employee_date": "00010101", "is_class_teacher": 0, "is_login_user": 1, "is_binding_dingtalk": 0, "shift_subject_id": "00000000-0000-0000-0000-000000000000", "level_id": "6efecad1-96e4-4916-8760-e0dec3275c24", "indate": "20220929", "outdate": "20241219", "outcause_id": "00000000-0000-0000-0000-000000000000", "status": 0, "describe": "", "user_status": 0, "field1": "", "field2": "", "field3": "", "field4": "", "field5": "", "field6": "", "field7": "", "field8": "", "field9": "", "field10": ""}, {"employee_id": "fe07ac35-9cef-4d97-83ea-a8a610a11de3", "createtime": "20240719135502", "updatetime": "20241218190031", "name": "<PERSON>", "nickname": "<PERSON>", "serial": "E0318", "username": "<PERSON>@firstedu", "levelstring": "|", "headimg": "", "idnumber": "", "sex": 2, "contact": "", "email": "", "qq": "", "smstel": "406095898", "postiontype": 0, "isemployee": 1, "be_employee_date": "20240719", "is_class_teacher": 0, "is_login_user": 1, "is_binding_dingtalk": 0, "shift_subject_id": "00000000-0000-0000-0000-000000000000", "level_id": "00000000-0000-0000-0000-000000000000", "indate": "20240719", "outdate": "20241218", "outcause_id": "00000000-0000-0000-0000-000000000000", "status": 0, "describe": "", "user_status": 0, "field1": "", "field2": "", "field3": "", "field4": "", "field5": "", "field6": "", "field7": "", "field8": "", "field9": "", "field10": ""}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}