{"request": {"method": "GET", "url": "/edu/get_class_teacher_list?access_token=placeholder&class_id=66f275fd-d959-4980-9330-a270f4c8b3cb"}, "response": {"status": 200, "jsonBody": {"data": [{"createtime": "20240804143238", "employee_id": "a3a627c1-c550-43b0-8a6b-934ee2049c86", "role": 1, "subject_id": "00000000-0000-0000-0000-000000000000", "types": []}], "errcode": 0, "errmsg": ""}, "headers": {"Content-Type": "application/json"}}}