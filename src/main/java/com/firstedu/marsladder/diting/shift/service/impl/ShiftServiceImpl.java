package com.firstedu.marsladder.diting.shift.service.impl;

import com.firstedu.marsladder.diting.shift.service.ShiftService;
import com.firstedu.marsladder.diting.shift.service.domain.Shift;
import com.firstedu.marsladder.diting.shift.service.exception.XiaoguanjiaShiftNotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaShiftRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaShiftEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShiftServiceImpl implements ShiftService {
    private final XiaoguanjiaShiftRepository xiaoguanjiaShiftRepository;

    public Shift findShiftByShiftId(String shiftId) {
        Optional<XiaoguanjiaShiftEntity> shiftEntityOptional = xiaoguanjiaShiftRepository.findById(shiftId);
        if (shiftEntityOptional.isEmpty()) {
            log.error("Shift not found: {}", shiftId);
            throw new XiaoguanjiaShiftNotFoundException("Shift not found: " + shiftId);
        } else {
            return Shift.from(shiftEntityOptional.get());
        }
    }
}
