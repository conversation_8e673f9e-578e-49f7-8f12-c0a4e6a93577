package com.firstedu.marsladder.diting.shift.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaShiftEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;

public record Shift(
        String id,
        String name,
        ShiftStatus status
) {
    public static Shift from(XiaoguanjiaShiftEntity shiftEntity) {
        return new Shift(
                shiftEntity.getId(),
                shiftEntity.getName(),
                shiftEntity.getStatus()
        );
    }
}
