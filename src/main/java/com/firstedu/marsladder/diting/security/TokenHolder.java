package com.firstedu.marsladder.diting.security;

import com.firstedu.marsladder.diting.security.domain.Token;
import com.firstedu.marsladder.diting.security.exception.TokenNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TokenHolder {

    private static final String AMAZON_OIDC_DATA_TOKEN_HEADER1 = "x-amzn-oidc-data";
    private static final String AMAZON_OIDC_DATA_TOKEN_HEADER2 = "amazon-oidc-data-token";
    private final HttpServletRequest request;

    public TokenHolder(HttpServletRequest request) {
        this.request = request;
    }

    public Token getToken() {
        var authenticationToken = (OAuth2AccessToken) SecurityContextHolder.getContext().getAuthentication().getCredentials();
        var idToken = Optional.ofNullable(request.getHeader(AMAZON_OIDC_DATA_TOKEN_HEADER1)).orElse(request.getHeader(AMAZON_OIDC_DATA_TOKEN_HEADER2));
        var accessToken = authenticationToken.getTokenValue();

        if (idToken == null || accessToken == null) {
            throw new TokenNotFoundException("Required tokens are missing in the request headers.");
        }

        return new Token(idToken, accessToken);
    }
}
