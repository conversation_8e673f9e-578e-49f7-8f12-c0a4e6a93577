package com.firstedu.marsladder.diting.security;

import com.firstedu.marsladder.diting.amazonoidc.AmazonOidcData;
import com.firstedu.marsladder.diting.amazonoidc.AmazonOidcDataTokenParser;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.UserProfile;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.DefaultOAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthentication;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

import static org.springframework.security.oauth2.core.OAuth2AccessToken.TokenType.BEARER;

@Component
public class BearerTokenAuthenticationBuilder {

    private final static String AUTHORITY_PREFIX = "ROLE_";
    private final AmazonOidcDataTokenParser amazonOidcDataTokenParser;
    private final FalconClient falconClient;

    public BearerTokenAuthenticationBuilder(
            AmazonOidcDataTokenParser amazonOidcDataTokenParser,
            FalconClient falconClient
    ) {
        this.amazonOidcDataTokenParser = amazonOidcDataTokenParser;
        this.falconClient = falconClient;
    }

    public BearerTokenAuthentication build(Jwt jwt, String idToken) {
        var userProfile = falconClient.getUserRoles(jwt.getTokenValue(), idToken);

        var amazonOidcData = amazonOidcDataTokenParser.parse(idToken);
        var authorities = buildAuthorities(userProfile, jwt);
        var principal = buildMarsLadderOAuth2AuthenticatedPrincipal(userProfile, amazonOidcData, authorities);
        var token = new OAuth2AccessToken(BEARER, jwt.getTokenValue(), jwt.getIssuedAt(), jwt.getExpiresAt());
        return new BearerTokenAuthentication(principal, token, authorities);
    }

    private RealusOAuth2AuthenticatedPrincipal buildMarsLadderOAuth2AuthenticatedPrincipal(UserProfile userProfile, AmazonOidcData amazonOidcData, Collection<GrantedAuthority> authorities) {
        var oAuth2AuthenticatedPrincipal = new DefaultOAuth2AuthenticatedPrincipal(
                userProfile.username(),
                Map.of("email", amazonOidcData.email()),
                authorities
        );
        return new RealusOAuth2AuthenticatedPrincipal(oAuth2AuthenticatedPrincipal, amazonOidcData.email());
    }

    private Collection<GrantedAuthority> buildAuthorities(UserProfile userProfile, Jwt jwt) {
        var additionalAuthorities = userProfile.roles().stream()
                .map(this::buildAuthority)
                .map(SimpleGrantedAuthority::new)
                .toList();
        var authorities = new JwtGrantedAuthoritiesConverter().convert(jwt);
        authorities.addAll(additionalAuthorities);
        return authorities;
    }

    private String buildAuthority(String role) {
        if (role.startsWith(AUTHORITY_PREFIX)) {
            return role;
        } else {
            return AUTHORITY_PREFIX + role.toUpperCase();
        }
    }
}
