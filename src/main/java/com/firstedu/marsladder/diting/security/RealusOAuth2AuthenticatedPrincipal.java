package com.firstedu.marsladder.diting.security;

import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.DefaultOAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;

import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

public class RealusOAuth2AuthenticatedPrincipal implements OAuth2AuthenticatedPrincipal, Serializable {
    private final DefaultOAuth2AuthenticatedPrincipal defaultOAuth2AuthenticatedPrincipal;
    @Getter
    private final String email;

    public RealusOAuth2AuthenticatedPrincipal(
            DefaultOAuth2AuthenticatedPrincipal defaultOAuth2AuthenticatedPrincipal,
            String email
    ) {
        this.defaultOAuth2AuthenticatedPrincipal = defaultOAuth2AuthenticatedPrincipal;
        this.email = email;
    }

    @Override
    public Map<String, Object> getAttributes() {
        return defaultOAuth2AuthenticatedPrincipal.getAttributes();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return defaultOAuth2AuthenticatedPrincipal.getAuthorities();
    }

    @Override
    public String getName() {
        return defaultOAuth2AuthenticatedPrincipal.getName();
    }
}
