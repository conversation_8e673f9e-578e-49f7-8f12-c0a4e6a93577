package com.firstedu.marsladder.diting.security;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.DefaultBearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.HeaderBearerTokenResolver;
import org.springframework.stereotype.Component;

@Component
public class AmzOidcBearerTokenResolver implements BearerTokenResolver {
    private final DefaultBearerTokenResolver defaultBearerTokenResolver;
    private final HeaderBearerTokenResolver amazonOidcAccessTokenResolver;

    public AmzOidcBearerTokenResolver() {
        defaultBearerTokenResolver = new DefaultBearerTokenResolver();
        amazonOidcAccessTokenResolver = new HeaderBearerTokenResolver("x-amzn-oidc-accesstoken");
    }

    @Override
    public String resolve(HttpServletRequest request) {
        var accessToken = defaultBearerTokenResolver.resolve(request);
        if (accessToken == null) {
            return amazonOidcAccessTokenResolver.resolve(request);
        }
        return accessToken;
    }
}