package com.firstedu.marsladder.diting.security;

import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtBearerTokenAuthenticationConverter;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

@Component
public class JwtAuthenticationTokenConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    private static final String HEADER_KEY_AMAZON_OIDC_DATA_TOKEN1 = "x-amzn-oidc-data";
    private static final String HEADER_KEY_AMAZON_OIDC_DATA_TOKEN2 = "amazon-oidc-data-token";
    private final HttpServletRequest request;
    private final BearerTokenAuthenticationBuilder bearerTokenAuthenticationBuilder;
    private final JwtBearerTokenAuthenticationConverter jwtBearerTokenAuthenticationConverter = new JwtBearerTokenAuthenticationConverter();

    public JwtAuthenticationTokenConverter(HttpServletRequest request, BearerTokenAuthenticationBuilder bearerTokenAuthenticationBuilder) {
        this.request = request;
        this.bearerTokenAuthenticationBuilder = bearerTokenAuthenticationBuilder;
    }

    @Override
    public AbstractAuthenticationToken convert(@NonNull Jwt jwt) {
        var idToken = Optional.ofNullable(request.getHeader(HEADER_KEY_AMAZON_OIDC_DATA_TOKEN1)).orElse(request.getHeader(HEADER_KEY_AMAZON_OIDC_DATA_TOKEN2));
        if (Objects.isNull(idToken)) {
            return jwtBearerTokenAuthenticationConverter.convert(jwt);
        } else {
            return bearerTokenAuthenticationBuilder.build(jwt, idToken);
        }
    }
}
