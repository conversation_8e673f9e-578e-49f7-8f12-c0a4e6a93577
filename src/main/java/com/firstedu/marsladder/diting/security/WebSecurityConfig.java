package com.firstedu.marsladder.diting.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;
import org.springframework.security.config.annotation.web.configurers.oauth2.server.resource.OAuth2ResourceServerConfigurer;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
public class WebSecurityConfig {
    private final JwtAuthenticationTokenConverter jwtAuthenticationConverter;
    private final AmzOidcBearerTokenResolver amzOidcBearerTokenResolver;

    public WebSecurityConfig(JwtAuthenticationTokenConverter jwtAuthenticationConverter, AmzOidcBearerTokenResolver amzOidcBearerTokenResolver) {
        this.jwtAuthenticationConverter = jwtAuthenticationConverter;
        this.amzOidcBearerTokenResolver = amzOidcBearerTokenResolver;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http.cors(AbstractHttpConfigurer::disable)
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(this::authorizeHttpRequests)
                .oauth2ResourceServer(this::configureOAuth2ResourceServer)
                .logout(AbstractHttpConfigurer::disable)
                .build();
    }

    public void configureOAuth2ResourceServer(OAuth2ResourceServerConfigurer<HttpSecurity> configurer) {
        configurer.bearerTokenResolver(amzOidcBearerTokenResolver).jwt(jwt -> jwt.jwtAuthenticationConverter(jwtAuthenticationConverter));
    }

    public void authorizeHttpRequests(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry authCustomizer) {
        authCustomizer.requestMatchers(HttpMethod.OPTIONS).permitAll()
                .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                .requestMatchers(
                        "/actuator/**",
                        "/public/**",
                        "/v3/api-docs/**",
                        "/swagger-ui/**",
                        "/swagger-ui.html",
                        "/all-bounded-classes/tasks/phase-feedback",
                        "/all-bounded-classes/tasks/phase-feedback")
                .permitAll()
                .requestMatchers(HttpMethod.POST, "/classes/*/tasks/phase-feedback").permitAll()
                .anyRequest()
                .authenticated();
    }
}
