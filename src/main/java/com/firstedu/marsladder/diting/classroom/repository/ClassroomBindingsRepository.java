package com.firstedu.marsladder.diting.classroom.repository;

import com.firstedu.marsladder.diting.classroom.repository.entity.ClassroomBindingsEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ClassroomBindingsRepository extends JpaRepository<ClassroomBindingsEntity, String> {
    Optional<ClassroomBindingsEntity> findByClassroomId(String classroomId);

    @Query(value = "SELECT * FROM classroom_bindings WHERE JSON_CONTAINS(xiaoguanjia_class_ids, JSON_QUOTE(:classId))", nativeQuery = true)
    List<ClassroomBindingsEntity> findByXiaoguanjiaClassId(String classId);
}
