package com.firstedu.marsladder.diting.classroom.controller;

import com.firstedu.marsladder.diting.classroom.controller.dto.BindClassRequest;
import com.firstedu.marsladder.diting.classroom.service.ClassroomService;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.XiaoguanjiaClassDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class ClassroomController {

    private final ClassroomService classroomService;

    @ResponseStatus(CREATED)
    @PostMapping("/classrooms/{classroomId}/bindings")
    public void bindClassrooms(
            @PathVariable String classroomId,
            @RequestBody BindClassRequest bindClassRequest
    ) {
        classroomService.bindClasses(bindClassRequest.toCommand(classroomId));
    }

    @ResponseStatus(OK)
    @GetMapping("/classrooms/{classroomId}/bindings")
    public List<XiaoguanjiaClassDto> getBoundXiaoguanjiaClasses(
            @PathVariable String classroomId
    ) {
        return XiaoguanjiaClassDto.listFrom(classroomService.getBoundClasses(classroomId));
    }
}
