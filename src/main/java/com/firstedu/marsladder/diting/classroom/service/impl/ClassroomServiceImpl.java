package com.firstedu.marsladder.diting.classroom.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.classroom.repository.ClassroomBindingsRepository;
import com.firstedu.marsladder.diting.classroom.service.ClassroomService;
import com.firstedu.marsladder.diting.classroom.service.domain.BindClassCommand;
import com.firstedu.marsladder.diting.classroom.service.domain.Classroom;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass.listFrom;

@Service
@RequiredArgsConstructor
public class ClassroomServiceImpl implements ClassroomService {
    private final XiaoguanjiaClassRepository xiaoguanjiaClassRepository;
    private final ClassroomBindingsRepository classroomBindingsRepository;

    @Override
    @Transactional
    public void bindClasses(BindClassCommand bindClassCommand) {
        for (var classId : bindClassCommand.classIds()) {
            if (!xiaoguanjiaClassRepository.existsById(classId)) {
                throw new XiaoguanjiaClassNotExistException("No such xiaoguanjia class id: " + bindClassCommand.classroomId());
            }
        }

        var existingBindingEntityOptional = classroomBindingsRepository.findByClassroomId(bindClassCommand.classroomId());
        if (existingBindingEntityOptional.isEmpty()) {
            classroomBindingsRepository.save(bindClassCommand.toEntity());
        } else {
            var existingBindingEntity = existingBindingEntityOptional.get();
            existingBindingEntity.setXiaoguanjiaClassIds(bindClassCommand.classIds());
            classroomBindingsRepository.save(existingBindingEntity);
        }
    }

    @Override
    public List<XiaoguanjiaClass> getBoundClasses(String classroomId) {
        return classroomBindingsRepository.findByClassroomId(classroomId)
                .map(entity -> listFrom(xiaoguanjiaClassRepository.findAllById(entity.getXiaoguanjiaClassIds())))
                .orElseGet(Collections::emptyList);
    }

    @Override
    public List<Classroom> getClassroomsByShiftId(String shiftId) {
        var classIdListForCourseId = xiaoguanjiaClassRepository.findByShiftIdAndDeletedAndStatus(shiftId, false, 1)
                .stream().map(XiaoguanjiaClassEntity::getId)
                .collect(Collectors.toSet());

        return classIdListForCourseId.stream()
                .flatMap(classId -> classroomBindingsRepository.findByXiaoguanjiaClassId(classId).stream()
                        .map(classroomBindingEntity -> new Classroom(classroomBindingEntity.getClassroomId()))
                )
                .distinct()
                .toList();
    }
}
