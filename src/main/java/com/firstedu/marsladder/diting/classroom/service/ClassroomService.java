package com.firstedu.marsladder.diting.classroom.service;

import com.firstedu.marsladder.diting.classroom.service.domain.BindClassCommand;
import com.firstedu.marsladder.diting.classroom.service.domain.Classroom;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;

import java.util.List;

public interface ClassroomService {
    void bindClasses(BindClassCommand bindClassCommand);

    List<XiaoguanjiaClass> getBoundClasses(String classroomId);

    List<Classroom> getClassroomsByShiftId(String shiftId);
}
