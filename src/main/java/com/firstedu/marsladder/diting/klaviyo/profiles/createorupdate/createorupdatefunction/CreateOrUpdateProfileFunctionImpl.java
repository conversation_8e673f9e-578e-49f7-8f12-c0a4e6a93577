package com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.createorupdatefunction;

import com.firstedu.marsladder.diting.klaviyo.KlaviyoApiRequestDto;
import com.firstedu.marsladder.diting.klaviyo.KlaviyoApiRequestDtoData;
import com.firstedu.marsladder.diting.klaviyo.KlaviyoApiResponseDto;
import com.firstedu.marsladder.diting.klaviyo.exception.KlaviyoCreateOrUpdateProfileFailedException;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoProfile;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoProfileApiRequestDtoDataAttributes;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoProfileApiRequestDtoDataAttributesProperties;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoVceMockProfileApiRequestDtoDataAttributes;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoVceMockProfileApiRequestDtoDataAttributesProperties;
import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Log4j2
public class CreateOrUpdateProfileFunctionImpl implements CreateOrUpdateProfileFunction {

    private final static String DEFAULT_PROFILE_TYPE = "profile";

    @Value("${klaviyo.host}")
    private String klaviyoHost;
    private final RestTemplate restTemplate;
    private final HttpHeaders httpHeaders;

    @Override
    public String createOrUpdateProfile(KlaviyoProfile profile) {
        val request = buildProfileCreationRequest(profile);
        return postToKlaviyo(profile.email(), request);
    }

    @Override
    public String createOrUpdateVceMockProfile(VceMockLead vceMockLead) {
        val request = buildVceMockProfileCreationRequest(vceMockLead);
        return postToKlaviyo(vceMockLead.getEmail(), request);
    }

    private String postToKlaviyo(String email, HttpEntity<?> request) {
        val url = buildKlaviyoApiEndpoint();
        try {
            val response = restTemplate.exchange(url, HttpMethod.POST, request, KlaviyoApiResponseDto.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                return Objects.requireNonNull(response.getBody()).data().id();
            } else {
                throw new KlaviyoCreateOrUpdateProfileFailedException(response.toString());
            }
        } catch (RestClientException e) {
            log.error("createOrUpdateProfile to klaviyo fail, email: {}", email, e);
            throw new KlaviyoCreateOrUpdateProfileFailedException(e.getMessage());
        }
    }

    private HttpEntity<KlaviyoApiRequestDto> buildProfileCreationRequest(KlaviyoProfile profile) {
        val creationRequest = buildProfileCreationRequestBody(profile);
        return new HttpEntity<>(creationRequest, httpHeaders);
    }

    private KlaviyoApiRequestDto buildProfileCreationRequestBody(KlaviyoProfile profile) {
        var properties = KlaviyoProfileApiRequestDtoDataAttributesProperties.builder()
                .grade(profile.grade())
                .postCode(profile.postCode())
                .name(profile.name())
                .campus(profile.campus())
                .phone(profile.phone())
                .referralSources(profile.referralSource())
                .otherReferralSource(profile.otherReferralSource())
                .inquiry(profile.inquiry())
                .build();

        var attributes = KlaviyoProfileApiRequestDtoDataAttributes.builder()
                .email(profile.email())
                .properties(properties)
                .build();

        var data = KlaviyoApiRequestDtoData.builder()
                .type(DEFAULT_PROFILE_TYPE)
                .attributes(attributes)
                .build();

        return new KlaviyoApiRequestDto(data);
    }

    private HttpEntity<KlaviyoApiRequestDto> buildVceMockProfileCreationRequest(VceMockLead vceMockLead) {
        val creationRequest = buildVceMockProfileCreationRequestBody(vceMockLead);
        return new HttpEntity<>(creationRequest, httpHeaders);
    }

    private KlaviyoApiRequestDto buildVceMockProfileCreationRequestBody(VceMockLead vceMockLead) {
        var properties = KlaviyoVceMockProfileApiRequestDtoDataAttributesProperties.builder()
                .firstName(vceMockLead.getFirstName())
                .lastName(vceMockLead.getLastName())
                .school(vceMockLead.getSchool())
                .email(vceMockLead.getEmail())
                .phoneNumber(vceMockLead.getPhoneNumber())
                .subjects(vceMockLead.getSubjects())
                .build();

        var attributes = KlaviyoVceMockProfileApiRequestDtoDataAttributes.builder()
                .email(vceMockLead.getEmail())
                .properties(properties)
                .build();

        var data = KlaviyoApiRequestDtoData.builder()
                .type(DEFAULT_PROFILE_TYPE)
                .attributes(attributes)
                .build();

        return new KlaviyoApiRequestDto(data);
    }

    private URI buildKlaviyoApiEndpoint() {
        String url = klaviyoHost + "/api/profile-import";
        return URI.create(url);
    }
}
