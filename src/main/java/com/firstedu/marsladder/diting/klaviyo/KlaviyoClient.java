package com.firstedu.marsladder.diting.klaviyo;

import com.firstedu.marsladder.diting.klaviyo.profiles.addprofiletolist.addprofiletolistfunction.AddProfileToListFunction;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoProfile;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.createorupdatefunction.CreateOrUpdateProfileFunction;
import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class KlaviyoClient {

    private final CreateOrUpdateProfileFunction createOrUpdateProfileFunction;
    private final AddProfileToListFunction addProfileToListFunction;

    public String createOrUpdateProfile(KlaviyoProfile profile) {
        return createOrUpdateProfileFunction.createOrUpdateProfile(profile);
    }

    public void addProfileToList(String profileId, String listId) {
        addProfileToListFunction.addProfileToList(profileId, listId);
    }

    public String createOrUpdateVceMockProfile(VceMockLead lead) {
        return createOrUpdateProfileFunction.createOrUpdateVceMockProfile(lead);
    }
}
