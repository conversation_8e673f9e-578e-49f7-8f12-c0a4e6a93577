package com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.leads.service.domain.Campus;
import com.firstedu.marsladder.diting.leads.service.domain.ReferralSource;
import lombok.Builder;

import java.util.List;

@Builder
public record KlaviyoProfileApiRequestDtoDataAttributesProperties(
        @JsonProperty("$realus_lead_grade")
        Integer grade,
        @JsonProperty("$realus_lead_campus")
        Campus campus,
        @JsonProperty("$realus_lead_name")
        String name,
        @JsonProperty("$realus_lead_phone")
        String phone,
        @JsonProperty("$realus_lead_post_code")
        String postCode,
        @JsonProperty("$realus_lead_referral_sources")
        List<ReferralSource> referralSources,
        @JsonProperty("$realus_lead_other_referral_source")
        String otherReferralSource,
        @JsonProperty("$realus_lead_inquiry")
        String inquiry) {
}

