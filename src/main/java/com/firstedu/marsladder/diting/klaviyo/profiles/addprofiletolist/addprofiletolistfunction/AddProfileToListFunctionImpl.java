package com.firstedu.marsladder.diting.klaviyo.profiles.addprofiletolist.addprofiletolistfunction;

import com.firstedu.marsladder.diting.klaviyo.exception.KlaviyoAddProfileToListException;
import com.firstedu.marsladder.diting.klaviyo.profiles.addprofiletolist.KlaviyoAddProfileToListRequest;
import com.firstedu.marsladder.diting.klaviyo.profiles.addprofiletolist.KlaviyoAddProfileToListRequestDataItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
public class AddProfileToListFunctionImpl implements AddProfileToListFunction {

    @Value("${klaviyo.host}")
    private String klaviyoHost;
    private final RestTemplate restTemplate;
    private final HttpHeaders httpHeaders;

    @Override
    public void addProfileToList(String profileId, String listId) {
        val request = buildAddProfileToListRequest(profileId);
        var url = buildKlaviyoApiEndpoint(listId);
        try {
            val response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            if (response.getStatusCode() != HttpStatus.NO_CONTENT) {
                throw new KlaviyoAddProfileToListException("addProfileToList fail " + response);
            }
        } catch (RestClientException e) {
            log.error("addProfileToList fail, profileId: {}, listId: {}", profileId, listId, e);
            throw new KlaviyoAddProfileToListException(e.getMessage());
        }
    }

    private HttpEntity<KlaviyoAddProfileToListRequest> buildAddProfileToListRequest(String profileId) {
        var body = KlaviyoAddProfileToListRequest.builder()
                .data(List.of(
                        KlaviyoAddProfileToListRequestDataItem.builder()
                                .type("profile")
                                .id(profileId)
                                .build()
                )).build();
        return new HttpEntity<>(body, httpHeaders);
    }

    private URI buildKlaviyoApiEndpoint(String listId) {
        String url = klaviyoHost + "/api/lists/" + listId + "/relationships/profiles";
        return URI.create(url);
    }
}
