package com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.util.List;

@Builder
public record KlaviyoVceMockProfileApiRequestDtoDataAttributesProperties(
        @JsonProperty("$vce_mock_lead_first_name")
        String firstName,
        @JsonProperty("$vce_mock_lead_last_name")
        String lastName,
        @JsonProperty("$vce_mock_lead_school")
        String school,
        @JsonProperty("$vce_mock_lead_email")
        String email,
        @JsonProperty("$vce_mock_lead_phone_number")
        String phoneNumber,
        @JsonProperty("$vce_mock_lead_subjects")
        List<String> subjects) {
}
