package com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate;

import com.firstedu.marsladder.diting.leads.service.domain.Campus;
import com.firstedu.marsladder.diting.leads.service.domain.ReferralSource;
import lombok.Builder;

import java.util.List;

@Builder
public record KlaviyoProfile(
        Integer grade,
        Campus campus,
        String name,
        String email,
        String phone,
        String postCode,
        List<ReferralSource> referralSource,
        String otherReferralSource,
        String inquiry) {
}
