package com.firstedu.marsladder.diting.klaviyo.event;

import com.firstedu.marsladder.diting.klaviyo.KlaviyoClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
@RequiredArgsConstructor
@Log4j2
public class KlaviyoProfileEventListener {
    private final KlaviyoClient klaviyoClient;
    @Value("${klaviyo.realus-lead-all-list}")
    private String realusLeadListId;

    @Value("${klaviyo.vce-mock-lead-all-list}")
    private String vceMockLeadListId;

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    protected void handleStudentTaskSubmitEvent(ProfileCreatedAndAddedToListEvent profileCreatedAndSubscribedEvent) {
        var klaviyoProfile = profileCreatedAndSubscribedEvent.getProfile();
        var profileId = klaviyoClient.createOrUpdateProfile(klaviyoProfile);
        log.info("created or updated klaviyo profile success profileId:{}", profileId);
        klaviyoClient.addProfileToList(profileId, realusLeadListId);
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    protected void handleVceMockLeadsUpdatedEvent(VceMockKlaviyoProfileCreatedAndAddedToListEvent event) {
        var vceMockLead = event.getVceMockLead();
        var profileId = klaviyoClient.createOrUpdateVceMockProfile(vceMockLead);
        log.info("created or updated vce mock klaviyo profile success profileId:{}", profileId);
        klaviyoClient.addProfileToList(profileId, vceMockLeadListId);
    }
}
