package com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.subscribefunction;

import com.firstedu.marsladder.diting.klaviyo.KlaviyoApiRequestDto;
import com.firstedu.marsladder.diting.klaviyo.KlaviyoApiRequestDtoData;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfiles;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfilesData;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfilesDataAttributes;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataRelationshipsList;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataRelationshipsListData;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataAttributes;
import com.firstedu.marsladder.diting.klaviyo.profiles.subscribe.KlaviyoSubscribeProfilesApiRequestDtoDataRelationships;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
public class SubscribeProfilesFunctionImpl implements SubscribeProfilesFunction {

    private final static String DEFAULT_PROFILE_TYPE = "profile";
    private final static String DEFAULT_RELATION_SHIPS = "list";

    @Value("${klaviyo.host}")
    private String klaviyoHost;
    private final RestTemplate restTemplate;
    private final HttpHeaders httpHeaders;

    @Override
    public void subscribeProfiles(List<String> emails, String listId) {
        val request = buildProfileCreationRequest(emails, listId);
        var url = buildKlaviyoApiEndpoint();

        try {
            restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        } catch (RestClientException e) {
            log.error("subscribeProfiles fail, emails: {}, listId: {}", emails, listId, e);
        }
    }

    private HttpEntity<KlaviyoApiRequestDto> buildProfileCreationRequest(List<String> emails, String listId) {
        val attributes = buildAttributes(emails);
        val relationships = buildRelationships(listId);
        var data = KlaviyoApiRequestDtoData.builder()
                .type("profile-subscription-bulk-create-job")
                .attributes(attributes)
                .relationships(relationships)
                .build();

        var klaviyoApiRequestDto = new KlaviyoApiRequestDto(data);
        return new HttpEntity<>(klaviyoApiRequestDto, httpHeaders);
    }

    private KlaviyoSubscribeProfilesApiRequestDtoDataAttributes buildAttributes(List<String> emails) {
        var profilesDataList = emails.stream().map(this::buildProfilesData).toList();
        var profiles = KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfiles.builder().data(profilesDataList).build();
        return KlaviyoSubscribeProfilesApiRequestDtoDataAttributes.builder().profiles(profiles).build();
    }

    private KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfilesData buildProfilesData(String email) {
        var attributes = KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfilesDataAttributes.builder().email(email).build();
        return KlaviyoSubscribeProfilesApiRequestDtoDataAttributesProfilesData.builder().attributes(attributes).type(DEFAULT_PROFILE_TYPE).build();
    }

    private KlaviyoSubscribeProfilesApiRequestDtoDataRelationships buildRelationships(String listId) {
        var data = KlaviyoSubscribeProfilesApiRequestDtoDataRelationshipsListData.builder().type(DEFAULT_RELATION_SHIPS).id(listId).build();
        var list = KlaviyoSubscribeProfilesApiRequestDtoDataRelationshipsList.builder().data(data).build();
        return KlaviyoSubscribeProfilesApiRequestDtoDataRelationships.builder().list(list).build();
    }

    private URI buildKlaviyoApiEndpoint() {
        String url = klaviyoHost + "/api/profile-subscription-bulk-create-jobs";
        return URI.create(url);
    }
}
