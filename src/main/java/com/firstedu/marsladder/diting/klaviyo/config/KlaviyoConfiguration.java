package com.firstedu.marsladder.diting.klaviyo.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

@Configuration
public class KlaviyoConfiguration {

    @Value("${klaviyo.private-api-key}")
    private String klaviyoPrivateApiKey;

    @Bean
    public HttpHeaders httpHeaders() {
        var headers = new HttpHeaders();
        headers.set(HttpHeaders.AUTHORIZATION, buildKlaviyoPrivateApiKey());
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
        headers.set("revision", "2024-07-15");
        return headers;
    }

    private String buildKlaviyoPrivateApiKey() {
        return String.format("Klaviyo-API-Key %s", klaviyoPrivateApiKey);
    }
}
