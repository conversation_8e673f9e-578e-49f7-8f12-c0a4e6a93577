package com.firstedu.marsladder.diting.xgjclass.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

import java.time.LocalDateTime;
import java.util.List;

import static org.hibernate.type.SqlTypes.JSON;

@Entity(name = "xiaoguanjia_class_tasks_phase_feedback")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Getter
public class StudentClassTasksPhaseFeedbackEntity {
    @Id
    @UuidGenerator
    String id;
    String classId;
    @JdbcTypeCode(JSON)
    List<String> classroomIds;
    String marsladderStudentId;
    String overallGrading;
    String comment;
    String commentCn;
    LocalDateTime startTime;
    LocalDateTime endTime;
    @CreationTimestamp
    LocalDateTime createdAt;
    @UpdateTimestamp
    LocalDateTime updatedAt;
}
