package com.firstedu.marsladder.diting.xgjclass.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.shift.service.ShiftService;
import com.firstedu.marsladder.diting.shift.service.exception.XiaoguanjiaShiftNotFoundException;
import com.firstedu.marsladder.diting.xgjclass.service.ClassService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassStudentService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassTeacherService;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassStudent;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassStudentDetail;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.groupingBy;

@Service
@RequiredArgsConstructor
public class ClassStudentServiceImpl implements ClassStudentService {
    private final ClassService classService;
    private final ClassTeacherService classTeacherService;
    private final ShiftService shiftService;
    private final XiaoguanjiaClassStudentRepository xiaoguanjiaClassStudentRepository;

    @Override
    public List<ClassStudentDetail> getAllClassStudentDetailsByStudentId(String studentId) {
        var classStudentRecords = getLatestClassStudentRecordsInAllClasses(studentId);

        var classStudentDetails = new ArrayList<ClassStudentDetail>();
        for (var classStudent : classStudentRecords) {
            var classId = classStudent.classId();
            try {
                var xiaoguanjiaClass = classService.getXiaoguanjiaClassByClassId(classId);
                var shift = shiftService.findShiftByShiftId(xiaoguanjiaClass.shiftId());
                var teacherName = classTeacherService.getTeacherName(classId);
                classStudentDetails.add(
                        ClassStudentDetail.from(shift, xiaoguanjiaClass, classStudent, teacherName)
                );
            } catch (XiaoguanjiaClassNotExistException | XiaoguanjiaShiftNotFoundException ignored) {
                continue;
            }
        }

        return classStudentDetails;
    }

    public List<ClassStudent> getLatestClassStudentRecordsInAllClasses(String studentId) {
        List<ClassStudent> latestClassStudentRecordsInEachClass = new ArrayList<>();

        xiaoguanjiaClassStudentRepository
                .findByStudentId(studentId)
                .stream()
                .collect(groupingBy(XiaoguanjiaClassStudentEntity::getClassId))
                .forEach((classId, classStudentEntities) -> {
                    classStudentEntities.sort(comparing(XiaoguanjiaClassStudentEntity::getCreatedAt));
                    var oldestRecordEntity = classStudentEntities.getFirst();
                    var latestRecordEntity = classStudentEntities.getLast();

                    latestClassStudentRecordsInEachClass.add(ClassStudent.from(latestRecordEntity, oldestRecordEntity.getCreatedAt()));
                });

        return latestClassStudentRecordsInEachClass;
    }
}
