package com.firstedu.marsladder.diting.xgjclass.service.domain;

import com.firstedu.marsladder.diting.shift.service.domain.Shift;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;
import lombok.Builder;

import java.time.LocalDateTime;

@Builder
public record ClassStudentDetail(
        String gradeId,
        String subjectId,
        String shiftId,
        String shiftName,
        ShiftStatus shiftStatus,
        String classId,
        String className,
        LocalDateTime classCreatedAt,
        Boolean isClassFinished,
        String teacherName,
        String classStudentId,
        ClassStudentStatus classStudentStatus,
        LocalDateTime classStudentInDate
) {

    public static ClassStudentDetail from(
            Shift shift,
            XiaoguanjiaClass xiaoguanjiaClass,
            ClassStudent classStudent,
            String teacherName) {
        return new ClassStudentDetail(
                xiaoguanjiaClass.gradeId(),
                xiaoguanjiaClass.subjectId(),
                shift.id(),
                shift.name(),
                shift.status(),
                classStudent.classId(),
                xiaoguanjiaClass.name(),
                xiaoguanjiaClass.createdAt(),
                xiaoguanjiaClass.isFinished(),
                teacherName,
                classStudent.id(),
                classStudent.status(),
                classStudent.classJoinedAt()
        );
    }
}
