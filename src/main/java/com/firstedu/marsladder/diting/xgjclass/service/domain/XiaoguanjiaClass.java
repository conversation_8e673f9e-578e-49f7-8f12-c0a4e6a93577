package com.firstedu.marsladder.diting.xgjclass.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;

import java.time.LocalDateTime;
import java.util.List;

public record XiaoguanjiaClass(
        String id,
        String name,
        String headMasterId,
        String subjectId,
        String shiftId,
        Boolean isFinished,
        LocalDateTime createdAt,
        String gradeId) {
    public static XiaoguanjiaClass from(XiaoguanjiaClassEntity xiaoguanjiaClassEntity) {
        return new XiaoguanjiaClass(
                xiaoguanjiaClassEntity.getId(),
                xiaoguanjiaClassEntity.getName(),
                xiaoguanjiaClassEntity.getHeadmasterId(),
                xiaoguanjiaClassEntity.getSubjectId(),
                xiaoguanjiaClassEntity.getShiftId(),
                xiaoguanjiaClassEntity.getIsFinished(),
                xiaoguanjiaClassEntity.getCreatedAt(),
                xiaoguanjiaClassEntity.getGradeId());
    }

    public static List<XiaoguanjiaClass> listFrom(List<XiaoguanjiaClassEntity> xiaoguanjiaClassEntities) {
        return xiaoguanjiaClassEntities.stream().map(XiaoguanjiaClass::from).toList();
    }
}
