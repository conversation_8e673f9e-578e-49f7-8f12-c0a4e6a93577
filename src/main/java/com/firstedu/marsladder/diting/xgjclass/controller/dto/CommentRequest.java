package com.firstedu.marsladder.diting.xgjclass.controller.dto;

import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;

public record CommentRequest(
        @NotNull String attendedBy,
        @NotNull Float score,
        @Nullable String comment
) {
    public ClassUnitComment toClassUnitComment(String classUnitId, String commentedBy) {
        return new ClassUnitComment(null, commentedBy, classUnitId, attendedBy, score, comment);
    }
}
