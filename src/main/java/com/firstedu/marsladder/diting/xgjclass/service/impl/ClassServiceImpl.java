package com.firstedu.marsladder.diting.xgjclass.service.impl;

import com.firstedu.marsladder.diting.campus.repository.SchoolCampusBindingsRepository;
import com.firstedu.marsladder.diting.campus.repository.entity.SchoolCampusBindingsEntity;
import com.firstedu.marsladder.diting.classroom.controller.dto.MarsladderClassroomsByType;
import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.classroom.repository.ClassroomBindingsRepository;
import com.firstedu.marsladder.diting.classroom.repository.entity.ClassroomBindingsEntity;
import com.firstedu.marsladder.diting.classroom.service.domain.Classroom;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticForAiFeedback;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticRequest;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.SchoolCampusDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.client.falcon.dto.XiaogjClassInfo;
import com.firstedu.marsladder.diting.client.kuixing.KuixingClient;
import com.firstedu.marsladder.diting.client.kuixing.dto.AiFeedbackDto;
import com.firstedu.marsladder.diting.client.nezha.NezhaClient;
import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatisticForAiFeedback;
import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatisticRequest;
import com.firstedu.marsladder.diting.xgjclass.repository.StudentClassTasksPhaseFeedbackRepository;
import com.firstedu.marsladder.diting.xgjclass.repository.entity.StudentClassTasksPhaseFeedbackEntity;
import com.firstedu.marsladder.diting.xgjclass.service.ClassService;
import com.firstedu.marsladder.diting.xgjclass.service.domain.StudentClassTasksFeedBack;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaGradeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaSubjectRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ClassServiceImpl implements ClassService {
    private final XiaoguanjiaClassRepository xiaoguanjiaClassRepository;
    private final ClassroomBindingsRepository classroomBindingsRepository;
    private final StudentClassTasksPhaseFeedbackRepository studentClassTasksPhaseFeedbackRepository;
    private final FalconClient falconClient;
    private final NezhaClient nezhaClient;
    private final KuixingClient kuixingClient;
    private final XiaoguanjiaGradeRepository xiaoguanjiaGradeRepository;
    private final XiaoguanjiaSubjectRepository xiaoguanjiaSubjectRepository;
    private final SchoolCampusBindingsRepository schoolCampusBindingsRepository;

    private static final String MATH_SUBJECT_ID = "00000024-0004-0028-0000-000000000001";

    @Override
    public XiaoguanjiaClass getXiaoguanjiaClassByClassId(String classId) {
        var classEntityOptional = xiaoguanjiaClassRepository.findById(classId);
        if (classEntityOptional.isEmpty()) {
            log.error("Xiaoguanjia class with id {} not found", classId);
            throw new XiaoguanjiaClassNotExistException("Xiaoguanjia class with id " + classId + " not found");
        } else {
            return XiaoguanjiaClass.from(classEntityOptional.get());
        }
    }

    @Override
    public List<XiaoguanjiaClass> getClasses() {
        return XiaoguanjiaClass.listFrom(xiaoguanjiaClassRepository.findAllByIsFinishedAndDeletedAndStatus(false, false, 1));
    }

    @Override
    public List<XiaoguanjiaClass> getClassesByCampusOfTeacher() {
        var teacherProfile = falconClient.getTeacherProfile();
        var schoolCampusIds = teacherProfile.schoolCampusList()
                .stream().filter(x->!x.removed())
                .map(SchoolCampusDto::schoolCampusId).toList();

        var departmentIds = schoolCampusBindingsRepository.findByschoolCampusIdIn(schoolCampusIds).stream().map(SchoolCampusBindingsEntity::getDepartmentId).toList();

        if (departmentIds.isEmpty()) {
            return XiaoguanjiaClass.listFrom(xiaoguanjiaClassRepository.findAllByIsFinishedAndDeletedAndStatus(false, false, 1));
        } else {
            return XiaoguanjiaClass.listFrom(xiaoguanjiaClassRepository.findAllByIsFinishedAndDeletedAndStatusAndCampusIdIn(false, false, 1, departmentIds));
        }
    }

    @Override
    public List<Classroom> getMarsladderClassrooms(String classId) {
        return classroomBindingsRepository.findByXiaoguanjiaClassId(classId).stream()
                .map(classroomBindingsEntity -> new Classroom(classroomBindingsEntity.getClassroomId()))
                .toList();
    }

    @Override
    public List<StudentClassTasksFeedBack> getStudentClassTasksFeedBack(String classId, String studentId) {
        var marsLadderStudentId = getMarsLadderUserId(studentId);
        if (marsLadderStudentId == null) {
            log.warn("Current xiaogjStudentId: {} not found in MarsLadder，classId:{}", studentId, classId);
            return Collections.emptyList();
        }

        return studentClassTasksPhaseFeedbackRepository
                .findByMarsladderStudentIdAndClassIdOrderByStartTimeDesc(marsLadderStudentId, classId)
                .stream()
                .map(entity -> StudentClassTasksFeedBack.from(entity, studentId))
                .toList();

    }

    private String getMarsLadderUserId(String xiaogjStudentId) {
        var user = falconClient.getUser();

        if (user.roles().contains(UserRole.REALUS_STUDENT)) {
            if (xiaogjStudentId.equals(user.realusStudentProfile().xiaogjStudentId())) {
                return user.realusStudentProfile().userId();
            }
            return null;
        } else {
            return user.realusParentProfile().students()
                    .stream()
                    .filter(student -> xiaogjStudentId.equals(student.xiaogjStudentId()))
                    .map(RealusStudentProfile::userId)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse(null);
        }
    }

    @Override
    public void generateTaskFeedbacksForBoundedClasses(LocalDateTime startTime, LocalDateTime endTime) {
        var allClassBindings = getAllClassBindings();
        log.info("current classes bindings, {}", allClassBindings);
        allClassBindings.forEach((classId, classroomIds) -> {
            var classrooms = getClassroomsGroupBySubject(classroomIds);

            var mathSubjectClassroomIds = classrooms.getMathClassroomIds();
            if (CollectionUtils.isNotEmpty(mathSubjectClassroomIds)) {
                log.info("xiaogjclassId:{}, math classroomIds: {}", classId, mathSubjectClassroomIds);
                falconClient.getContentForGenerateTaskFeedback(mathSubjectClassroomIds, startTime, endTime).parallelStream().forEach(
                        taskStatistic -> getAndSaveClassTaskPhaseFeedbackForMath(classId, mathSubjectClassroomIds, taskStatistic)
                );
            }

            var multiSubjectClassroomIds = classrooms.getMultiSubjectClassroomIds();
            if (CollectionUtils.isNotEmpty(multiSubjectClassroomIds)) {
                log.info("xiaogjclassId: {}, multi classroomIds: {}", classId, multiSubjectClassroomIds);
                nezhaClient.getContentForGenerateTaskFeedback(multiSubjectClassroomIds, startTime, endTime).parallelStream().forEach(
                        taskStatistic -> getAndSaveClassTaskPhaseFeedbackForMultiSubject(classId, multiSubjectClassroomIds, taskStatistic)
                );
            }
        });
    }

    @Override
    public void generateTaskFeedbackForStudentInClass(String classId, String marsladderStudentId, LocalDateTime startTime, LocalDateTime endTime) {
        var classroomIds = classroomBindingsRepository.findByXiaoguanjiaClassId(classId)
                .stream()
                .map(ClassroomBindingsEntity::getClassroomId)
                .toList();

        var classroomIdsBySubject = getClassroomsGroupBySubject(classroomIds);
        var mathClassroomIds = classroomIdsBySubject.getMathClassroomIds();
        if (CollectionUtils.isNotEmpty(mathClassroomIds)) {
            falconClient.getContentForGenerateTaskFeedback(mathClassroomIds, startTime, endTime).stream()
                    .filter(taskStatic -> marsladderStudentId.equals(taskStatic.userId()))
                    .forEach(taskStatic -> getAndSaveClassTaskPhaseFeedbackForMath(classId, mathClassroomIds, taskStatic));
        }

        var multiSubjectClassroomIds = classroomIdsBySubject.getMultiSubjectClassroomIds();
        if (CollectionUtils.isNotEmpty(multiSubjectClassroomIds)) {
            nezhaClient.getContentForGenerateTaskFeedback(multiSubjectClassroomIds, startTime, endTime).stream()
                    .filter(taskStatic -> marsladderStudentId.equals(taskStatic.userId()))
                    .forEach(taskStatic -> getAndSaveClassTaskPhaseFeedbackForMultiSubject(classId, multiSubjectClassroomIds, taskStatic));
        }
    }

    private void getAndSaveClassTaskPhaseFeedbackForMath(String classId, List<String> classroomIds, MathTaskStatisticForAiFeedback taskStatic) {
        var mathTaskStatisticRequest = new MathTaskStatisticRequest(
                buildXiaogjClassInfo(classId),
                taskStatic.tasks(),
                taskStatic.classPerformance()
        );
        log.info("get and save math tasks feedback, userId: {}, classId:{}, taskStaticRequest:{}", taskStatic.userId(), classId, mathTaskStatisticRequest);
        ResponseEntity<AiFeedbackDto> aiFeedbackResponse;
        try {
            aiFeedbackResponse = kuixingClient.getAiFeedbackForMath(mathTaskStatisticRequest);
            if (aiFeedbackResponse.getStatusCode().is2xxSuccessful()) {
                var aiFeedBack = aiFeedbackResponse.getBody();
                if (aiFeedBack != null) {
                    studentClassTasksPhaseFeedbackRepository.save(
                            new StudentClassTasksPhaseFeedbackEntity(
                                    null,
                                    classId,
                                    classroomIds,
                                    taskStatic.userId(),
                                    aiFeedBack.overallGrading(),
                                    aiFeedBack.feedbackEn(),
                                    aiFeedBack.feedbackCn(),
                                    taskStatic.startTime(),
                                    taskStatic.endTime(),
                                    null,
                                    null
                            )
                    );
                }
            } else {
                log.error("kuixing request for math tasks static exception, status code:{}", aiFeedbackResponse.getStatusCode());
            }
        } catch (Exception e) {
            log.error("kuixing request for math tasks static fail", e);
        }
    }

    private void getAndSaveClassTaskPhaseFeedbackForMultiSubject(String classId, List<String> classroomIds, MultiSubjectTaskStatisticForAiFeedback taskStatic) {
        var multiSubjectTaskStatisticRequest = new MultiSubjectTaskStatisticRequest(
                buildXiaogjClassInfo(classId),
                taskStatic.tasks(),
                taskStatic.classPerformance()
        );
        log.info("get and save multi subject tasks feedback, userId: {}, classId:{}, taskStaticRequest:{}", taskStatic.userId(), classId, multiSubjectTaskStatisticRequest);
        try {
            var aiFeedbackResponse = kuixingClient.getAiFeedbackForMultiSubject(multiSubjectTaskStatisticRequest);
            if (aiFeedbackResponse.getStatusCode().is2xxSuccessful()) {
                var aiFeedBack = aiFeedbackResponse.getBody();
                if (aiFeedBack != null) {
                    studentClassTasksPhaseFeedbackRepository.save(
                            new StudentClassTasksPhaseFeedbackEntity(
                                    null,
                                    classId,
                                    classroomIds,
                                    taskStatic.userId(),
                                    aiFeedBack.overallGrading(),
                                    aiFeedBack.feedbackEn(),
                                    aiFeedBack.feedbackCn(),
                                    taskStatic.startTime(),
                                    taskStatic.endTime(),
                                    null,
                                    null
                            )
                    );
                }
            } else {
                log.error("kuixing request for multi subject tasks static exception, status code:{}", aiFeedbackResponse.getStatusCode());
            }
        } catch (Exception e) {
            log.error("kuixing request for multi subject tasks static fail", e);
        }
    }

    private Map<String, List<String>> getAllClassBindings() {
        var allClassroomBindings = getAllClassroomBindings();

        Map<String, List<String>> allClassBindings = new HashMap<>();
        allClassroomBindings.forEach(classroomBindingsEntity -> {
            var classroomId = classroomBindingsEntity.getClassroomId();
            classroomBindingsEntity.getXiaoguanjiaClassIds().forEach(xiaogjClassId ->
                    allClassBindings.computeIfAbsent(xiaogjClassId, k -> new ArrayList<>()).add(classroomId)
            );
        });
        return allClassBindings;
    }

    private List<ClassroomBindingsEntity> getAllClassroomBindings() {
        List<ClassroomBindingsEntity> allBindings = Lists.newArrayList();
        int page = 0;
        Page<ClassroomBindingsEntity> resultPage;
        do {
            resultPage = classroomBindingsRepository.findAll(PageRequest.of(page, 50));
            allBindings.addAll(resultPage.getContent());
            page++;
        } while (resultPage.hasNext() && resultPage.getSize() > 0);

        return allBindings;
    }

    private MarsladderClassroomsByType getClassroomsGroupBySubject(List<String> classroomIds) {
        Map<Boolean, List<String>> classifiedClassrooms = classroomIds.stream()
                .collect(Collectors.partitioningBy(classroomId -> {
                    var classroom = falconClient.getClassroom(classroomId);
                    return MATH_SUBJECT_ID.equals(classroom.subjectId());
                }));
        return new MarsladderClassroomsByType(
                classifiedClassrooms.getOrDefault(true, List.of()),
                classifiedClassrooms.getOrDefault(false, List.of())
        );
    }

    private XiaogjClassInfo buildXiaogjClassInfo(String classId) {
        return xiaoguanjiaClassRepository.findById(classId)
                .map(classEntity -> {
                    String subject = xiaoguanjiaSubjectRepository
                            .findByIdAndStatus(classEntity.getSubjectId(), DictStatus.ENABLED)
                            .map(XiaoguanjiaSubjectEntity::getValue)
                            .orElse("");

                    String grade = xiaoguanjiaGradeRepository
                            .findByIdAndStatus(classEntity.getGradeId(), DictStatus.ENABLED)
                            .map(XiaoguanjiaGradeEntity::getValue)
                            .orElse("");

                    return new XiaogjClassInfo(subject, grade);
                })
                .orElse(new XiaogjClassInfo("", ""));
    }
}

