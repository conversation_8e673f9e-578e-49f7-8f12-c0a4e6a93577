package com.firstedu.marsladder.diting.xgjclass.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;

import java.time.LocalDateTime;

public record ClassStudent(
        String id,
        String classId,
        String studentId,
        ClassStudentStatus status,
        LocalDateTime classJoinedAt
) {

    public static ClassStudent from(
            XiaoguanjiaClassStudentEntity entity,
            LocalDateTime classJoinedAt
    ) {
        return new ClassStudent(
                entity.getId(),
                entity.getClassId(),
                entity.getStudentId(),
                entity.getStatus(),
                classJoinedAt
        );
    }
}
