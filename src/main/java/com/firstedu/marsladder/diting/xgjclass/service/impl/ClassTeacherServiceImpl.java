package com.firstedu.marsladder.diting.xgjclass.service.impl;

import com.firstedu.marsladder.diting.employee.service.EmployeeService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassTeacherService;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassTeacherRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.TeacherRole.TEACHER;

@Service
@RequiredArgsConstructor
public class ClassTeacherServiceImpl implements ClassTeacherService {
    private final XiaoguanjiaClassTeacherRepository xiaoguanjiaClassTeacherRepository;
    private final EmployeeService employeeService;

    @Override
    public String getTeacherName(String classId) {
        var headTeacherEntity = xiaoguanjiaClassTeacherRepository.findByClassIdAndRole(classId, TEACHER);
        if (null != headTeacherEntity) {
            return employeeService.getEmployeeById(headTeacherEntity.getEmployeeId()).name();
        } else {
            return null;
        }
    }
}
