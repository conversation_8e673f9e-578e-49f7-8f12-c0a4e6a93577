package com.firstedu.marsladder.diting.xgjclass.controller.dto;

import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;

import java.util.List;

public record ClassUnitCommentResponse(
        String id,
        String classUnitId,
        Float score,
        String comment
) {
    public static ClassUnitCommentResponse from(ClassUnitComment comment) {
        return new ClassUnitCommentResponse(
                comment.id(),
                comment.classUnitId(),
                comment.score(),
                comment.comment());
    }

    public static List<ClassUnitCommentResponse> listFrom(List<ClassUnitComment> comments) {
        return comments.stream().map(ClassUnitCommentResponse::from).toList();
    }

}
