package com.firstedu.marsladder.diting.xgjclass.controller.dto;

import com.firstedu.marsladder.diting.xgjclass.service.domain.StudentClassTasksFeedBack;

public record ClassTasksFeedBackDetailResponse(
        String xiaogjStudentId,
        String classId,
        String overallGrading,
        String comment,
        String commentCn,
        Long startTime,
        Long endTime
) {
    public static ClassTasksFeedBackDetailResponse from(StudentClassTasksFeedBack studentClassTasksFeedBack) {
        return new ClassTasksFeedBackDetailResponse(
                studentClassTasksFeedBack.xiaogjStudentId(),
                studentClassTasksFeedBack.classId(),
                studentClassTasksFeedBack.overallGrading(),
                studentClassTasksFeedBack.comment(),
                studentClassTasksFeedBack.commentCn(),
                studentClassTasksFeedBack.startTime(),
                studentClassTasksFeedBack.endTime()
        );
    }
}
