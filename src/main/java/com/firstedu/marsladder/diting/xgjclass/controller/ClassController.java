package com.firstedu.marsladder.diting.xgjclass.controller;

import com.firstedu.marsladder.diting.classroom.controller.dto.ClassroomResponse;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.course.service.CourseService;
import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import com.firstedu.marsladder.diting.utils.DatetimeProvider;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.ClassStudentDetailResponse;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.ClassTasksFeedBackDetailResponse;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.ClassUnitCommentResponse;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.Status;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.TaskFeedbackRequest;
import com.firstedu.marsladder.diting.xgjclass.service.ClassService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassStudentService;
import com.firstedu.marsladder.diting.xgjclass.service.ClassUnitService;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassStudentDetail;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.XiaoguanjiaClassDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.firstedu.marsladder.diting.client.falcon.dto.UserRole.TEACHER;
import static com.firstedu.marsladder.diting.client.falcon.dto.UserRole.TUTOR;
import static com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus.ENTERED;
import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
@Log4j2
public class ClassController {
    private final ClassService classService;
    private final ClassStudentService classStudentService;
    private final ClassUnitService classUnitService;
    private final CourseService courseService;
    private final FalconClient falconClient;
    private final DatetimeProvider datetimeProvider;

    @ResponseStatus(OK)
    @GetMapping("/classes/me")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<ClassStudentDetailResponse> getClassStudentDetails(
            @RequestParam(required = false) Status courseStatus
    ) {
        var userDto = falconClient.getUser();

        if (isStudent(userDto)) {
            var studentProfile = userDto.realusStudentProfile();
            return getClassStudentDetailResponses(courseStatus, studentProfile);
        } else {
            var studentProfilesOfParent = userDto.realusParentProfile().students();
            return studentProfilesOfParent
                    .stream()
                    .map(studentProfile -> getClassStudentDetailResponses(courseStatus, studentProfile))
                    .flatMap(List::stream)
                    .toList();
        }
    }

    private List<ClassStudentDetailResponse> getClassStudentDetailResponses(
            Status courseStatus,
            RealusStudentProfile studentProfile
    ) {
        var studentId = studentProfile.xiaogjStudentId();
        var userId = studentProfile.userId();
        var classStudentDetails = classStudentService.getAllClassStudentDetailsByStudentId(studentId);
        if (courseStatus == null) {
            return classStudentDetails
                    .stream()
                    .map(classStudentDetail -> ClassStudentDetailResponse.from(userId, studentId, classStudentDetail, computeDisplayedStatus(classStudentDetail)))
                    .toList();
        } else {
            return classStudentDetails
                    .stream()
                    .map(classStudentDetail -> ClassStudentDetailResponse.from(userId, studentId, classStudentDetail, computeDisplayedStatus(classStudentDetail)))
                    .filter(response -> response.status() == courseStatus)
                    .toList();
        }
    }

    private boolean isStudent(UserDto userDto) {
        return userDto.roles().contains(UserRole.REALUS_STUDENT);
    }

    @ResponseStatus(OK)
    @GetMapping("/classes")
    public List<XiaoguanjiaClassDto> getClasses() {
        var user = falconClient.getUser();

        if (user.roles().contains(TUTOR)) {
            return XiaoguanjiaClassDto.listFrom(classService.getClasses());
        } else if (user.roles().contains(TEACHER)) {
            return XiaoguanjiaClassDto.listFrom(classService.getClassesByCampusOfTeacher());
        }
        return XiaoguanjiaClassDto.listFrom(Collections.emptyList());
    }

    @ResponseStatus(OK)
    @GetMapping("/classes/{classId}/comments")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<ClassUnitCommentResponse> getAllComments(
            @PathVariable String classId,
            @RequestParam(required = true) String attendedBy
    ) {
        var courseSchedules = courseService.getCourseSchedules(CourseScheduleFilter.builder().classId(classId).build());
        var comments = classUnitService.getComments(courseSchedules.stream().map(CourseSchedule::courseId).toList(), attendedBy);
        return ClassUnitCommentResponse.listFrom(comments);
    }

    @ResponseStatus(OK)
    @GetMapping("/classes/{classId}/students/{studentId}/task-feedback")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<ClassTasksFeedBackDetailResponse> getStudentClassTasksFeedback(
            @PathVariable String classId,
            @PathVariable String studentId) {

        return classService.getStudentClassTasksFeedBack(classId, studentId).stream()
                .map(ClassTasksFeedBackDetailResponse::from)
                .toList();
    }

    @ResponseStatus(OK)
    @PostMapping("/all-bounded-classes/tasks/phase-feedback")
    public void generateTaskFeedbacksForBoundedClassesFromCron(
            @RequestParam(value = "startTime", required = false) Optional<LocalDateTime> startTime,
            @RequestParam(value = "endTime", required = false) Optional<LocalDateTime> endTime
    ) {
        final LocalDateTime finalStartTime = startTime.orElseGet(datetimeProvider::getFirstDayOfCurrentMonth);
        final LocalDateTime finalEndTime = endTime.orElseGet(datetimeProvider::now);
        log.info("generate task feedback start.., startTime:{} -  endTime:{}", startTime, endTime);
        classService.generateTaskFeedbacksForBoundedClasses(finalStartTime, finalEndTime);
    }

    @ResponseStatus(OK)
    @PostMapping("/classes/{classId}/tasks/phase-feedback")
    public void generateTaskFeedbackForStudentInClass(
            @PathVariable String classId,
            @RequestBody TaskFeedbackRequest request
    ) {
        classService.generateTaskFeedbackForStudentInClass(classId, request.marsLadderStudentId(), request.startTime(), request.endTime());
    }

    @ResponseStatus(OK)
    @GetMapping("/classes/{classId}/marsladder-classrooms")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<ClassroomResponse> getMarsLadderClassroomsByClassId(
            @PathVariable String classId) {
        return classService.getMarsladderClassrooms(classId).stream()
                .map(ClassroomResponse::from)
                .toList();
    }

    public static Status computeDisplayedStatus(ClassStudentDetail classStudentDetail) {
        var isClassFinished = classStudentDetail.isClassFinished();
        var isStudentEnteredClass = classStudentDetail.classStudentStatus() == ENTERED;
        if (isClassFinished) {
            return Status.COMPLETED;
        } else {
            if (isStudentEnteredClass) {
                return Status.IN_PROGRESS;
            } else {
                return Status.REMOVED;
            }
        }
    }

}
