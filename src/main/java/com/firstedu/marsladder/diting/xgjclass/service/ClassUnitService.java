package com.firstedu.marsladder.diting.xgjclass.service;

import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;

import java.util.List;

public interface ClassUnitService {

    ClassUnitComment comment(ClassUnitComment classUnitComment);

    ClassUnitComment getComment(String classUnitId, String attendedBy);

    List<ClassUnitComment> getComments(List<String> classUnitIds, String attendedBy);
}
