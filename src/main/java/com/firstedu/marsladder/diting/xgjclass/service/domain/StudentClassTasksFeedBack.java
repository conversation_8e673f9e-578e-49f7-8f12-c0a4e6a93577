package com.firstedu.marsladder.diting.xgjclass.service.domain;

import com.firstedu.marsladder.diting.xgjclass.repository.entity.StudentClassTasksPhaseFeedbackEntity;
import lombok.Builder;

import java.time.ZoneOffset;

@Builder
public record StudentClassTasksFeedBack(
        String id,
        String classId,
        String xiaogjStudentId,
        String marsladderStudentId,
        String overallGrading,
        String comment,
        String commentCn,
        Long startTime,
        Long endTime
) {
    public static StudentClassTasksFeedBack from(StudentClassTasksPhaseFeedbackEntity entity, String xiaogjStudentId) {
        return new StudentClassTasksFeedBack(
                entity.getId(),
                entity.getClassId(),
                xiaogjStudentId,
                entity.getMarsladderStudentId(),
                entity.getOverallGrading(),
                entity.getComment(),
                entity.getCommentCn(),
                entity.getStartTime().toEpochSecond(ZoneOffset.UTC),
                entity.getEndTime().toEpochSecond(ZoneOffset.UTC)
        );
    }

}

