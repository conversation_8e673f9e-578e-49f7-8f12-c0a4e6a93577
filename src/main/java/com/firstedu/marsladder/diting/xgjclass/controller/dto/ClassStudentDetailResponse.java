package com.firstedu.marsladder.diting.xgjclass.controller.dto;

import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassStudentDetail;

import java.time.ZoneOffset;

public record ClassStudentDetailResponse(
        String id,
        String marsladderUserId,
        String xiaogjStudentId,
        Status status,
        String gradeId,
        String subjectId,
        String courseId,
        String courseName,
        String classId,
        String className,
        Long classCreatedAt,
        String teacherName,
        Long registeredAt // todo: maybe rename to studentJoinedAt
) {

    public static ClassStudentDetailResponse from(
            String userId,
            String xiaogjStudentId,
            ClassStudentDetail classStudentDetail,
            Status displayedStatus
    ) {
        return new ClassStudentDetailResponse(
                classStudentDetail.classStudentId(),
                userId,
                xiaogjStudentId,
                displayedStatus,
                classStudentDetail.gradeId(),
                classStudentDetail.subjectId(),
                classStudentDetail.shiftId(),
                classStudentDetail.shiftName(),
                classStudentDetail.classId(),
                classStudentDetail.className(),
                classStudentDetail.classCreatedAt().toEpochSecond(ZoneOffset.UTC),
                classStudentDetail.teacherName(),
                classStudentDetail.classStudentInDate().toEpochSecond(ZoneOffset.UTC)
        );
    }
}
