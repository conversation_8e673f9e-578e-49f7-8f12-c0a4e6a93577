package com.firstedu.marsladder.diting.xgjclass.repository;

import com.firstedu.marsladder.diting.xgjclass.repository.entity.StudentClassTasksPhaseFeedbackEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StudentClassTasksPhaseFeedbackRepository extends JpaRepository<StudentClassTasksPhaseFeedbackEntity, String> {
    List<StudentClassTasksPhaseFeedbackEntity> findByMarsladderStudentIdAndClassIdOrderByStartTimeDesc(String marsladderStudentId, String classId);
}
