package com.firstedu.marsladder.diting.xgjclass.controller;

import com.firstedu.marsladder.diting.security.AuthenticationPrincipalUsername;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.ClassUnitCommentResponse;
import com.firstedu.marsladder.diting.xgjclass.controller.dto.CommentRequest;
import com.firstedu.marsladder.diting.xgjclass.service.ClassUnitService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class ClassUnitController {
    private final ClassUnitService classUnitService;

    @ResponseStatus(CREATED)
    @PostMapping("/class-units/{classUnitId}/comment")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public ClassUnitCommentResponse createClassUnitComment(
            @AuthenticationPrincipalUsername String userId,
            @PathVariable String classUnitId,
            @RequestBody @Validated CommentRequest commentRequest
    ) {
        var comment = classUnitService.comment(commentRequest.toClassUnitComment(classUnitId, userId));
        return ClassUnitCommentResponse.from(comment);
    }

    @ResponseStatus(OK)
    @GetMapping("/class-units/{classUnitId}/comment")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public ClassUnitCommentResponse getClassUnitComment(
            @PathVariable String classUnitId,
            @RequestParam(required = true) String attendedBy
    ) {
        return ClassUnitCommentResponse.from(
                classUnitService.getComment(classUnitId, attendedBy));
    }
}
