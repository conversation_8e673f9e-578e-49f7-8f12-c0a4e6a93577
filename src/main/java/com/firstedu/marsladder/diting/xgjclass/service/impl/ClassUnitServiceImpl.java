package com.firstedu.marsladder.diting.xgjclass.service.impl;

import com.firstedu.marsladder.diting.xgjclass.exception.ClassUnitAlreadyCommentedException;
import com.firstedu.marsladder.diting.xgjclass.repository.ClassUnitCommentRepository;
import com.firstedu.marsladder.diting.xgjclass.service.ClassUnitService;
import com.firstedu.marsladder.diting.xgjclass.service.domain.ClassUnitComment;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ClassUnitServiceImpl implements ClassUnitService {

    private final ClassUnitCommentRepository classUnitCommentRepository;

    @Override
    public ClassUnitComment comment(ClassUnitComment classUnitComment) {
        var existingCommentEntityOptional = classUnitCommentRepository
                .findByAttendedByAndClassUnitId(classUnitComment.attendedBy(), classUnitComment.classUnitId());

        if (existingCommentEntityOptional.isPresent()) {
            throw new ClassUnitAlreadyCommentedException("Class unit " + classUnitComment.classUnitId() + " has already been commented by " + classUnitComment.commentedBy());
        } else {
            var entity = classUnitCommentRepository.save(classUnitComment.toEntity());
            return ClassUnitComment.from(entity);
        }
    }

    @Override
    public ClassUnitComment getComment(String classUnitId, String attendedBy) {
        var existingCommentEntityOptional = classUnitCommentRepository.findByAttendedByAndClassUnitId(attendedBy, classUnitId);
        return existingCommentEntityOptional.map(ClassUnitComment::from)
                .orElseGet(() -> ClassUnitComment.ofEmpty(classUnitId, attendedBy));
    }

    @Override
    public List<ClassUnitComment> getComments(List<String> classUnitIds, String attendedBy) {
        var existingCommentEntities = classUnitCommentRepository.findByAttendedByAndClassUnitIdIn(attendedBy, classUnitIds);

        var commentMap = existingCommentEntities.stream()
                .map(ClassUnitComment::from)
                .collect(Collectors.toMap(ClassUnitComment::classUnitId, Function.identity()));

        return classUnitIds.stream()
                .map(id -> commentMap.getOrDefault(id, ClassUnitComment.ofEmpty(id, attendedBy)))
                .toList();
    }
}