package com.firstedu.marsladder.diting.xgjclass.service;

import com.firstedu.marsladder.diting.classroom.service.domain.Classroom;
import com.firstedu.marsladder.diting.xgjclass.service.domain.StudentClassTasksFeedBack;
import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;

import java.time.LocalDateTime;
import java.util.List;

public interface ClassService {
    XiaoguanjiaClass getXiaoguanjiaClassByClassId(String classId);

    List<XiaoguanjiaClass> getClasses();

    List<XiaoguanjiaClass> getClassesByCampusOfTeacher();

    List<Classroom> getMarsladderClassrooms(String classId);

    List<StudentClassTasksFeedBack> getStudentClassTasksFeedBack(String classId, String studentId);

    void generateTaskFeedbacksForBoundedClasses(LocalDateTime startTime, LocalDateTime endTime);

    void generateTaskFeedbackForStudentInClass(String classId, String marsladderStudentId, LocalDateTime startTime, LocalDateTime endTime);
}
