package com.firstedu.marsladder.diting.xgjclass.repository;

import com.firstedu.marsladder.diting.xgjclass.repository.entity.ClassUnitCommentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ClassUnitCommentRepository extends JpaRepository<ClassUnitCommentEntity, String> {
    Optional<ClassUnitCommentEntity> findByAttendedByAndClassUnitId(String userId, String classUnitId);

    List<ClassUnitCommentEntity> findByAttendedByAndClassUnitIdIn(String userId, List<String> classUnitId);
}
