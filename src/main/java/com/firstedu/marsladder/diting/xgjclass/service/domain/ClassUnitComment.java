package com.firstedu.marsladder.diting.xgjclass.service.domain;

import com.firstedu.marsladder.diting.xgjclass.repository.entity.ClassUnitCommentEntity;
import lombok.Builder;

@Builder
public record ClassUnitComment(
        String id,
        String commentedBy,
        String classUnitId,
        String attendedBy,
        Float score,
        String comment
) {
    public static ClassUnitComment from(ClassUnitCommentEntity classUnitCommentEntity) {
        return new ClassUnitComment(
                classUnitCommentEntity.getId(),
                classUnitCommentEntity.getCommentedBy(),
                classUnitCommentEntity.getClassUnitId(),
                classUnitCommentEntity.getAttendedBy(),
                classUnitCommentEntity.getScore(),
                classUnitCommentEntity.getComment()
        );
    }

    public static ClassUnitComment ofEmpty(String classUnitId, String attendedBy) {
        return new ClassUnitComment(null, null, classUnitId, attendedBy, null, null);
    }

    public ClassUnitCommentEntity toEntity() {
        return new ClassUnitCommentEntity(
                null,
                classUnitId,
                commentedBy,
                attendedBy,
                score,
                comment,
                null,
                null
        );
    }
}
