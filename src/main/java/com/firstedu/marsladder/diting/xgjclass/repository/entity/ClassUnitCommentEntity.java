package com.firstedu.marsladder.diting.xgjclass.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

import java.time.LocalDateTime;

@Entity(name = "class_unit_comment")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Getter
public class ClassUnitCommentEntity {
    @Id
    @UuidGenerator
    String id;

    String classUnitId; // another name for xiaogaunjia course id

    String commentedBy;

    String attendedBy;

    Float score;

    String comment;

    @CreationTimestamp
    LocalDateTime createdAt;

    @UpdateTimestamp
    LocalDateTime updatedAt;
}
