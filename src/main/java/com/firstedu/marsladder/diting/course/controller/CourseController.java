package com.firstedu.marsladder.diting.course.controller;

import com.firstedu.marsladder.diting.classroom.controller.dto.ClassroomResponse;
import com.firstedu.marsladder.diting.classroom.service.ClassroomService;
import com.firstedu.marsladder.diting.client.falcon.FalconClient;
import com.firstedu.marsladder.diting.client.falcon.dto.RealusStudentProfile;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserRole;
import com.firstedu.marsladder.diting.course.controller.dto.CourseScheduleByDayResponse;
import com.firstedu.marsladder.diting.course.controller.dto.CourseScheduleResponse;
import com.firstedu.marsladder.diting.course.service.CourseService;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByMonthFilter;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.YearMonth;
import java.time.ZoneId;
import java.util.List;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class CourseController {
    private final CourseService courseService;
    private final FalconClient falconClient;
    private final ClassroomService classroomService;

    @ResponseStatus(OK)
    @GetMapping("/course-schedules")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<CourseScheduleResponse> getCourseSchedules(
            @RequestParam(required = false) Long startTimeBefore,
            @RequestParam(required = false) Long startTimeAfter,
            @RequestParam(required = false) String classId,
            @RequestParam(required = false) List<String> studentIds,
            @RequestParam(required = false) List<String> xiaogjStudentIds,
            @PageableDefault(size = Integer.MAX_VALUE, page = 0, sort = {"courseStartTime"}, direction = Sort.Direction.ASC) Pageable pageable
    ) {
        var xiaogjStudentIdsCurrentUserCanRead = getXiaogjStudentIdsCurrentUserCanRead(falconClient.getUser(), xiaogjStudentIds);
        return courseService.getCourseSchedulesByPage(
                        CourseScheduleFilter.builder().xiaogjStudentIds(xiaogjStudentIdsCurrentUserCanRead).startTimeBefore(startTimeBefore).startTimeAfter(startTimeAfter).classId(classId).build(),
                        pageable)
                .stream().map(CourseScheduleResponse::from).toList();
    }

    @ResponseStatus(OK)
    @GetMapping("/course-schedules-by-month")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<CourseScheduleByDayResponse> getCourseSchedulesByMonth(
            @RequestParam YearMonth yearMonth,
            @RequestParam(required = false) String classId,
            @RequestParam(required = false) List<String> studentIds,
            @RequestParam(required = false) List<String> xiaogjStudentIds
    ) {
        var xiaogjStudentIdsCurrentUserCanRead = getXiaogjStudentIdsCurrentUserCanRead(falconClient.getUser(), xiaogjStudentIds);
        return courseService.getCourseSchedulesByMonth(CourseScheduleByMonthFilter.builder().xiaogjStudentIds(xiaogjStudentIdsCurrentUserCanRead).classId(classId).build(), yearMonth, ZoneId.of("Australia/Melbourne"))
                .stream().map(CourseScheduleByDayResponse::from).toList();
    }

    @ResponseStatus(OK)
    @GetMapping("/courses/{courseId}/marsladder-classrooms")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'REALUS_PARENT')")
    public List<ClassroomResponse> getClassroomsByCourseId(
            @PathVariable String courseId) {

        //courseId --> shiftId
        String xiaogjiShiftId = courseId;
        return classroomService.getClassroomsByShiftId(xiaogjiShiftId).stream()
                .map(ClassroomResponse::from)
                .toList();
    }

    private List<String> getXiaogjStudentIdsCurrentUserCanRead(UserDto currentUser, List<String> xiaogjStudentIds) {
        if (currentUser.roles().contains(UserRole.REALUS_STUDENT)) {
            return List.of(currentUser.realusStudentProfile().xiaogjStudentId());
        } else if (currentUser.roles().contains(UserRole.REALUS_PARENT)) {
            if (xiaogjStudentIds != null && !xiaogjStudentIds.isEmpty()) {
                return currentUser.realusParentProfile().students().stream()
                        .map(RealusStudentProfile::xiaogjStudentId)
                        .filter(xiaogjStudentIds::contains).toList();
            } else {
                return currentUser.realusParentProfile().students().stream().map(RealusStudentProfile::xiaogjStudentId).toList();
            }
        }
        return Collections.emptyList();
    }
}
