package com.firstedu.marsladder.diting.course.service.domain;

public enum AbsentCause {
    PERSONAL_AFFAIR,
    SICK,
    TRUANT,
    PUBLIC_TRANSPORT_BREAKDOWN;

    public static AbsentCause of(String value) {
        switch (value) {
            case "6281f3b3-03e2-461e-9cf8-a5eedfd62f8e" -> {
                return PERSONAL_AFFAIR;
            }
            case "e2696981-98a7-42f5-8dbf-be44389e01db" -> {
                return SICK;
            }
            case "5793244c-09da-4c09-a4f3-cf0ad78cfd11" -> {
                return TRUANT;
            }
            case "70ab9ef3-8a7b-49f2-812a-c1377363a29f" -> {
                return PUBLIC_TRANSPORT_BREAKDOWN;
            }
            default -> {
                return null;
            }
        }
    }
}
