package com.firstedu.marsladder.diting.course.service;

import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByDay;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByMonthFilter;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import org.springframework.data.domain.Pageable;

import java.time.YearMonth;
import java.time.ZoneId;
import java.util.List;

public interface CourseService {
    List<CourseSchedule> getCourseSchedules(CourseScheduleFilter filter);

    List<CourseSchedule> getCourseSchedulesByPage(CourseScheduleFilter filter, Pageable pageable);

    List<CourseScheduleByDay> getCourseSchedulesByMonth(CourseScheduleByMonthFilter filter, YearMonth yearMonth, ZoneId zoneId);
}
