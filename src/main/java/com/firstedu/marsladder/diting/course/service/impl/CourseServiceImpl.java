package com.firstedu.marsladder.diting.course.service.impl;

import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByDay;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByMonthFilter;
import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleFilter;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailRepository;
import com.firstedu.marsladder.diting.course.service.CourseService;
import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailSpecification.hasClassIdEqualTo;
import static com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailSpecification.hasCourseDeletedEqualTo;
import static com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailSpecification.hasCourseIsFinishedNotEqualTo;
import static com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailSpecification.hasCourseStartTimeGreaterThanOrEqualTo;
import static com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailSpecification.hasCourseStartTimeLessThanOrEqualTo;
import static com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentDetailSpecification.hasStudentIdIn;

@Service
@RequiredArgsConstructor
public class CourseServiceImpl implements CourseService {
    private final XiaoguanjiaCourseStudentDetailRepository xiaoguanjiaCourseStudentDetailRepository;

    @Override
    public List<CourseSchedule> getCourseSchedules(CourseScheduleFilter filter) {
        var specification = Specification.where(
                hasStudentIdIn(filter.xiaogjStudentIds())
                        .and(hasCourseStartTimeLessThanOrEqualTo(filter.startTimeBefore()))
                        .and(hasCourseStartTimeGreaterThanOrEqualTo(filter.startTimeAfter()))
                        .and(hasCourseDeletedEqualTo(false))
                        .and(hasClassIdEqualTo(filter.classId()))
                        .and(hasCourseIsFinishedNotEqualTo(2)));

        var courseStudentDetails = xiaoguanjiaCourseStudentDetailRepository.findAll(specification);
        return courseStudentDetails.stream().map(CourseSchedule::from).toList();
    }

    @Override
    public List<CourseSchedule> getCourseSchedulesByPage(CourseScheduleFilter filter, Pageable pageable) {
        var specification = Specification.where(
                hasStudentIdIn(filter.xiaogjStudentIds())
                        .and(hasCourseStartTimeLessThanOrEqualTo(filter.startTimeBefore()))
                        .and(hasCourseStartTimeGreaterThanOrEqualTo(filter.startTimeAfter()))
                        .and(hasCourseDeletedEqualTo(false))
                        .and(hasClassIdEqualTo(filter.classId()))
                        .and(hasCourseIsFinishedNotEqualTo(2)));
        var courseStudentDetails = xiaoguanjiaCourseStudentDetailRepository.findAll(specification, pageable);
        return courseStudentDetails.stream().map(CourseSchedule::from).toList();
    }

    @Override
    public List<CourseScheduleByDay> getCourseSchedulesByMonth(CourseScheduleByMonthFilter filter, YearMonth yearMonth, ZoneId zoneId) {
        var utcZone = ZoneId.of("UTC");
        var startDateTime = LocalDateTime.of(yearMonth.atDay(1), LocalTime.MIDNIGHT)
                .atZone(zoneId)
                .withZoneSameInstant(utcZone)
                .toEpochSecond();

        var endDateTime = LocalDateTime.of(yearMonth.atEndOfMonth(), LocalTime.MAX)
                .atZone(zoneId)
                .withZoneSameInstant(utcZone)
                .toEpochSecond();

        var specification = Specification.where(
                hasStudentIdIn(filter.xiaogjStudentIds())
                        .and(hasCourseStartTimeLessThanOrEqualTo(endDateTime))
                        .and(hasCourseStartTimeGreaterThanOrEqualTo(startDateTime))
                        .and(hasCourseDeletedEqualTo(false))
                        .and(hasClassIdEqualTo(filter.classId()))
                        .and(hasCourseIsFinishedNotEqualTo(2)));

        var courseStudentDetails = xiaoguanjiaCourseStudentDetailRepository.findAll(specification);

        List<CourseScheduleByDay> courseScheduleByDays = new ArrayList<>();

        int daysInMonth = yearMonth.lengthOfMonth();
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate currentDate = yearMonth.atDay(day);

            List<CourseSchedule> dailySchedules = courseStudentDetails.stream()
                    .filter(detail -> isCourseOnDate(detail, currentDate, zoneId))
                    .map(CourseSchedule::from)
                    .sorted(Comparator.comparing(CourseSchedule::startTime))
                    .toList();

            courseScheduleByDays.add(new CourseScheduleByDay(currentDate, dailySchedules));
        }
        return courseScheduleByDays;
    }

    private boolean isCourseOnDate(XiaoguanjiaCourseStudentDetailEntity detail, LocalDate date, ZoneId zoneId) {
        ZonedDateTime courseStartTimeInZone = ZonedDateTime.of(detail.getCourseStartTime(), ZoneOffset.UTC).withZoneSameInstant(zoneId);
        return !courseStartTimeInZone.toLocalDate().isBefore(date) && !courseStartTimeInZone.toLocalDate().isAfter(date);
    }
}
