package com.firstedu.marsladder.diting.course.service.domain;

public enum CourseCostUnit {
    HOUR,
    COUNT,
    MONTH;

    public static CourseCostUnit of(int value) {
        switch (value) {
            case 1 -> {
                return HOUR;
            }
            case 2 -> {
                return COUNT;
            }
            case 3 -> {
                return MONTH;
            }
            default -> {
                return null;
            }
        }
    }
}
