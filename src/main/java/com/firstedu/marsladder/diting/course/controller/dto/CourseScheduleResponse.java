package com.firstedu.marsladder.diting.course.controller.dto;

import com.firstedu.marsladder.diting.course.service.domain.AbsentCause;
import com.firstedu.marsladder.diting.course.service.domain.CourseCostUnit;
import com.firstedu.marsladder.diting.course.service.domain.CourseSchedule;
import com.firstedu.marsladder.diting.subject.service.domain.SubjectType;
import lombok.Builder;

import java.time.ZoneOffset;

@Builder
public record CourseScheduleResponse(
        String xiaogjStudentId,
        String studentName,
        String classId,
        String className,
        String classUnitId,
        Long startTime,
        Long endTime,
        String course,
        String campus,
        String classroom,
        String teacher,
        boolean isAttend,
        AbsentCause absentCause,
        CourseCostUnit courseCostUnit,
        double courseCost,
        String subjectId,
        SubjectType subjectType,
        boolean isAttendanceTaken
) {
    public static CourseScheduleResponse from(CourseSchedule courseSchedule) {
        return CourseScheduleResponse.builder()
                .xiaogjStudentId(courseSchedule.xiaogjStudentId())
                .studentName(courseSchedule.studentName())
                .classId(courseSchedule.classId())
                .className(courseSchedule.className())
                .classUnitId(courseSchedule.classUnitId())
                .startTime(courseSchedule.startTime().toEpochSecond(ZoneOffset.UTC))
                .endTime(courseSchedule.endTime().toEpochSecond(ZoneOffset.UTC))
                .course(courseSchedule.course())
                .campus(courseSchedule.campus())
                .classroom(courseSchedule.classroom())
                .teacher(courseSchedule.teacher())
                .isAttend(courseSchedule.isAttend())
                .absentCause(courseSchedule.absentCause())
                .courseCostUnit(courseSchedule.courseCostUnit())
                .courseCost(courseSchedule.courseCost())
                .subjectId(courseSchedule.subjectId())
                .subjectType(courseSchedule.subjectType())
                .isAttendanceTaken(courseSchedule.isClassUnitFinished())
                .build();
    }
}
