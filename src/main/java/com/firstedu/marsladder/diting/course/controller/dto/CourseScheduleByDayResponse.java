package com.firstedu.marsladder.diting.course.controller.dto;

import com.firstedu.marsladder.diting.course.service.domain.CourseScheduleByDay;
import lombok.Builder;

import java.time.LocalDate;
import java.util.List;

@Builder
public record CourseScheduleByDayResponse(
        LocalDate date,
        List<CourseScheduleResponse> courseSchedules
) {
    public static CourseScheduleByDayResponse from(CourseScheduleByDay courseScheduleByDay) {
        return CourseScheduleByDayResponse.builder()
                .date(courseScheduleByDay.date())
                .courseSchedules(courseScheduleByDay.courseSchedules().stream().map(CourseScheduleResponse::from).toList())
                .build();
    }
}
