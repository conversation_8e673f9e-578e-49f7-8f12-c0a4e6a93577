package com.firstedu.marsladder.diting.course.service.domain;

import com.firstedu.marsladder.diting.subject.service.domain.SubjectType;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailEntity;
import lombok.Builder;

import java.time.LocalDateTime;

@Builder
public record CourseSchedule(
        String courseId,
        String xiaogjStudentId,
        String studentName,
        String classId,
        String className,
        String classUnitId,
        boolean isClassUnitFinished,
        LocalDateTime startTime,
        LocalDateTime endTime,
        String course,
        String campus,
        String classroom,
        String teacher,
        boolean isAttend,
        AbsentCause absentCause,
        CourseCostUnit courseCostUnit,
        double courseCost,
        String subjectId,
        SubjectType subjectType
) {
    public static CourseSchedule from(XiaoguanjiaCourseStudentDetailEntity courseStudentDetail) {
        var courseCostUnit = CourseCostUnit.of(courseStudentDetail.getShiftUnit());
        var courseCost = switch (courseCostUnit) {
            case HOUR -> courseStudentDetail.getStudentCost() / 60;
            case COUNT, MONTH -> courseStudentDetail.getStudentCost();
            case null -> 0.0;
        };
        return CourseSchedule.builder()
                .courseId(courseStudentDetail.getId().getCourseId())
                .xiaogjStudentId(courseStudentDetail.getId().getStudentId())
                .studentName(courseStudentDetail.getStudentName())
                .classId(courseStudentDetail.getClassId())
                .className(courseStudentDetail.getClassName())
                .classUnitId(courseStudentDetail.getId().getCourseId())
                .isClassUnitFinished(courseStudentDetail.getCourseIsFinished() == 1)
                .startTime(courseStudentDetail.getCourseStartTime())
                .endTime(courseStudentDetail.getCourseEndTime())
                .course(courseStudentDetail.getShiftName())
                .campus(courseStudentDetail.getCampusName())
                .classroom(courseStudentDetail.getClassroomName())
                .teacher(courseStudentDetail.getCourseTeacherName())
                .isAttend(courseStudentDetail.getStudentIsAttend() != 0)
                .absentCause(AbsentCause.of(courseStudentDetail.getStudentAbsentCauseId()))
                .courseCostUnit(CourseCostUnit.of(courseStudentDetail.getShiftUnit()))
                .courseCost(courseCost)
                .subjectId(courseStudentDetail.getSubjectId())
                .subjectType(courseStudentDetail.getSubjectName() == null ? null : SubjectType.fromChineseName(courseStudentDetail.getSubjectName()))
                .build();
    }
}
