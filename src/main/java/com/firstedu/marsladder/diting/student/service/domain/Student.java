package com.firstedu.marsladder.diting.student.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;

public record Student(
        String id,
        String name,
        String serial,
        String userName,
        XiaoguanjiaStudentStatus status,
        String campusName,
        String campusId
) {
    public static Student from(XiaoguanjiaStudentEntity xiaoguanjiaStudentEntity) {
        return from(xiaoguanjiaStudentEntity, null);
    }

    public static Student from(XiaoguanjiaStudentEntity xiaoguanjiaStudentEntity, String campusName) {
        return new Student(
                xiaoguanjiaStudentEntity.getId(),
                xiaoguanjiaStudentEntity.getName(),
                xiaoguanjiaStudentEntity.getSerial(),
                xiaoguanjiaStudentEntity.getUserName(),
                xiaoguanjiaStudentEntity.getStatus(),
                campusName,
                xiaoguanjiaStudentEntity.getCampusId()
        );
    }
}
