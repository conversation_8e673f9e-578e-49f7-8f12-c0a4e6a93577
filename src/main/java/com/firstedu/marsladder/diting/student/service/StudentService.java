package com.firstedu.marsladder.diting.student.service;

import com.firstedu.marsladder.diting.student.service.domain.Student;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface StudentService {

    Page<Student> getStudentByName(Pageable pageable, String name);

    Student getStudentById(String studentId);

    List<Student> getStudentsByIds(List<String> studentIds);
}
