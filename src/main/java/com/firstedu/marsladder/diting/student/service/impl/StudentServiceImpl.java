package com.firstedu.marsladder.diting.student.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaStudentNotFoundException;
import com.firstedu.marsladder.diting.student.service.StudentService;
import com.firstedu.marsladder.diting.student.service.domain.Student;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaDepartmentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaDepartmentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class StudentServiceImpl implements StudentService {
    private final XiaoguanjiaStudentRepository xiaoguanjiaStudentRepository;
    private final XiaoguanjiaDepartmentRepository xiaoguanjiaDepartmentRepository;

    @Override
    public Page<Student> getStudentByName(Pageable pageable, String name) {
        List<XiaoguanjiaDepartmentEntity> xiaoguanjiaDepartmentEntityList = xiaoguanjiaDepartmentRepository.findAll();
        
        return StringUtils.isNotEmpty(name) ?
                xiaoguanjiaStudentRepository.findByNameLikeIgnoreCaseAndStatusIn(pageable, "%" + name + "%", List.of(XiaoguanjiaStudentStatus.ENROLLED, XiaoguanjiaStudentStatus.AUDITION))
                        .map(entity -> Student.from(entity, getCampusName(entity.getCampusId(), xiaoguanjiaDepartmentEntityList)))
                : xiaoguanjiaStudentRepository.findAllByStatusIn(pageable, List.of(XiaoguanjiaStudentStatus.ENROLLED, XiaoguanjiaStudentStatus.AUDITION))
                        .map(entity -> Student.from(entity, getCampusName(entity.getCampusId(), xiaoguanjiaDepartmentEntityList)));
    }

    @Override
    public Student getStudentById(String studentId) {
        List<XiaoguanjiaDepartmentEntity> xiaoguanjiaDepartmentEntityList = xiaoguanjiaDepartmentRepository.findAll();

        return xiaoguanjiaStudentRepository.findById(studentId)
                .map(entity -> Student.from(entity, getCampusName(entity.getCampusId(), xiaoguanjiaDepartmentEntityList)))
                .orElseThrow(() -> new XiaoguanjiaStudentNotFoundException("Student not found by id: " + studentId));
    }

    @Override
    public List<Student> getStudentsByIds(List<String> studentIds) {
        List<XiaoguanjiaDepartmentEntity> xiaoguanjiaDepartmentEntityList = xiaoguanjiaDepartmentRepository.findAll();

        return xiaoguanjiaStudentRepository.findAllByIdIn(studentIds).stream()
                .map(entity -> Student.from(entity, getCampusName(entity.getCampusId(), xiaoguanjiaDepartmentEntityList)))
                .toList();
    }

    private String getCampusName(String campusId, List<XiaoguanjiaDepartmentEntity> departmentList) {
        if (campusId == null || departmentList == null) {
            return null;
        }
        
        return departmentList.stream()
                .filter(department -> campusId.equals(department.getId()))
                .findFirst()
                .map(XiaoguanjiaDepartmentEntity::getName)
                .orElse(null);
    }
}
