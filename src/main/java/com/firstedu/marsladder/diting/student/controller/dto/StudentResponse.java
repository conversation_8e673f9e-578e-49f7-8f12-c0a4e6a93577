package com.firstedu.marsladder.diting.student.controller.dto;

import com.firstedu.marsladder.diting.student.service.domain.Student;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;

public record StudentResponse(
        String id,
        String name,
        String serial,
        String userName,
        XiaoguanjiaStudentStatus status,
        String campusName,
        String campusId
) {
    public static StudentResponse from(Student student) {
        return new StudentResponse(
                student.id(),
                student.name(),
                student.serial(),
                student.userName(),
                student.status(),
                student.campusName(),
                student.campusId()
        );
    }
}
