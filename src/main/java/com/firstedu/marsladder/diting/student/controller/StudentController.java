package com.firstedu.marsladder.diting.student.controller;

import com.firstedu.marsladder.diting.student.controller.dto.StudentPageResponse;
import com.firstedu.marsladder.diting.student.controller.dto.StudentResponse;
import com.firstedu.marsladder.diting.student.service.StudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
@Log4j2
public class StudentController {
    private final StudentService studentService;

    @ResponseStatus(OK)
    @GetMapping("/students")
    @PreAuthorize("hasRole('ADMIN')")
    public StudentPageResponse searchStudentsByName(
            @PageableDefault() Pageable pageable,
            @RequestParam(required = false) String name) {

        String decodedName = Optional.ofNullable(name).map(n ->URLDecoder.decode(name, StandardCharsets.UTF_8)).orElse("");
        var pageResponse = studentService.getStudentByName(pageable, decodedName);

        return new StudentPageResponse(
                pageResponse.getTotalElements(),
                pageResponse.getContent().stream().map(StudentResponse::from).toList()
        );
    }

    @ResponseStatus(OK)
    @GetMapping("/students/{studentId}")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'ADMIN', 'REALUS_PARENT')")
    public StudentResponse getXiaoguanjiaStudent(
            @PathVariable String studentId) {

        return StudentResponse.from(studentService.getStudentById(studentId));
    }

    @ResponseStatus(OK)
    @GetMapping("/students/batch")
    @PreAuthorize("hasAnyRole('REALUS_STUDENT', 'ADMIN', 'REALUS_PARENT')")
    public List<StudentResponse> getXiaoguanjiaStudents(
            @RequestParam List<String> studentIds) {

        return studentService.getStudentsByIds(studentIds).stream()
                .map(StudentResponse::from)
                .toList();
    }
}
