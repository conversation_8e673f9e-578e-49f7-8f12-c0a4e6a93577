package com.firstedu.marsladder.diting.employee.service.impl;

import com.firstedu.marsladder.diting.employee.service.EmployeeService;
import com.firstedu.marsladder.diting.employee.service.domain.Employee;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEmployeeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaEmployeeEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class EmployeeServiceImpl implements EmployeeService {
    private final XiaoguanjiaEmployeeRepository xiaoguanjiaEmployeeRepository;

    @Override
    public Employee getEmployeeById(String employeeId) {
        Optional<XiaoguanjiaEmployeeEntity> employeeEntityOptional = xiaoguanjiaEmployeeRepository.findById(employeeId);
        if (employeeEntityOptional.isEmpty()) {
            log.error("Employee with ID {} not found", employeeId);
            return new Employee(employeeId, "");
        }
        return Employee.from(employeeEntityOptional.get());
    }
}
