package com.firstedu.marsladder.diting.leads.controller.dto;

import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import lombok.Builder;

import java.util.List;

@Builder
public record VceMockLeadDto(
         String id,
         String firstName,
         String lastName,
         String school,
         String email,
         String phoneNumber,
         List<String> subjects) {

    public static VceMockLeadDto from(VceMockLead lead) {
        return VceMockLeadDto.builder()
                .id(lead.getId())
                .firstName(lead.getFirstName())
                .lastName(lead.getLastName())
                .school(lead.getSchool())
                .email(lead.getEmail())
                .phoneNumber(lead.getPhoneNumber())
                .subjects(lead.getSubjects())
                .build();
    }
}
