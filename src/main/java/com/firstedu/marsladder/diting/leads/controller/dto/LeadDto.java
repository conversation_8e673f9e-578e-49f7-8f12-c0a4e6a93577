package com.firstedu.marsladder.diting.leads.controller.dto;

import com.firstedu.marsladder.diting.leads.service.domain.Campus;
import com.firstedu.marsladder.diting.leads.service.domain.ReferralSource;
import com.firstedu.marsladder.diting.leads.service.domain.Lead;
import lombok.Builder;

import java.util.List;

@Builder
public record LeadDto(
        String id,
        Integer grade,
        Campus campus,
        String name,
        String email,
        String phone,
        String postCode,
        List<ReferralSource> referralSources,
        String otherReferralSource,
        String inquiry) {

    public static LeadDto from(Lead lead) {
        return LeadDto.builder()
                .id(lead.getId())
                .grade(lead.getGrade())
                .campus(lead.getCampus())
                .name(lead.getName())
                .email(lead.getEmail())
                .phone(lead.getPhone())
                .postCode(lead.getPostCode())
                .referralSources(lead.getReferralSources())
                .otherReferralSource(lead.getOtherReferralSource())
                .inquiry(lead.getInquiry())
                .build();
    }
}
