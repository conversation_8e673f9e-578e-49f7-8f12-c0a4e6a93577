package com.firstedu.marsladder.diting.leads.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UuidGenerator;

import java.util.List;

import static org.hibernate.type.SqlTypes.JSON;

@Entity(name = "vce_mock_lead_detail")
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class VceMockLeadEntity {
    @Id
    @UuidGenerator
    private String id;

    private String firstName;

    private String lastName;

    private String school;

    private String email;

    private String phoneNumber;

    @JdbcTypeCode(JSON)
    private List<String> subjects;
}
