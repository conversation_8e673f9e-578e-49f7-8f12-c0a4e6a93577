package com.firstedu.marsladder.diting.leads.controller.dto;

import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;

import java.util.List;

@Builder
public record VceMockLeadCreateOrUpdateRequest(
        @NotBlank(message = "First name is required")
        @Size(max = 255, message = "First name must be at most 255 characters")
        String firstName,
        @NotBlank(message = "Last name is required")
        @Size(max = 255, message = "Last name must be at most 255 characters")
        String lastName,
        @NotBlank(message = "School is required")
        String school,
        @NotBlank(message = "Email is required")
        @Email(message = "Invalid email")
        String email,
        @NotBlank(message = "Phone number is required")
        @Pattern(
                regexp = "^((\\+61|0)[2-478])(\\s?\\d){8}$",
                message = "Invalid Australian phone number"
        )
        String phoneNumber,
        @Size(min = 1, message = "At least one subject must be selected")
        List<String> subjects
) {

    public VceMockLead toVceMockLead() {
        return VceMockLead.builder()
                .firstName(firstName)
                .lastName(lastName)
                .school(school)
                .email(email)
                .phoneNumber(phoneNumber)
                .subjects(subjects)
                .build();
    }
}
