package com.firstedu.marsladder.diting.leads.service.domain;

import com.firstedu.marsladder.diting.leads.repository.entity.VceMockLeadEntity;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;

@Builder(toBuilder = true)
@Getter
@EqualsAndHashCode
public class VceMockLead {
    private String id;
    private String firstName;
    private String lastName;
    private String school;
    private String email;
    private String phoneNumber;
    private List<String> subjects;

    public VceMockLeadEntity toEntity() {
        return VceMockLeadEntity.builder()
                .firstName(firstName)
                .lastName(lastName)
                .school(school)
                .email(email)
                .phoneNumber(phoneNumber)
                .subjects(subjects)
                .build();
    }

    public static VceMockLead from(VceMockLeadEntity entity) {
        return VceMockLead.builder()
                .id(entity.getId())
                .firstName(entity.getFirstName())
                .lastName(entity.getLastName())
                .email(entity.getEmail())
                .school(entity.getSchool())
                .phoneNumber(entity.getPhoneNumber())
                .subjects(entity.getSubjects())
                .build();
    }
}
