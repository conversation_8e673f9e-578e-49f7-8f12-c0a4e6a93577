package com.firstedu.marsladder.diting.leads.repository;

import com.firstedu.marsladder.diting.leads.repository.entity.VceMockLeadEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface VceMockLeadRepository extends JpaRepository<VceMockLeadEntity, String> {

    Boolean existsByEmail(String email);

    Optional<VceMockLeadEntity> findByEmail(String email);
}
