package com.firstedu.marsladder.diting.leads.controller;

import com.firstedu.marsladder.diting.leads.controller.dto.LeadCreationRequest;
import com.firstedu.marsladder.diting.leads.controller.dto.LeadDto;
import com.firstedu.marsladder.diting.leads.controller.dto.VceMockLeadCreateOrUpdateRequest;
import com.firstedu.marsladder.diting.leads.controller.dto.VceMockLeadDto;
import com.firstedu.marsladder.diting.leads.service.LeadService;
import com.firstedu.marsladder.diting.leads.service.VceMockLeadService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static java.net.URI.create;

@RestController
@RequiredArgsConstructor
public class LeadController {

    private final LeadService leadService;
    private final VceMockLeadService vceMockLeadService;

    @CrossOrigin
    @PostMapping(value = "public/leads")
    public ResponseEntity<LeadDto> createLead(@RequestBody LeadCreationRequest request) {
        var lead = leadService.createLead(request.toLead());
        return ResponseEntity
                .created(create("/leads/" + lead.getId()))
                .body(LeadDto.from(lead));
    }

    @CrossOrigin
    @PostMapping(value = "public/vce-mock-leads")
    public ResponseEntity<VceMockLeadDto> createOrUpdateVceMockLead(@RequestBody VceMockLeadCreateOrUpdateRequest request) {
        var lead = vceMockLeadService.createOrUpdateVceMockLead(request.toVceMockLead());
        return ResponseEntity.ok(VceMockLeadDto.from(lead));
    }
}
