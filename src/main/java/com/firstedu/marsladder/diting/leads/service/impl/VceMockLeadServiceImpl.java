package com.firstedu.marsladder.diting.leads.service.impl;

import com.firstedu.marsladder.diting.klaviyo.event.VceMockKlaviyoProfileCreatedAndAddedToListEvent;
import com.firstedu.marsladder.diting.leads.exception.LeadNotFoundException;
import com.firstedu.marsladder.diting.leads.repository.VceMockLeadRepository;
import com.firstedu.marsladder.diting.leads.repository.entity.VceMockLeadEntity;
import com.firstedu.marsladder.diting.leads.service.VceMockLeadService;
import com.firstedu.marsladder.diting.leads.service.domain.VceMockLead;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class VceMockLeadServiceImpl implements VceMockLeadService {

    private final VceMockLeadRepository vceMockleadRepository;
    private final ApplicationEventPublisher publisher;

    @Override
    public VceMockLead createOrUpdateVceMockLead(VceMockLead lead) {
        var updatedLead = vceMockleadRepository.existsByEmail(lead.getEmail())
                ? updateLead(lead.getEmail(), lead)
                : VceMockLead.from(vceMockleadRepository.save(lead.toEntity()));

        publisher.publishEvent(new VceMockKlaviyoProfileCreatedAndAddedToListEvent(this, updatedLead));
        return updatedLead;
    }

    public VceMockLead updateLead(String email, VceMockLead lead) {
        var existedLeadEntity = vceMockleadRepository.findByEmail(email)
                .orElseThrow(() -> new LeadNotFoundException("vce mock Lead not found by email: " + email));

        var updatedLeadEntity = VceMockLeadEntity.builder()
                .id(existedLeadEntity.getId())
                .firstName(lead.getFirstName())
                .lastName(lead.getLastName())
                .school(lead.getSchool())
                .email(lead.getEmail())
                .phoneNumber(lead.getPhoneNumber())
                .subjects(lead.getSubjects())
                .build();
        return VceMockLead.from(vceMockleadRepository.save(updatedLeadEntity));
    }
}
