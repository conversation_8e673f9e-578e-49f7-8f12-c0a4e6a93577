package com.firstedu.marsladder.diting.leads.service.impl;

import com.firstedu.marsladder.diting.klaviyo.event.ProfileCreatedAndAddedToListEvent;
import com.firstedu.marsladder.diting.klaviyo.profiles.createorupdate.KlaviyoProfile;
import com.firstedu.marsladder.diting.leads.exception.LeadNotFoundException;
import com.firstedu.marsladder.diting.leads.repository.LeadRepository;
import com.firstedu.marsladder.diting.leads.repository.entity.LeadEntity;
import com.firstedu.marsladder.diting.leads.service.LeadService;
import com.firstedu.marsladder.diting.leads.service.domain.Lead;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class LeadServiceImpl implements LeadService {

    private final LeadRepository leadRepository;
    private final ApplicationEventPublisher publisher;

    @Override
    public Lead createLead(Lead lead) {
        Lead updatedLead = leadRepository.existsByEmail(lead.getEmail())
                ? updateLead(lead.getEmail(), lead)
                : Lead.from(leadRepository.save(lead.toEntity()));

        var klaviyoProfile = KlaviyoProfile.builder()
                .grade(updatedLead.getGrade())
                .campus(updatedLead.getCampus())
                .name(updatedLead.getName())
                .email(updatedLead.getEmail())
                .phone(updatedLead.getPhone())
                .postCode(updatedLead.getPostCode())
                .referralSource(updatedLead.getReferralSources())
                .otherReferralSource(updatedLead.getOtherReferralSource())
                .inquiry(updatedLead.getInquiry())
                .build();
        publisher.publishEvent(new ProfileCreatedAndAddedToListEvent(this, klaviyoProfile));
        return updatedLead;
    }

    @Override
    public Lead updateLead(String email, Lead lead) {
        var existedLeadEntity = Optional.ofNullable(leadRepository.findByEmail(email))
                .orElseThrow(() -> new LeadNotFoundException("Lead not found by email: " + email));

        var updatedLeadEntity = LeadEntity.builder()
                .id(existedLeadEntity.getId())
                .grade(lead.getGrade())
                .campus(lead.getCampus())
                .name(lead.getName())
                .email(lead.getEmail())
                .phone(lead.getPhone())
                .postCode(lead.getPostCode())
                .referralSources(lead.getReferralSources())
                .otherReferralSource(lead.getOtherReferralSource())
                .createdAt(existedLeadEntity.getCreatedAt())
                .inquiry(lead.getInquiry())
                .build();
        return Lead.from(leadRepository.save(updatedLeadEntity));
    }
}
