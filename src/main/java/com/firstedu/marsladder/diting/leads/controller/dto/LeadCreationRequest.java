package com.firstedu.marsladder.diting.leads.controller.dto;

import com.firstedu.marsladder.diting.leads.service.domain.Campus;
import com.firstedu.marsladder.diting.leads.service.domain.ReferralSource;
import com.firstedu.marsladder.diting.leads.service.domain.Lead;
import lombok.Builder;

import java.util.List;

@Builder
public record LeadCreationRequest(
        Integer grade,
        Campus campus,
        String name,
        String email,
        String phone,
        String postCode,
        List<ReferralSource> referralSources,
        String otherReferralSource,
        String inquiry) {

    public Lead toLead() {
        return Lead.builder()
                .grade(grade)
                .campus(campus)
                .name(name)
                .email(email)
                .phone(phone)
                .postCode(postCode)
                .referralSources(referralSources)
                .otherReferralSource(otherReferralSource)
                .inquiry(inquiry)
                .build();
    }
}
