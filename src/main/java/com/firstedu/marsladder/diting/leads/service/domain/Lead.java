package com.firstedu.marsladder.diting.leads.service.domain;

import com.firstedu.marsladder.diting.leads.repository.entity.LeadEntity;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;

@Builder(toBuilder = true)
@Getter
@EqualsAndHashCode
public class Lead {
    private String id;
    private Integer grade;
    private Campus campus;
    private String name;
    private String email;
    private String phone;
    private String postCode;
    private List<ReferralSource> referralSources;
    private String otherReferralSource;
    private String inquiry;

    public LeadEntity toEntity() {
        return LeadEntity.builder()
                .grade(grade)
                .campus(campus)
                .name(name)
                .email(email)
                .phone(phone)
                .postCode(postCode)
                .referralSources(referralSources)
                .otherReferralSource(otherReferralSource)
                .inquiry(inquiry)
                .build();
    }

    public static Lead from(LeadEntity entity) {
        return Lead.builder()
                .id(entity.getId())
                .grade(entity.getGrade())
                .campus(entity.getCampus())
                .name(entity.getName())
                .email(entity.getEmail())
                .phone(entity.getPhone())
                .postCode(entity.getPostCode())
                .referralSources(entity.getReferralSources())
                .otherReferralSource(entity.getOtherReferralSource())
                .inquiry(entity.getInquiry())
                .build();
    }
}
