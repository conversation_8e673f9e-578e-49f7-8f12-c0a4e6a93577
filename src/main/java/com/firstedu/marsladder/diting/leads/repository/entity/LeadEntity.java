package com.firstedu.marsladder.diting.leads.repository.entity;

import com.firstedu.marsladder.diting.leads.service.domain.Campus;
import com.firstedu.marsladder.diting.leads.service.domain.ReferralSource;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

import java.time.LocalDateTime;
import java.util.List;

import static jakarta.persistence.EnumType.STRING;
import static org.hibernate.type.SqlTypes.JSON;

@Entity(name = "lead_detail")
@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LeadEntity {

    @Id
    @UuidGenerator
    private String id;

    private Integer grade;

    @Enumerated(STRING)
    private Campus campus;

    private String name;

    private String email;

    private String phone;

    private String postCode;

    @JdbcTypeCode(JSON)
    private List<ReferralSource> referralSources;

    private String otherReferralSource;

    private String inquiry;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
