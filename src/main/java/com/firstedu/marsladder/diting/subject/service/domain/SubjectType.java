package com.firstedu.marsladder.diting.subject.service.domain;

public enum SubjectType {
    PHYSICS, BIOLOGY, ENGLISH, BUSINESS, MATHS, CHINESE, SCIENCE, CHEMISTRY, OTHER;

    public static SubjectType fromChineseName(String chineseName) {
        return switch (chineseName) {
            case "物理" -> PHYSICS;
            case "生物" -> BIOLOGY;
            case "英语" -> ENGLISH;
            case "商科" -> BUSINESS;
            case "数学" -> MATHS;
            case "中文" -> CHINESE;
            case "科学" -> SCIENCE;
            case "化学" -> CHEMISTRY;
            default -> OTHER;
        };
    }
}
