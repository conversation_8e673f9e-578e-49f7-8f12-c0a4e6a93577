package com.firstedu.marsladder.diting.subject.controller;

import com.firstedu.marsladder.diting.subject.SubjectService;
import com.firstedu.marsladder.diting.subject.controller.dto.SubjectDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class SubjectController {
    private final SubjectService subjectService;

    @GetMapping("/subjects")
    public List<SubjectDto> getSubjects() {
        return subjectService
                .getSubjects()
                .stream()
                .map(SubjectDto::from).toList();
    }

    @GetMapping("/subjects/{subjectId}")
    public SubjectDto getSubjectById(
            @PathVariable String subjectId
    ) {
        return SubjectDto.from(subjectService.getSubjectById(subjectId));
    }
}
