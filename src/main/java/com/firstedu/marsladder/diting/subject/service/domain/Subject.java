package com.firstedu.marsladder.diting.subject.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;

public record Subject(
        String id,
        SubjectType subjectType
) {
    public static Subject from(XiaoguanjiaSubjectEntity xiaoguanjiaSubjectEntity) {
        return new Subject(
                xiaoguanjiaSubjectEntity.getId(),
                SubjectType.fromChineseName(xiaoguanjiaSubjectEntity.getValue())
        );
    }
}
