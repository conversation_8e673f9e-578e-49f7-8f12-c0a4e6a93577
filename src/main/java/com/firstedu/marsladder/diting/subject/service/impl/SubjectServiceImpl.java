package com.firstedu.marsladder.diting.subject.service.impl;

import com.firstedu.marsladder.diting.subject.exception.SubjectNotFoundException;
import com.firstedu.marsladder.diting.subject.service.domain.Subject;
import com.firstedu.marsladder.diting.subject.SubjectService;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaSubjectRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class SubjectServiceImpl implements SubjectService {

    private final XiaoguanjiaSubjectRepository xiaoguanjiaSubjectRepository;

    @Override
    public List<Subject> getSubjects() {
        return xiaoguanjiaSubjectRepository
                .findAll()
                .stream()
                .map(Subject::from)
                .toList();
    }

    @Override
    public Subject getSubjectById(String subjectId) {
        Optional<XiaoguanjiaSubjectEntity> subjectOptional = xiaoguanjiaSubjectRepository.findById(subjectId);
        if (subjectOptional.isEmpty()) {
            throw new SubjectNotFoundException("Subject with id " + subjectId + " not found");
        } else {
            return Subject.from(subjectOptional.get());
        }
    }
}
