package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record CourseInfoWithErrorCodeDto(
        int errcode,
        String errmsg,
        @JsonProperty("course_id")
        String courseId,
        @JsonProperty("shift_id")
        String shiftId,
        @JsonProperty("class_id")
        String classId,
        @JsonProperty("classroom_id")
        String classroomId,
        @JsonProperty("confirm_user_id")
        String confirmUserId,
        @JsonProperty("isfinished")
        int isFinished,
        String createtime,
        String updatetime,
        String starttime,
        String endtime
) {
}
