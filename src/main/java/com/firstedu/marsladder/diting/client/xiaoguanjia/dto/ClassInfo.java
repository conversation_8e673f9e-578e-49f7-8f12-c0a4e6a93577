package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassType;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record ClassInfo(
        @JsonProperty("class_id")
        String classId,
        String name,
        String createtime,
        String updatetime,
        @JsonProperty("grade_id")
        String gradeId,
        @JsonProperty("subject_id")
        String subjectId,
        @JsonProperty("campus_id")
        String campusId,
        @JsonProperty("shift_id")
        String shiftId,
        @JsonProperty("max_student_amount")
        int maxStudentAmount,
        @JsonProperty("opendate")
        String openDate,
        @JsonProperty("isfinished")
        int isFinished,
        @JsonProperty("finished_date")
        String finishedDate,
        @JsonProperty("course_time")
        String courseTime,
        @JsonProperty("headmaster_id")
        String headmasterId,
        @JsonProperty("classroom_id")
        String classroomId,
        int type,
        @JsonProperty("closedate")
        String closeDate,
        @JsonProperty("course_times")
        float courseTimes,
        @JsonProperty("shift_schedule_id")
        String shiftScheduleId,
        @JsonProperty("last_class_time")
        String lastClassTime,
        @JsonProperty("shift_amount")
        int shiftAmount,
        @JsonProperty("valid_attend_minutes")
        int validAttendMinutes,
        String tag,
        @JsonProperty("is_skip_holiday")
        int isSkipHoliday,
        @JsonProperty("is_send_course_msg")
        int isSendCourseMsg,
        int status,
        String describe,
        @JsonProperty("last_course_end_time")
        String lastCourseEndTime,
        @JsonProperty("earliest_course_start_time")
        String earliestCourseStartTime
) {
    public XiaoguanjiaClassEntity toEntity() {
        return new XiaoguanjiaClassEntity(
                classId,
                name,
                gradeId,
                subjectId,
                false,
                campusId,
                shiftId,
                maxStudentAmount,
                fromBJTimeToUTC(openDate),
                isFinished == 1,
                fromBJTimeToUTC(finishedDate),
                courseTime,
                headmasterId,
                classroomId,
                ClassType.of(type),
                fromBJTimeToUTC(closeDate),
                courseTimes,
                shiftScheduleId,
                lastClassTime,
                shiftAmount,
                validAttendMinutes,
                tag,
                isSkipHoliday == 1,
                isSendCourseMsg == 1,
                status,
                describe,
                fromBJTimeToUTC(lastCourseEndTime),
                fromBJTimeToUTC(earliestCourseStartTime),
                fromBJTimeToUTC(createtime),
                fromBJTimeToUTC(updatetime)
        );
    }

    public static ClassInfo from(String classId, ClassInfoWithErrorCodeDto dto) {
        return new ClassInfo(
                classId,
                dto.name(),
                dto.createtime(),
                dto.updatetime(),
                dto.gradeId(),
                dto.subjectId(),
                dto.campusId(),
                dto.shiftId(),
                dto.maxStudentAmount(),
                dto.opendate(),
                dto.isfinished(),
                dto.finishedDate(),
                dto.courseTime(),
                dto.headmasterId(),
                dto.classroomId(),
                dto.type(),
                dto.closedate(),
                dto.courseTimes(),
                dto.shiftScheduleId(),
                dto.lastClassTime(),
                dto.shiftAmount(),
                dto.validAttendMinutes(),
                dto.tag(),
                dto.isSkipHoliday(),
                dto.isSendCourseMsg(),
                dto.status(),
                dto.describe(),
                dto.lastCourseEndTime(),
                dto.earliestCourseStartTime()
        );
    }
}
