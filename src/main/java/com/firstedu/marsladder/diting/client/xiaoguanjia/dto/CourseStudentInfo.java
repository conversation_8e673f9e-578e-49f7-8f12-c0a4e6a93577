package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentEntity;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record CourseStudentInfo(
        @JsonProperty("student_id")
        String studentId,
        @JsonProperty("isattend")
        int isAttend,
        double cost,
        @JsonProperty("student_absentcause_id")
        String absentCauseId,
        @JsonProperty("studetname")
        String studentName,
        @JsonProperty("createtime")
        String createTime
) {
    public XiaoguanjiaCourseStudentEntity toEntity(String courseId) {
        return XiaoguanjiaCourseStudentEntity.builder()
                .courseId(courseId)
                .studentId(studentId)
                .studentName(studentName)
                .isAttend(isAttend)
                .cost(cost)
                .absentCauseId(absentCauseId)
                .createdAt(fromBJTimeToUTC(createTime))
                .build();
    }
}
