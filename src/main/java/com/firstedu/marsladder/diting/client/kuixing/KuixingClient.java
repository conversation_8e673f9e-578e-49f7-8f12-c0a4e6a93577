package com.firstedu.marsladder.diting.client.kuixing;

import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticRequest;
import com.firstedu.marsladder.diting.client.kuixing.dto.AiFeedbackDto;
import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatisticRequest;
import com.firstedu.marsladder.diting.security.TokenHolder;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import static org.springframework.http.MediaType.APPLICATION_JSON;

@Component
@Log4j2
public class KuixingClient {
    private final RestClient restClient;

    private final KuixingClientConfig kuixingClientConfig;

    private final TokenHolder tokenHolder;

    public KuixingClient(RestClient restClient, KuixingClientConfig kuixingClientConfig, TokenHolder tokenHolder) {
        this.restClient = restClient;
        this.kuixingClientConfig = kuixingClientConfig;
        this.tokenHolder = tokenHolder;
    }

    public ResponseEntity<AiFeedbackDto> getAiFeedbackForMath(MathTaskStatisticRequest mathTaskStatisticRequest) {
        var uri = UriComponentsBuilder.fromUriString(kuixingClientConfig.getUrl())
                .pathSegment("comments", "phase-feedback", "math")
                .build()
                .toUri();

        return restClient.post()
                .uri(uri)
                .body(mathTaskStatisticRequest)
                .accept(APPLICATION_JSON)
                .retrieve()
                .toEntity(AiFeedbackDto.class);
    }

    public ResponseEntity<AiFeedbackDto> getAiFeedbackForMultiSubject(MultiSubjectTaskStatisticRequest multiSubjectTaskStatisticRequest) {
        var uri = UriComponentsBuilder.fromUriString(kuixingClientConfig.getUrl())
                .pathSegment("comments", "phase-feedback", "multisubject")
                .build()
                .toUri();
        return restClient.post()
                .uri(uri)
                .body(multiSubjectTaskStatisticRequest)
                .accept(APPLICATION_JSON)
                .retrieve()
                .toEntity(AiFeedbackDto.class);
    }
}
