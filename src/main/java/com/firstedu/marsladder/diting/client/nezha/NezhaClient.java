package com.firstedu.marsladder.diting.client.nezha;

import com.firstedu.marsladder.diting.client.nezha.dto.MultiSubjectTaskStatisticForAiFeedback;
import com.firstedu.marsladder.diting.security.TokenHolder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.springframework.http.MediaType.APPLICATION_JSON;

@Component
public class NezhaClient {

    private final RestClient restClient;

    private final NezhaClientConfig nezhaClientConfig;

    private final TokenHolder tokenHolder;

    public NezhaClient(RestClient restClient, NezhaClientConfig nezhaClientConfig, TokenHolder tokenHolder) {
        this.restClient = restClient;
        this.nezhaClientConfig = nezhaClientConfig;
        this.tokenHolder = tokenHolder;
    }

    public List<MultiSubjectTaskStatisticForAiFeedback> getContentForGenerateTaskFeedback(List<String> classroomIds, LocalDateTime startTime, LocalDateTime endTime) {
        var uri = UriComponentsBuilder.fromUriString(nezhaClientConfig.getUrl())
                .pathSegment("classrooms", "all-students", "task-statistics")
                .queryParam("classroomIds", classroomIds)
                .queryParam("startTime", startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .queryParam("endTime", endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .build()
                .toUri();
        return restClient.get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {});

    }
}
