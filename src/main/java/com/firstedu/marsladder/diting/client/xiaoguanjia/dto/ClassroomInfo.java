package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassroomEntity;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record ClassroomInfo(
        String id,
        @JsonProperty("campus_id")
        String campusId,
        String name,
        @JsonProperty("person_count")
        int personCount,
        int status,
        String createtime,
        String updatetime
) {
    public XiaoguanjiaClassroomEntity toEntity() {
        return new XiaoguanjiaClassroomEntity(
                id,
                campusId,
                name,
                personCount,
                status,
                false,
                fromBJTimeToUTC(createtime),
                fromBJTimeToUTC(updatetime)
        );
    }
}
