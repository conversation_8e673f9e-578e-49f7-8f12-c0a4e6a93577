package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaDepartmentEntity;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record DepartmentInfo(
        @JsonProperty("dept_id")
        String id,
        String name,
        @JsonProperty("iscampus")
        boolean isCampus,
        int status,
        @JsonProperty("brief_name")
        String briefName,
        String createtime,
        String updatetime
) {
    public XiaoguanjiaDepartmentEntity toEntity() {
        return new XiaoguanjiaDepartmentEntity(
                id,
                name,
                isCampus,
                status,
                briefName,
                false,
                fromBJTimeToUTC(createtime),
                fromBJTimeToUTC(updatetime)
        );
    }

    public static DepartmentInfo from(DepartmentInfoWithErrorCodeDto dto, String departmentId) {
        return new DepartmentInfo(
                departmentId,
                dto.name(),
                dto.isCampus(),
                dto.status(),
                dto.briefName(),
                dto.createtime(),
                dto.updatetime()
        );
    }
}
