package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record ClassInfoWithErrorCodeDto(
        int errcode,
        String errmsg,
        String name,
        String createtime,
        String updatetime,
        @JsonProperty("grade_id")
        String gradeId,
        @JsonProperty("subject_id")
        String subjectId,
        @JsonProperty("campus_id")
        String campusId,
        @JsonProperty("shift_id")
        String shiftId,
        @JsonProperty("max_student_amount")
        int maxStudentAmount,
        String opendate,
        int isfinished,
        @JsonProperty("finished_date")
        String finishedDate,
        @JsonProperty("course_time")
        String courseTime,
        @JsonProperty("headmaster_id")
        String headmasterId,
        @JsonProperty("classroom_id")
        String classroomId,
        int type,
        String closedate,
        @JsonProperty("course_times")
        float courseTimes,
        @JsonProperty("shift_schedule_id")
        String shiftScheduleId,
        @JsonProperty("last_class_time")
        String lastClassTime,
        @JsonProperty("shift_amount")
        int shiftAmount,
        @JsonProperty("valid_attend_minutes")
        int validAttendMinutes,
        String tag,
        @JsonProperty("is_skip_holiday")
        int isSkipHoliday,
        @JsonProperty("is_send_course_msg")
        int isSendCourseMsg,
        int status,
        String describe,
        @JsonProperty("last_course_end_time")
        String lastCourseEndTime,
        @JsonProperty("earliest_course_start_time")
        String earliestCourseStartTime
) {
}
