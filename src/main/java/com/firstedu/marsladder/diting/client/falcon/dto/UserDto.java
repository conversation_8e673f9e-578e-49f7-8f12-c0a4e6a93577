package com.firstedu.marsladder.diting.client.falcon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.util.List;

@Builder
public record UserDto(
        String userId,
        String email,
        List<UserRole> roles,
        @JsonProperty("REALUS_STUDENT_PROFILE")
        RealusStudentProfile realusStudentProfile,
        @JsonProperty("REALUS_PARENT_PROFILE")
        RealusParentProfile realusParentProfile
) {
}
