package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record DictInfo(
        String id,
        String createtime,
        String updatetime,
        String value, // subject name
        String code,
        int issys,
        String describe,
        int status
) {
    public XiaoguanjiaSubjectEntity toSubjectEntity() {
        return new XiaoguanjiaSubjectEntity(
                id,
                value,
                DictStatus.of(status),
                fromBJTimeToUTC(createtime),
                fromBJTimeToUTC(updatetime)
        );
    }

    public XiaoguanjiaGradeEntity toGradeEntity() {
        return new XiaoguanjiaGradeEntity(
                id,
                value,
                DictStatus.of(status),
                fromBJTimeToUTC(createtime),
                fromBJTimeToUTC(updatetime)
        );
    }
}
