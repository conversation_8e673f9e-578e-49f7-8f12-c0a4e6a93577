package com.firstedu.marsladder.diting.client.falcon;

import com.firstedu.marsladder.diting.classroom.controller.dto.MarsladderClassroom;
import com.firstedu.marsladder.diting.client.falcon.dto.MathTaskStatisticForAiFeedback;
import com.firstedu.marsladder.diting.client.falcon.dto.TeacherProfileDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserDto;
import com.firstedu.marsladder.diting.client.falcon.dto.UserProfile;
import com.firstedu.marsladder.diting.security.TokenHolder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.springframework.http.MediaType.APPLICATION_JSON;

@Component
public class FalconClient {

    public static final String HEADER_KEY_AMAZON_OIDC_DATA_TOKEN = "amazon-oidc-data-token";

    private final RestClient restClient;

    private final FalconClientConfig falconClientConfig;

    private final TokenHolder tokenHolder;

    public FalconClient(RestClient restClient, FalconClientConfig falconClientConfig, TokenHolder tokenHolder) {
        this.restClient = restClient;
        this.falconClientConfig = falconClientConfig;
        this.tokenHolder = tokenHolder;
    }

    public UserProfile getUserRoles(String accessToken, String idToken) {
        var uri = UriComponentsBuilder.fromUriString(falconClientConfig.getUrl())
                .pathSegment("users", "me", "profile")
                .build()
                .toUri();
        return restClient.get()
                .uri(uri)
                .headers(headers -> {
                    headers.setBearerAuth(accessToken);
                    headers.set(HEADER_KEY_AMAZON_OIDC_DATA_TOKEN, idToken);
                })
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(UserProfile.class);
    }

    public UserDto getUser() {
        var token = tokenHolder.getToken();
        var uri = UriComponentsBuilder.fromUriString(falconClientConfig.getUrl())
                .pathSegment("users", "me")
                .build()
                .toUri();
        return restClient.get()
                .uri(uri)
                .headers(headers -> {
                    headers.setBearerAuth(token.accessToken());
                    headers.set(HEADER_KEY_AMAZON_OIDC_DATA_TOKEN, token.idToken());
                })
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(UserDto.class);
    }

    public TeacherProfileDto getTeacherProfile() {
        var token = tokenHolder.getToken();
        var uri = UriComponentsBuilder.fromUriString(falconClientConfig.getUrl())
                .pathSegment("teacher-profiles", "me")
                .build()
                .toUri();
        return restClient.get()
                .uri(uri)
                .headers(headers -> {
                    headers.setBearerAuth(token.accessToken());
                    headers.set(HEADER_KEY_AMAZON_OIDC_DATA_TOKEN, token.idToken());
                })
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(TeacherProfileDto.class);
    }

    public List<MathTaskStatisticForAiFeedback> getContentForGenerateTaskFeedback(List<String> classroomIds, LocalDateTime startTime, LocalDateTime endTime) {
        var uri = UriComponentsBuilder.fromUriString(falconClientConfig.getUrl())
                .pathSegment("classrooms", "all-students", "task-statistics")
                .queryParam("classroomIds", classroomIds)
                .queryParam("startTime", startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .queryParam("endTime", endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .build()
                .toUri();
        return restClient.get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(new ParameterizedTypeReference<>() {});

    }

    public MarsladderClassroom getClassroom(String classroomId) {
        var uri = UriComponentsBuilder.fromUriString(falconClientConfig.getUrl())
                .pathSegment("internal", "classrooms", classroomId)
                .build()
                .toUri();
        return restClient.get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(MarsladderClassroom.class);
    }
}
