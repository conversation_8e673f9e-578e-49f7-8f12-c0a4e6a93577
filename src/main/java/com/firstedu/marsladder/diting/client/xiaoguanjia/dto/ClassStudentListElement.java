package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaClassStudent;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;
import lombok.Builder;

@Builder
public record ClassStudentListElement(
        String id,
        @JsonProperty("student_id")
        String studentId,
        Integer status,
        @JsonProperty("indate")
        String inDate,
        @JsonProperty("outdate")
        String outDate,
        @JsonProperty("out_cause_id")
        String outCauseId,
        @JsonProperty("out_memo")
        String outMemo,
        @JsonProperty("out_type")
        String outType,
        @JsonProperty("createtime")
        String createTime,
        @JsonProperty("updatetime")
        String updateTime
) {
    public XiaoguanjiaClassStudent toXiaoguanjiaClassStudent(String classId) {
        return new XiaoguanjiaClassStudent(
                id,
                classId,
                studentId,
                ClassStudentStatus.fromCode(status),
                fromBJTimeToUTC(inDate),
                fromBJTimeToUTC(outDate),
                outCauseId,
                outMemo,
                outType,
                fromBJTimeToUTC(createTime),
                fromBJTimeToUTC(updateTime)
        );
    }
}
