package com.firstedu.marsladder.diting.client.xiaoguanjia;

public enum XiaogjResponseStatus {
    SYSTEM_BUSY(-1, "System is busy, please try again later"),
    SUCCESS(0, "Request successful"),
    BANNED(301, "Banned"),
    IP_RESTRICTED(315, "IP restricted"),
    FORBIDDEN(403, "Forbidden access"),
    NOT_FOUND(404, "Object not found"),
    TOKEN_EXPIRED(405, "Token expired"),
    TIMEOUT(408, "Request timeout"),
    PARAM_ERROR(414, "Parameter error"),
    INTERNAL_ERROR(500, "Internal server error");

    private final int code;
    private final String message;

    XiaogjResponseStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static XiaogjResponseStatus fromCode(int code) {
        for (XiaogjResponseStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}