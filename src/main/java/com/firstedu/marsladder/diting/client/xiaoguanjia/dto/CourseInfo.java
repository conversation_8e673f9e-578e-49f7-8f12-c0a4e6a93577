package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseEntity;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromMelTimeToUTC;

public record CourseInfo(
        @JsonProperty("course_id")
        String courseId,
        @JsonProperty("shift_id")
        String shiftId,
        @JsonProperty("class_id")
        String classId,
        @JsonProperty("classroom_id")
        String classroomId,
        @JsonProperty("isfinished")
        int isFinished,
        String createtime,
        String updatetime,
        String starttime,
        String endtime
) {
    public XiaoguanjiaCourseEntity toEntity() {
        return new XiaoguanjiaCourseEntity(
                courseId,
                classId,
                shiftId,
                classroomId,
                isFinished,
                false,
                fromMelTimeToUTC(starttime),
                fromMelTimeToUTC(endtime),
                fromMelTimeToUTC(createtime),
                fromMelTimeToUTC(updatetime)
        );
    }

    public static CourseInfo from(String courseId, CourseInfoWithErrorCodeDto dto) {
        return new CourseInfo(
                courseId,
                dto.shiftId(),
                dto.classId(),
                dto.classroomId(),
                dto.isFinished(),
                dto.createtime(),
                dto.updatetime(),
                dto.starttime(),
                dto.endtime()
        );
    }
}
