package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record StudentInfo(
        @JsonProperty("student_id")
        String studentId,
        Integer status,
        @JsonProperty("campus_id")
        String campusId,
        String name,
        @JsonProperty("english_name")
        String englishName,
        Integer sex,
        @JsonProperty("smstel")
        String smsTel,
        String address,
        @JsonProperty("username")
        String userName,
        @JsonProperty("headimg")
        String headImg,
        String serial,
        @JsonProperty("idnumber")
        String idNumber,
        String birthday,
        @JsonProperty("birthday_moon")
        String birthdayMoon,
        @JsonProperty("fulltime_school")
        String fullTimeSchool,
        @JsonProperty("fulltime_class")
        String fullTimeClass,
        @JsonProperty("grade_code")
        String gradeCode,
        @JsonProperty("grade_base_date")
        String gradeBaseDate,
        @JsonProperty("master_id")
        String masterId,
        @JsonProperty("master_adjust_date")
        String masterAdjustDate,
        @JsonProperty("type_id")
        String typeId,
        @JsonProperty("importtime")
        String importTime,
        @JsonProperty("feeupdatetime")
        String feeUpdateTime,
        @JsonProperty("customer_campus_id")
        String customerCampusId,
        @JsonProperty("introducer_id")
        String introducerId,
        @JsonProperty("returndate")
        String returnDate,
        @JsonProperty("is_out_trans_customer")
        Integer isOutTransCustomer,
        @JsonProperty("trystatus")
        Integer tryStatus,
        @JsonProperty("trystatus_date")
        String tryStatusDate,
        @JsonProperty("indate")
        String inDate,
        @JsonProperty("outdate")
        String outDate,
        @JsonProperty("quitschooldate")
        String quitSchoolDate,
        @JsonProperty("is_exception_out")
        Integer isExceptionOut,
        @JsonProperty("outcause")
        String outCause,
        @JsonProperty("outcause_id")
        String outCauseId,
        String describe,
        @JsonProperty("can_login_ssx")
        Integer canLoginSsx,
        @JsonProperty("sale_person_id")
        String salePersonId,
        String point,
        @JsonProperty("createtime")
        String createdAt,
        @JsonProperty("updatetime")
        String updatedAt
) {
    public XiaoguanjiaStudentEntity toEntity() {
        return new XiaoguanjiaStudentEntity(
                studentId,
                XiaoguanjiaStudentStatus.getByCode(status),
                campusId,
                name,
                englishName,
                sex,
                smsTel,
                address,
                userName,
                headImg,
                serial,
                idNumber,
                fromBJTimeToUTC(birthday),
                fromBJTimeToUTC(birthdayMoon),
                fullTimeSchool,
                fullTimeClass,
                gradeCode,
                fromBJTimeToUTC(gradeBaseDate),
                masterId,
                fromBJTimeToUTC(masterAdjustDate),
                typeId,
                fromBJTimeToUTC(importTime),
                fromBJTimeToUTC(feeUpdateTime),
                customerCampusId,
                introducerId,
                fromBJTimeToUTC(returnDate),
                isOutTransCustomer,
                tryStatus,
                fromBJTimeToUTC(tryStatusDate),
                fromBJTimeToUTC(inDate),
                fromBJTimeToUTC(outDate),
                fromBJTimeToUTC(quitSchoolDate),
                isExceptionOut,
                outCause,
                outCauseId,
                describe,
                canLoginSsx,
                salePersonId,
                point,
                fromBJTimeToUTC(createdAt),
                fromBJTimeToUTC(updatedAt)
        );
    }

    public static StudentInfo from(StudentInfoWithErrorCodeDto dto) {
        return new StudentInfo(
                dto.studentId(),
                dto.status(),
                dto.campusId(),
                dto.name(),
                dto.englishName(),
                dto.sex(),
                dto.smsTel(),
                dto.address(),
                dto.userName(),
                dto.headImg(),
                dto.serial(),
                dto.idNumber(),
                dto.birthday(),
                dto.birthdayMoon(),
                dto.fullTimeSchool(),
                dto.fullTimeClass(),
                dto.gradeCode(),
                dto.gradeBaseDate(),
                dto.masterId(),
                dto.masterAdjustDate(),
                dto.typeId(),
                dto.importTime(),
                dto.feeUpdateTime(),
                dto.customerCampusId(),
                dto.introducerId(),
                dto.returnDate(),
                dto.isOutTransCustomer(),
                dto.tryStatus(),
                dto.tryStatusDate(),
                dto.inDate(),
                dto.outDate(),
                dto.quitSchoolDate(),
                dto.isExceptionOut(),
                dto.outCause(),
                dto.outCauseId(),
                dto.describe(),
                dto.canLoginSsx(),
                dto.salePersonId(),
                dto.point(),
                dto.createdAt(),
                dto.updatedAt()
        );
    }
}
