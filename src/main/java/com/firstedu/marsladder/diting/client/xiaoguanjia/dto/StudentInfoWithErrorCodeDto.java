package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record StudentInfoWithErrorCodeDto(
        int errcode,
        String errmsg,
        String id,
        @JsonProperty("student_id")
        String studentId,
        Integer status,
        @JsonProperty("campus_id")
        String campusId,
        String name,
        @JsonProperty("english_name")
        String englishName,
        Integer sex,
        @JsonProperty("smstel")
        String smsTel,
        String address,
        @JsonProperty("username")
        String userName,
        @JsonProperty("headimg")
        String headImg,
        String serial,
        @JsonProperty("idnumber")
        String idNumber,
        String birthday,
        @JsonProperty("birthday_moon")
        String birthdayMoon,
        @JsonProperty("fulltime_school")
        String fullTimeSchool,
        @JsonProperty("fulltime_class")
        String fullTimeClass,
        @JsonProperty("grade_code")
        String gradeCode,
        @JsonProperty("grade_base_date")
        String gradeBaseDate,
        @JsonProperty("master_id")
        String masterId,
        @JsonProperty("master_adjust_date")
        String masterAdjustDate,
        @JsonProperty("type_id")
        String typeId,
        @JsonProperty("importtime")
        String importTime,
        @JsonProperty("feeupdatetime")
        String feeUpdateTime,
        @JsonProperty("customer_campus_id")
        String customerCampusId,
        @JsonProperty("introducer_id")
        String introducerId,
        @JsonProperty("returndate")
        String returnDate,
        @JsonProperty("is_out_trans_customer")
        Integer isOutTransCustomer,
        @JsonProperty("trystatus")
        Integer tryStatus,
        @JsonProperty("trystatus_date")
        String tryStatusDate,
        @JsonProperty("indate")
        String inDate,
        @JsonProperty("outdate")
        String outDate,
        @JsonProperty("quitschooldate")
        String quitSchoolDate,
        @JsonProperty("is_exception_out")
        Integer isExceptionOut,
        @JsonProperty("outcause")
        String outCause,
        @JsonProperty("outcause_id")
        String outCauseId,
        String describe,
        @JsonProperty("can_login_ssx")
        Integer canLoginSsx,
        @JsonProperty("sale_person_id")
        String salePersonId,
        String point,
        @JsonProperty("createtime")
        String createdAt,
        @JsonProperty("updatetime")
        String updatedAt
) {
}
