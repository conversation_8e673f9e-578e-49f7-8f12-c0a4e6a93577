package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseTeacherEntity;

public record CourseTeacherInfo(
        @JsonProperty("employee_id")
        String employeeId,
        int role
) {
    public XiaoguanjiaCourseTeacherEntity toEntity(String courseId) {
        return XiaoguanjiaCourseTeacherEntity.builder()
                .courseId(courseId)
                .employeeId(employeeId)
                .role(role)
                .build();
    }
}
