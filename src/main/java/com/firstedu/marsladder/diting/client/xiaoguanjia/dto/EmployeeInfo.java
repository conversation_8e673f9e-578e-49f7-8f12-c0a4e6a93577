package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaEmployeeEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEmployeeStatus;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record EmployeeInfo(
        @JsonProperty("employee_id")
        String employeeId,
        String name,
        @JsonProperty("nickname")
        String nickName,
        Integer status,
        @JsonProperty("createtime")
        String createdAt,
        @JsonProperty("updatetime")
        String updatedAt
) {
    public XiaoguanjiaEmployeeEntity toEntity() {
        return new XiaoguanjiaEmployeeEntity(
                employeeId,
                name,
                nickName,
                XiaoguanjiaEmployeeStatus.fromCode(status),
                fromBJTimeToUTC(createdAt),
                fromBJTimeToUTC(updatedAt)
        );
    }

    public XiaoguanjiaEmployeeEntity toEntity(String employeeId) {
        return new XiaoguanjiaEmployeeEntity(
                employeeId,
                name,
                nickName,
                XiaoguanjiaEmployeeStatus.fromCode(status),
                fromBJTimeToUTC(createdAt),
                fromBJTimeToUTC(updatedAt)
        );
    }

    public static EmployeeInfo from(EmployeeInfoWithErrorCodeDto dto) {
        return new EmployeeInfo(
                dto.employeeId(),
                dto.name(),
                dto.nickName(),
                dto.status(),
                dto.createdAt(),
                dto.updatedAt()
        );
    }
}
