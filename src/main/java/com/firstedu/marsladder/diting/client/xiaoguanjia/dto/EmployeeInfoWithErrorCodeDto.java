package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record EmployeeInfoWithErrorCodeDto(
        int errcode,
        String errmsg,
        @JsonProperty("employee_id")
        String employeeId,
        String name,
        @JsonProperty("nickname")
        String nickName,
        Integer status,
        @JsonProperty("createtime")
        String createdAt,
        @JsonProperty("updatetime")
        String updatedAt
) {
}
