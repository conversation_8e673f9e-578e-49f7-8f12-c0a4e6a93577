package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record ShiftInfoWithErrorCodeDto(
        int errcode,
        String errmsg,
        String createtime,
        String updatetime,
        String name,
        @JsonProperty("subject_id")
        String subjectId,
        int year,
        int unit,
        int status
) {
    public ShiftInfo toShiftInfo(String shiftId) {
        return new ShiftInfo(
                shiftId,
                createtime,
                updatetime,
                name,
                subjectId,
                year,
                unit,
                status
        );
    }
}