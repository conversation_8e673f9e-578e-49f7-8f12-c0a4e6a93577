package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaShiftEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record ShiftInfo(
        @JsonProperty("shift_id")
        String shiftId,
        String createtime,
        String updatetime,
        String name,
        @JsonProperty("subject_id")
        String subjectId,
        int year,
        int unit,
        int status
) {
    public XiaoguanjiaShiftEntity toEntity() {
        return new XiaoguanjiaShiftEntity(
                shiftId,
                subjectId,
                name,
                year,
                unit,
                ShiftStatus.of(status),
                fromBJTimeToUTC(createtime),
                fromBJTimeToUTC(updatetime)
        );
    }
}