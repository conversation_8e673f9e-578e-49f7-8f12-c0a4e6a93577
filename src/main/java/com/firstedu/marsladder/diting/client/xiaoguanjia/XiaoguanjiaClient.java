package com.firstedu.marsladder.diting.client.xiaoguanjia;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassInfoWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassStudentListElement;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassStudentsResponse;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassTeachersResponse;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ClassTeachersResponse.ClassTeacherListElement;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseInfoWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseStudentInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseStudentsWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseTeacherInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.CourseTeachersWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DepartmentInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DepartmentInfoWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DictInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DictInfosResponse;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.EmployeeInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.EmployeeInfoWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfClasses;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfClassrooms;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfCourses;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfDepartments;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfEmployees;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfShifts;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.PageOfStudents;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ShiftInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.ShiftInfoWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentCourseIdInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentCourseIdListWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentCourseInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentCoursesWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentInfo;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.StudentInfoWithErrorCodeDto;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.XiaogjAccessTokenResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisException;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestClient.ResponseSpec.ErrorHandler;

import java.net.URI;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.firstedu.marsladder.diting.client.xiaoguanjia.XiaogjResponseStatus.fromCode;
import static java.time.format.DateTimeFormatter.ofPattern;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.web.util.UriComponentsBuilder.fromUriString;

@Component
@RequiredArgsConstructor
@Log4j2
public class XiaoguanjiaClient {
    private static final String XIAOGJ_ACCESS_TOKEN_REDIS_KEY = "XIAOGJ_ACCESS_TOKEN";
    private static final String XIAOGJ_ACCESS_TOKEN_REDIS_KEY_LOCK = "XIAOGJ_ACCESS_TOKEN_LOCK";

    private final RestClient restClient;
    private final XiaoguanjiaClientConfig xiaoguanjiaClientConfig;
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;

    @Value("${client.xiaoguanjia.appid}")
    private String xiaogjAppId;

    @Value("${client.xiaoguanjia.secret}")
    private String xiaogjSecret;

    public PageOfClasses getPageOfClasses(LocalDate createdAfter, int pageSize, int pageNumber) {
        var accessToken = getAccessToken();
        var createTime = createdAfter.format(ofPattern("yyyyMMdd"));
        var offset = pageNumber;
        var size = pageSize;

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_class_batch")
                .queryParam("access_token", accessToken)
                .queryParam("createTime", createTime)
                .queryParam("offset", offset)
                .queryParam("size", size)
                .build()
                .toUri();

        var pageOfClasses = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(PageOfClasses.class);

        handleDtoErrorCode(pageOfClasses == null ? -1 : pageOfClasses.errcode());
        return pageOfClasses;
    }

    public PageOfShifts getPageOfShifts(LocalDate updatedAfter, int pageSize, int pageNumber) {
        var accessToken = getAccessToken();
        var updatetime = updatedAfter.format(ofPattern("yyyyMMdd"));
        var offset = pageNumber;
        var size = pageSize;

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_shift_batch")
                .queryParam("access_token", accessToken)
                .queryParam("updatetime", updatetime)
                .queryParam("offset", offset)
                .queryParam("size", size)
                .build()
                .toUri();

        var pageOfShifts = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(PageOfShifts.class);

        handleDtoErrorCode(pageOfShifts == null ? -1 : pageOfShifts.errcode());
        return pageOfShifts;
    }

    public PageOfCourses getPageOfCourses(LocalDate startTimeAfter, int pageSize, int pageNumber) {
        var accessToken = getAccessToken();
        var updateTime = startTimeAfter.format(ofPattern("yyyyMMdd"));
        var offset = pageNumber;
        var size = pageSize;

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_course_batch")
                .queryParam("access_token", accessToken)
                .queryParam("updatetime", updateTime)
                .queryParam("timeflag", 2)
                .queryParam("offset", offset)
                .queryParam("size", size)
                .build()
                .toUri();

        var pageOfCourses = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(PageOfCourses.class);

        handleDtoErrorCode(pageOfCourses == null ? -1 : pageOfCourses.errcode());
        return pageOfCourses;
    }

    public PageOfDepartments getPageOfDepartments(LocalDate updateTimeAfter, int pageSize, int pageNumber) {
        var accessToken = getAccessToken();
        var updateTime = updateTimeAfter.format(ofPattern("yyyyMMdd"));
        var offset = pageNumber;
        var size = pageSize;

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("base/get_dept_batch")
                .queryParam("access_token", accessToken)
                .queryParam("updatetime", updateTime)
                .queryParam("offset", offset)
                .queryParam("size", size)
                .build()
                .toUri();

        var pageOfDepartments = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(PageOfDepartments.class);

        handleDtoErrorCode(pageOfDepartments == null ? -1 : pageOfDepartments.errcode());
        return pageOfDepartments;
    }

    public PageOfClassrooms getPageOfClassrooms(LocalDate updateTimeAfter, int pageSize, int pageNumber) {
        var accessToken = getAccessToken();
        var updateTime = updateTimeAfter.format(ofPattern("yyyyMMdd"));
        var offset = pageNumber;
        var size = pageSize;

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_classroom_batch")
                .queryParam("access_token", accessToken)
                .queryParam("updatetime", updateTime)
                .queryParam("offset", offset)
                .queryParam("size", size)
                .build()
                .toUri();

        var pageOfClassrooms = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(PageOfClassrooms.class);

        handleDtoErrorCode(pageOfClassrooms == null ? -1 : pageOfClassrooms.errcode());
        return pageOfClassrooms;
    }

    public List<DictInfo> fetchDict(String type) {
        var accessToken = getAccessToken();
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("base/get_dict")
                .queryParam("access_token", accessToken)
                .queryParam("type", type)
                .build()
                .toUri();

        var subjects = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(DictInfosResponse.class);

        handleDtoErrorCode(subjects == null ? -1 : subjects.errcode());
        return subjects.data();
    }

    public String getAccessToken() {
        String accessToken = getStringData(XIAOGJ_ACCESS_TOKEN_REDIS_KEY);
        if (accessToken != null) {
            return accessToken;
        }

        RLock lock = redissonClient.getLock(XIAOGJ_ACCESS_TOKEN_REDIS_KEY_LOCK);
        boolean lockAcquired = false;
        try {
            if (lock.tryLock(3, 3, TimeUnit.SECONDS)) {
                lockAcquired = true;
                try {
                    XiaogjAccessTokenResponse latestAccessToken = getLatestAccessToken();
                    setStringData(XIAOGJ_ACCESS_TOKEN_REDIS_KEY, latestAccessToken.accessToken(), latestAccessToken.expiresIn() - 3600);
                    log.info("Set xiaoguanjia access token to Redis, accessToken:{}", latestAccessToken.accessToken());
                    return latestAccessToken.accessToken();
                } catch (Exception e) {
                    throw new RuntimeException("Error while fetching or storing token.", e);
                }
            } else {
                Thread.sleep(2 * 1000);
                return getAccessToken();
            }
        } catch (Exception e) {
            throw new RuntimeException("Error while acquiring Redis lock.", e);
        } finally {
            if (lockAcquired) {
                lock.unlock();
            }
        }
    }

    private String getStringData(String key) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
            return bucket.get();
        } catch (RedisException e) {
            throw new RuntimeException("get token value exception", e);
        }
    }

    public void setStringData(String key, String value, Integer timeToLive) {
        RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
        bucket.set(value, timeToLive, TimeUnit.SECONDS);
    }

    private XiaogjAccessTokenResponse getLatestAccessToken() {
        URI uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("token")
                .queryParam("appid", xiaogjAppId)
                .queryParam("secret", xiaogjSecret)
                .build()
                .toUri();

        var response = restClient
                .get()
                .uri(uri)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(XiaogjAccessTokenResponse.class);

        handleDtoErrorCode(response == null ? -1 : response.errcode());
        return response;
    }

    public ClassInfo fetchClass(String classId) {
        var accessToken = getAccessToken();

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_class")
                .queryParam("access_token", accessToken)
                .queryParam("class_id", classId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(ClassInfoWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return ClassInfo.from(classId, dto);
    }

    public CourseInfo fetchCourse(String courseId) {
        var accessToken = getAccessToken();

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_course")
                .queryParam("access_token", accessToken)
                .queryParam("course_id", courseId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(CourseInfoWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return CourseInfo.from(courseId, dto);
    }

    public PageOfStudents getPageOfStudents(LocalDate startTimeAfter, int pageSize, int pageNumber) {
        var updateTime = startTimeAfter.format(ofPattern("yyyyMMdd"));
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("user/get_student_batch")
                .queryParam("access_token", getAccessToken())
                .queryParam("updatetime", updateTime)
                .queryParam("offset", pageNumber)
                .queryParam("size", pageSize)
                .build()
                .toUri();

        var pageOfStudents = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(PageOfStudents.class);

        handleDtoErrorCode(pageOfStudents == null ? -1 : pageOfStudents.errcode());
        return pageOfStudents;
    }

    public StudentInfo fetchStudent(String studentId) {
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("user/get_student")
                .queryParam("access_token", getAccessToken())
                .queryParam("student_id", studentId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(StudentInfoWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return StudentInfo.from(dto);
    }

    public EmployeeInfo fetchEmployee(String employeeId) {
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("org/get_employee")
                .queryParam("access_token",  getAccessToken())
                .queryParam("employee_id", employeeId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(EmployeeInfoWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());
        return EmployeeInfo.from(dto);
    }

    public List<CourseTeacherInfo> fetchCourseTeachers(String courseId) {
        var accessToken = getAccessToken();

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_course_teacher_list")
                .queryParam("access_token", accessToken)
                .queryParam("course_id", courseId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(CourseTeachersWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return dto.data();
    }

    public List<CourseStudentInfo> fetchCourseStudents(String courseId) {
        var accessToken = getAccessToken();

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_course_student_list")
                .queryParam("access_token", accessToken)
                .queryParam("course_id", courseId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(CourseStudentsWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return dto.data();
    }

    public List<StudentCourseInfo> fetchStudentCourses(String studentId) {
        var accessToken = getAccessToken();

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_courselist_by_student")
                .queryParam("access_token", accessToken)
                .queryParam("student_id", studentId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(StudentCoursesWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return dto.data();
    }

    public List<StudentCourseIdInfo> fetchStudentCourseIds(String studentId) {
        var accessToken = getAccessToken();

        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_courseidlist_by_student")
                .queryParam("access_token", accessToken)
                .queryParam("student_id", studentId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(StudentCourseIdListWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());

        return dto.data();
    }

    public PageOfEmployees getPageOfEmployees(LocalDate startTimeAfter, int pageSize, int pageNumber) {
        var updateTime = startTimeAfter.format(ofPattern("yyyyMMdd"));
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("org/get_employee_batch")
                .queryParam("access_token", getAccessToken())
                .queryParam("updatetime", updateTime)
                .queryParam("offset", pageNumber)
                .queryParam("size", pageSize)
                .build()
                .toUri();

        var pageOfEmployees = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(PageOfEmployees.class);

        handleDtoErrorCode(pageOfEmployees == null ? -1 : pageOfEmployees.errcode());
        return pageOfEmployees;
    }

    public List<ClassTeacherListElement> getClassTeachers(String classId) {
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_class_teacher_list")
                .queryParam("access_token", getAccessToken())
                .queryParam("class_id", classId)
                .build()
                .toUri();
        var classTeachersResponse = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .onStatus(HttpStatusCode::isError, handleHttpErrorStatus())
                .body(ClassTeachersResponse.class);
        handleDtoErrorCode(classTeachersResponse == null ? -1 : classTeachersResponse.errcode());

        return classTeachersResponse.data();
    }

    public List<ClassStudentListElement> getClassStudents(String classId) {
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_class_student_list")
                .queryParam("access_token", getAccessToken())
                .queryParam("class_id", classId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(ClassStudentsResponse.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errCode());

        return dto.data();
    }

    public ShiftInfo fetchShift(String shiftId) {
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("edu/get_shift")
                .queryParam("access_token",  getAccessToken())
                .queryParam("shift_id", shiftId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(ShiftInfoWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());
        return dto.toShiftInfo(shiftId);
    }

    public DepartmentInfo fetchDepartment(String departmentId) {
        var uri = fromUriString(xiaoguanjiaClientConfig.getUrl())
                .pathSegment("org/get_dept")
                .queryParam("access_token",  getAccessToken())
                .queryParam("dept_id", departmentId)
                .build()
                .toUri();

        var dto = restClient
                .get()
                .uri(uri)
                .accept(APPLICATION_JSON)
                .retrieve()
                .body(DepartmentInfoWithErrorCodeDto.class);

        handleDtoErrorCode(dto == null ? -1 : dto.errcode());
        return DepartmentInfo.from(dto, departmentId);
    }

    private static ErrorHandler handleHttpErrorStatus() {
        return (request, response) -> {
            var code = response.getStatusCode().value();
            var message = new String(response.getBody().readAllBytes());
            log.error("Http request to xiaoguanjia failed with status code {}, message: {}", code, message);
            throw new XiaogjApiRequestException("Http request to xiaoguanjia failed");
        };
    }

    private void handleDtoErrorCode(int errCode) {
        if (errCode == 0) {
            return;
        } else {
            var errorMessage = fromCode(errCode).getMessage();
            log.error("Request to Xiaogj API failed with error code {}, error message: {}", errCode, errorMessage);
            throw new XiaogjApiRequestException(errorMessage);
        }
    }

}
