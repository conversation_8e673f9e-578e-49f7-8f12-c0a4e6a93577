package com.firstedu.marsladder.diting.client.xiaoguanjia.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.TeacherRole;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassTeacherEntity;

import java.util.List;

import static com.firstedu.marsladder.diting.xiaoguanjia.utils.Utils.fromBJTimeToUTC;

public record ClassTeachersResponse(
        int errcode,
        String errmsg,
        List<ClassTeacherListElement> data
) {
    public record ClassTeacherListElement(
            String createtime,
            @JsonProperty("employee_id")
            String employeeId,
            int role,
            @JsonProperty("subject_id")
            String subjectId,
            List<String> types
    ) {
        public XiaoguanjiaClassTeacherEntity toEntity(String classId) {
            return new XiaoguanjiaClassTeacherEntity(
                    null,
                    classId,
                    employeeId,
                    subjectId,
                    TeacherRole.of(role),
                    types,
                    fromBJTimeToUTC(createtime),
                    null
            );
        }
    }
}
