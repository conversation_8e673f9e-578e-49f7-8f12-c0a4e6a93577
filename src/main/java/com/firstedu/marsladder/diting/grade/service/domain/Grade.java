package com.firstedu.marsladder.diting.grade.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;

import java.util.List;
import java.util.stream.Collectors;

public record Grade(
        String id,
        String value
) {
    public static Grade from(XiaoguanjiaGradeEntity xiaoguanjiaGradeEntity) {
        return new Grade(xiaoguanjiaGradeEntity.getId(), xiaoguanjiaGradeEntity.getValue());
    }

    public static List<Grade> listFrom(List<XiaoguanjiaGradeEntity> allGrades) {
        return allGrades.stream().map(Grade::from).collect(Collectors.toList());
    }
}
