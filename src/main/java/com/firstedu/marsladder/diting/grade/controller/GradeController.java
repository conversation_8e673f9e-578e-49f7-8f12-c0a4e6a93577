package com.firstedu.marsladder.diting.grade.controller;

import com.firstedu.marsladder.diting.grade.controller.dto.GradeDto;
import com.firstedu.marsladder.diting.grade.service.GradeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class GradeController {

    private final GradeService gradeService;

    @GetMapping("/grades")
    @ResponseStatus(OK)
    public List<GradeDto> getGrades() {
        return gradeService
                .getGrades()
                .stream()
                .map(GradeDto::from).toList();
    }

    @GetMapping("/grades/{gradeId}")
    @ResponseStatus(OK)
    public GradeDto getGradeById(
            @PathVariable String gradeId
    ) {
        return GradeDto.from(gradeService.getGradeById(gradeId));
    }

}
