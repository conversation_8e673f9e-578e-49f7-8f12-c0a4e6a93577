package com.firstedu.marsladder.diting.grade.service.impl;

import com.firstedu.marsladder.diting.grade.exception.XiaoguanjiaGradeNotFoundException;
import com.firstedu.marsladder.diting.grade.service.GradeService;
import com.firstedu.marsladder.diting.grade.service.domain.Grade;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaGradeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GradeServiceImpl implements GradeService {
    private final XiaoguanjiaGradeRepository xiaoguanjiaGradeRepository;

    @Override
    public List<Grade> getGrades() {
        return Grade.listFrom(xiaoguanjiaGradeRepository.findAll());
    }

    @Override
    public Grade getGradeById(String gradeId) {
        var gradeOptional = xiaoguanjiaGradeRepository.findById(gradeId);
        if (gradeOptional.isEmpty()) {
            throw new XiaoguanjiaGradeNotFoundException("No such xiaoguanjia grade: " + gradeId);
        }
        return Grade.from(gradeOptional.get());
    }
}
