package com.firstedu.marsladder.diting.amazonoidc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.JWSObject;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.ParseException;

@Component
public class AmazonOidcDataTokenParserImpl implements AmazonOidcDataTokenParser {
    private final ObjectMapper objectMapper;

    public AmazonOidcDataTokenParserImpl(
            ObjectMapper objectMapper
    ) {
        this.objectMapper = objectMapper;
    }

    @Override
    public AmazonOidcData parse(String token) {
        try {
            var jwtObject = JWSObject.parse(token);
            return objectMapper.readValue(jwtObject.getPayload().toBytes(), AmazonOidcData.class);
        } catch (ParseException | IOException e) {
            throw new RuntimeException(e);
        }
    }
}
