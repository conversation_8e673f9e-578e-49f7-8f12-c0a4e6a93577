package com.firstedu.marsladder.diting.campus.repository;

import com.firstedu.marsladder.diting.campus.repository.entity.SchoolCampusBindingsEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchoolCampusBindingsRepository extends JpaRepository<SchoolCampusBindingsEntity, String> {
    List<SchoolCampusBindingsEntity> findByschoolCampusIdIn(List<String> campusIds);
}
