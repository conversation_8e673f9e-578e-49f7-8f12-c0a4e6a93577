package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.employee.exception.XiaoguanjiaEmployeeNotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.EmployeeActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEmployeeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaEmployeeService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncEmployeesCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEmployeeStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaEmployeeServiceImpl implements XiaoguanjiaEmployeeService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaEmployeeRepository xiaoguanjiaEmployeeRepository;

    @Override
    @Transactional
    public void syncEmployee(SyncEmployeesCommand syncEmployeesCommand) {
        var pageNumber = 1;
        var hasMore = 1;
        log.info("Start syncing classes");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfEmployees = xiaoguanjiaClient.getPageOfEmployees(
                    syncEmployeesCommand.createdAfter(),
                    syncEmployeesCommand.pageSize(),
                    pageNumber
            );

            for (var emplyeeInfo : pageOfEmployees.data()) {
                xiaoguanjiaEmployeeRepository.save(emplyeeInfo.toEntity());
            }
            pageNumber += 1;
            hasMore = pageOfEmployees.hasMore();
        }
        log.info("Finished syncing employees");
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void handleEmployeeStudentEvent(EmployeeActionEvent actionEvent) {
        var employeeId = actionEvent.getEmployeeId();
        var actionType = actionEvent.getActionType();
        log.info("handling employee action event, employee id: {}, action is: {}", employeeId, actionType);

        switch (actionType) {
            case CREATE -> {
                if (xiaoguanjiaEmployeeRepository.existsById(employeeId)) {
                    log.info("current employee existed when receive employee create event, employee id: {}", employeeId);
                } else {
                    fetchAndSaveLatestEmployee(employeeId);
                }
            }
            case MODIFY -> {
                xiaoguanjiaEmployeeRepository.findById(employeeId)
                        .orElseThrow(() -> new XiaoguanjiaEmployeeNotFoundException("Failed to modify xiaoguanjia employee " + employeeId + ", no such employee found"));

                fetchAndSaveLatestEmployee(employeeId);
            }
            case REMOVE -> {
                var existedEntity = xiaoguanjiaEmployeeRepository.findById(employeeId)
                        .orElseThrow(() -> new XiaoguanjiaEmployeeNotFoundException("Failed to remove xiaoguanjia employee " + employeeId + ", no such employee found"));

                existedEntity.setStatus(XiaoguanjiaEmployeeStatus.DELETED);
                xiaoguanjiaEmployeeRepository.save(existedEntity);
            }
        }
    }

    private void fetchAndSaveLatestEmployee(String employeeId) {
        var employeeInfo = xiaoguanjiaClient.fetchEmployee(employeeId);
        xiaoguanjiaEmployeeRepository.save(employeeInfo.toEntity(employeeId));
    }
}
