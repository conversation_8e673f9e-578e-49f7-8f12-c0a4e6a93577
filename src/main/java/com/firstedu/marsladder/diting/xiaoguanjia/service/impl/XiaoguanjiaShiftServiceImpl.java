package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.shift.service.exception.XiaoguanjiaShiftNotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ShiftActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaShiftRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaShiftService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncShiftsCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@RequiredArgsConstructor
@Service
public class XiaoguanjiaShiftServiceImpl implements XiaoguanjiaShiftService {

    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaShiftRepository xiaoguanjiaShiftRepository;

    @Override
    @Transactional
    public void syncShifts(SyncShiftsCommand syncShiftsCommand) {
        var pageNumber = 1; // page number from xiaoguanjia starts from 1 instead of 0
        var hasMore = 1;
        log.info("Start syncing shifts");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfShifts = xiaoguanjiaClient.getPageOfShifts(
                    syncShiftsCommand.updatedAfter(),
                    syncShiftsCommand.pageSize(),
                    pageNumber
            );

            for (var shiftInfo : pageOfShifts.data()) {
                xiaoguanjiaShiftRepository.save(shiftInfo.toEntity());
            }
            pageNumber += 1;
            hasMore = pageOfShifts.has_more();
        }
        log.info("Finished syncing shifts");
    }

    @Override
    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void handleShiftEvent(ShiftActionEvent shiftActionEvent) {
        var shiftId = shiftActionEvent.getShiftId();
        var actionType = shiftActionEvent.getActionType();
        log.info("handling shift action event, shiftId id: {}, action is: {}", shiftId, actionType);

        switch (actionType) {
            case CREATE -> {
                if (xiaoguanjiaShiftRepository.existsById(shiftId)) {
                    log.info("current student existed when receive student create event, student id: {}", shiftId);
                } else {
                    fetchAndSaveLatestShift(shiftId);
                }
            }
            case MODIFY -> {
                xiaoguanjiaShiftRepository.findById(shiftId)
                        .orElseThrow(() -> new XiaoguanjiaShiftNotFoundException("Failed to modify xiaoguanjia shift " + shiftId + ", no such shift found"));
                fetchAndSaveLatestShift(shiftId);
            }
            case REMOVE -> {
                var existedEntity = xiaoguanjiaShiftRepository.findById(shiftId)
                        .orElseThrow(() -> new XiaoguanjiaShiftNotFoundException("Failed to remove xiaoguanjia shift " + shiftId + ", no such shift found"));

                existedEntity.setStatus(ShiftStatus.DELETED);
                xiaoguanjiaShiftRepository.save(existedEntity);
            }
        }
    }

    private void fetchAndSaveLatestShift(String shiftId) {
        var shiftInfo = xiaoguanjiaClient.fetchShift(shiftId);
        xiaoguanjiaShiftRepository.save(shiftInfo.toEntity());
    }
}
