package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ClassStudentModifiedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassStudentService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaClassStudent;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaClassStudentServiceImpl implements XiaoguanjiaClassStudentService {
    private final XiaoguanjiaClassStudentRepository xiaoguanjiaClassStudentRepository;
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaCourseStudentRepository xiaoguanjiaCourseStudentRepository;
    private final XiaoguanjiaCourseRepository xiaoguanjiaCourseRepository;

    @Override
    @Transactional
    public void refreshClassStudents(String classId) {
        xiaoguanjiaClassStudentRepository.deleteByClassId(classId);

        var xiaoguanjiaClassStudents = getXiaoguanjiaClassStudents(classId);
        xiaoguanjiaClassStudentRepository.saveAll(
                xiaoguanjiaClassStudents.stream()
                        .map(XiaoguanjiaClassStudent::toEntity)
                        .toList()
        );
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(fallbackExecution = true)
    public void handleClassStudentModifiedEvent(ClassStudentModifiedEvent classStudentModifiedEvent) {
        var classId = classStudentModifiedEvent.getClassId();
        log.info("handling class student modified event, class id: {}", classId);
        var oldClassStudents = xiaoguanjiaClassStudentRepository.findByClassId(classId);
        var latestClassStudents = getXiaoguanjiaClassStudents(classId).stream().map(XiaoguanjiaClassStudent::toEntity).toList();
        var newStudentIds = getNewStudentIds(oldClassStudents, latestClassStudents);
        xiaoguanjiaClassStudentRepository.deleteByClassId(classId);
        xiaoguanjiaClassStudentRepository.saveAll(latestClassStudents);
        newStudentIds.forEach(studentId -> syncStudentCourse(studentId, classId));
        log.info("handling class student modified event finished, class id: {}", classId);
    }

    private List<XiaoguanjiaClassStudent> getXiaoguanjiaClassStudents(String classId) {
        return Stream.ofNullable(xiaoguanjiaClient.getClassStudents(classId))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(classStudent -> classStudent.toXiaoguanjiaClassStudent(classId))
                .toList();
    }

    private List<String> getNewStudentIds(List<XiaoguanjiaClassStudentEntity> oldClassStudents, List<XiaoguanjiaClassStudentEntity> latestClassStudents) {
        LocalDateTime maxCreatedAt = oldClassStudents.stream()
                .map(XiaoguanjiaClassStudentEntity::getCreatedAt)
                .max(LocalDateTime::compareTo)
                .orElse(null);

        return latestClassStudents.stream()
                .filter(e -> e.getCreatedAt() != null
                        && (maxCreatedAt == null || e.getCreatedAt().isAfter(maxCreatedAt)))
                .map(XiaoguanjiaClassStudentEntity::getStudentId)
                .distinct()
                .toList();
    }

    private void syncStudentCourse(String studentId, String classId) {
        log.info("Start syncing student course for student: {}", studentId);
        List<String> courseIds = xiaoguanjiaCourseRepository.findByClassId(classId).stream().map(XiaoguanjiaCourseEntity::getId).toList();
        xiaoguanjiaCourseStudentRepository.deleteByStudentIdAndCourseIdIn(studentId, courseIds);
        xiaoguanjiaClient.fetchStudentCourseIds(studentId).forEach(courseIdInfo -> {
            if (courseIds.contains(courseIdInfo.courseId())) {
                syncCourseStudent(courseIdInfo.courseId(), studentId);
            }
        });
        log.info("Finished syncing student course for student: {}", studentId);
    }

    private void syncCourseStudent(String courseId, String studentId) {
        log.info("Start syncing course students for course: {}, student: {}", courseId, studentId);
        xiaoguanjiaClient.fetchCourseStudents(courseId).forEach(courseStudent -> {
                if (studentId.equals(courseStudent.studentId())) {
                    xiaoguanjiaCourseStudentRepository.save(courseStudent.toEntity(courseId));
                }
            }
        );
        log.info("Finished syncing course students for course: {}, student: {}", courseId, studentId);
    }
}
