package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaStudentEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public interface XiaoguanjiaStudentRepository extends JpaRepository<XiaoguanjiaStudentEntity, String> {
    Page<XiaoguanjiaStudentEntity> findAllByStatusIn(Pageable pageable, Collection<XiaoguanjiaStudentStatus> status);

    Page<XiaoguanjiaStudentEntity> findByNameLikeIgnoreCaseAndStatusIn(Pageable pageable, String name, Collection<XiaoguanjiaStudentStatus> status);

    List<XiaoguanjiaStudentEntity> findAllByIdIn(List<String> studentIds);
}
