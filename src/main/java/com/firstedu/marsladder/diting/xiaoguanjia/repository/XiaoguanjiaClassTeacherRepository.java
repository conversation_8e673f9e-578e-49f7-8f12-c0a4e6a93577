package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassTeacherEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.TeacherRole;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XiaoguanjiaClassTeacherRepository extends JpaRepository<XiaoguanjiaClassTeacherEntity, String> {
    void deleteByClassId(String classId);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<XiaoguanjiaClassTeacherEntity> findByClassId(String classId);

    XiaoguanjiaClassTeacherEntity findByClassIdAndRole(String classId, TeacherRole role);
}
