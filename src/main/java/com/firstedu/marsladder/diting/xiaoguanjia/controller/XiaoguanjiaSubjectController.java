package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaSubjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class XiaoguanjiaSubjectController {
    private final XiaoguanjiaSubjectService xiaoguanjiaSubjectService;

    @ResponseStatus(OK)
    @PostMapping("/subjects/sync")
    public void syncSubjects() {
        xiaoguanjiaSubjectService.syncSubjects();
    }

    @ResponseStatus(OK)
    @PostMapping("grades/sync")
    public void syncGrades() {
        xiaoguanjiaSubjectService.syncGrades();
    }
}
