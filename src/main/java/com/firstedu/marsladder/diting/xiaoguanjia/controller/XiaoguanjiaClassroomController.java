package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncClassroomsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassroomService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class <PERSON>gua<PERSON>aClassroomController {

    private final XiaoguanjiaClassroomService xiaoguanjiaClassroomService;

    @ResponseStatus(OK)
    @PostMapping("/classrooms/sync")
    public void syncClassrooms(
            @RequestBody SyncClassroomsRequest syncClassroomsRequest
    ) {
        xiaoguanjiaClassroomService.syncClassrooms(syncClassroomsRequest.toCommand());
    }
}
