package com.firstedu.marsladder.diting.xiaoguanjia.event.events;

import com.firstedu.marsladder.diting.xiaoguanjia.event.DepartmentAction;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class DepartmentActionEvent extends ApplicationEvent {
    private final String departmentId;
    private final DepartmentAction actionType;

    public DepartmentActionEvent(Object source, String departmentId, DepartmentAction actionType) {
        super(source);
        this.departmentId = departmentId;
        this.actionType = actionType;
    }
}
