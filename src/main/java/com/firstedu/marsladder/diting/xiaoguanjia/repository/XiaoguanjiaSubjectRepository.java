package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaSubjectEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface XiaoguanjiaSubjectRepository extends JpaRepository<XiaoguanjiaSubjectEntity, String> {
    Optional<XiaoguanjiaSubjectEntity> findByIdAndStatus(String id, DictStatus status);
}
