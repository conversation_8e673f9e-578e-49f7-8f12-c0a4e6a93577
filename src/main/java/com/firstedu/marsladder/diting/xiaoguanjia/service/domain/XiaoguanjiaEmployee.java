package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaEmployeeEntity;

import java.time.LocalDateTime;

public record XiaoguanjiaEmployee(
        String id,
        String name,
        String nickName,
        XiaoguanjiaEmployeeStatus status,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
) {
    public static XiaoguanjiaEmployee from(XiaoguanjiaEmployeeEntity xiaoguanjiaEmployeeEntity) {
        return new XiaoguanjiaEmployee(
                xiaoguanjiaEmployeeEntity.getId(),
                xiaoguanjiaEmployeeEntity.getName(),
                xiaoguanjiaEmployeeEntity.getNickName(),
                xiaoguanjiaEmployeeEntity.getStatus(),
                xiaoguanjiaEmployeeEntity.getCreatedAt(),
                xiaoguanjiaEmployeeEntity.getUpdatedAt()
        );
    }
}
