package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.TeacherRole;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

import java.time.LocalDateTime;
import java.util.List;

import static org.hibernate.type.SqlTypes.JSON;

@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Getter
@Builder
@Entity(name = "xiaoguanjia_class_teacher")
public class XiaoguanjiaClassTeacherEntity {
    @Id
    @UuidGenerator
    private String id;

    private String classId;

    private String employeeId;

    private String subjectId;

    @Enumerated(EnumType.STRING)
    private TeacherRole role;

    @JdbcTypeCode(JSON)
    private List<String> types;

    LocalDateTime createdAt;

    @UpdateTimestamp
    LocalDateTime updatedAt;
}
