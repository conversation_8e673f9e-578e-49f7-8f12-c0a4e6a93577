package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

public enum ClassType {
    // 正常班级
    NORMAL,

    // 一对一班级
    ONE_ON_ONE,

    // 补课班级
    MAKEUP,

    // 试听班级
    TRIAL;

    public static ClassType of(int value) {
        switch (value) {
            case 0 -> {
                return NORMAL;
            }
            case 1 -> {
                return ONE_ON_ONE;
            }
            case 2 -> {
                return MAKEUP;
            }
            case 3 -> {
                return TRIAL;
            }
            default -> throw new IllegalArgumentException("Unknown ClassType value: " + value);
        }
    }
}
