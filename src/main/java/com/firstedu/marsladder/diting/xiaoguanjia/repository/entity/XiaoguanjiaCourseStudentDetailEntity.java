package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity(name = "view_xiaoguanjia_course_student_detail")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class XiaoguanjiaCourseStudentDetailEntity {
    @EmbeddedId
    private XiaoguanjiaCourseStudentDetailId id;

    private String studentName;

    private int studentIsAttend;

    private double studentCost;

    private String studentAbsentCauseId;

    private String classId;

    private String className;

    // 是否已上课（已点名）（0未上课，1已上课，2已取消）
    private int courseIsFinished;

    private Boolean courseDeleted;

    private LocalDateTime courseStartTime;

    private LocalDateTime courseEndTime;

    private String courseTeacherId;

    private String courseTeacherName;

    private String courseTeacherNickName;

    private String shiftId;

    private String shiftName;

    private int shiftUnit;

    private String classroomId;

    private String classroomName;

    private String campusId;

    private String campusName;

    private String subjectId;

    private String subjectName;
}
