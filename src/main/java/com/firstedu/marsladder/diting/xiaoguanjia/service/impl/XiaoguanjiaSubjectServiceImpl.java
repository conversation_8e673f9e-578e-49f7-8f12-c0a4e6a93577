package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.client.xiaoguanjia.dto.DictInfo;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaGradeRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaSubjectRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaSubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaSubjectServiceImpl implements XiaoguanjiaSubjectService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaSubjectRepository xiaoguanjiaSubjectRepository;
    private final XiaoguanjiaGradeRepository xiaoguanjiaGradeRepository;

    private final static String SHIFT_SUBJECT_TYPE = "SHIFT_SUBJECT";
    private final static String SHIFT_GRADE_TYPE = "SHIFT_GRADE";

    @Override
    public void syncSubjects() {
        log.info("Start syncing subjects");
        List<DictInfo> dictInfos = xiaoguanjiaClient.fetchDict(SHIFT_SUBJECT_TYPE);
        dictInfos.forEach(dictInfo -> {
            xiaoguanjiaSubjectRepository.save(dictInfo.toSubjectEntity());
        });
        log.info("Finished syncing subjects");
    }

    @Override
    public void syncGrades() {
        log.info("Start syncing grades");
        List<DictInfo> dictInfos = xiaoguanjiaClient.fetchDict(SHIFT_GRADE_TYPE);
        dictInfos.forEach(dictInfo -> {
            xiaoguanjiaGradeRepository.save(dictInfo.toGradeEntity());
        });
        log.info("Finished syncing subjects");
    }
}
