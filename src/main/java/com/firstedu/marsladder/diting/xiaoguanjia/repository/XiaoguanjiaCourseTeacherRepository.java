package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseTeacherEntity;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XiaoguanjiaCourseTeacherRepository extends JpaRepository<XiaoguanjiaCourseTeacherEntity, String> {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<XiaoguanjiaCourseTeacherEntity> findByCourseId(String courseId);

    void deleteByCourseId(String courseId);
}
