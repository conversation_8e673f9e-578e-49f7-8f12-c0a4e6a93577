package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import lombok.Getter;

@Getter
public enum XiaoguanjiaEmployeeStatus {
    INACTIVE(0),
    ACTIVE(1),
    DELETED(-1);

    private final int code;

    XiaoguanjiaEmployeeStatus(int code) {
        this.code = code;
    }

    public static XiaoguanjiaEmployeeStatus fromCode(int code) {
        return java.util.Arrays.stream(XiaoguanjiaEmployeeStatus.values())
                .filter(status -> status.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown status code: " + code));
    }
}
