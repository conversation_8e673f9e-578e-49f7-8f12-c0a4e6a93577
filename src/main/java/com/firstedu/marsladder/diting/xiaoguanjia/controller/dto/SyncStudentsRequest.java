package com.firstedu.marsladder.diting.xiaoguanjia.controller.dto;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncStudentsCommand;

import java.time.LocalDate;

public record SyncStudentsRequest(
        LocalDate startTimeAfter, // this is beijing time to fit xiaoguanjia api
        int pageSize
) {
    public SyncStudentsCommand toSyncStudentsCommand() {
        return new SyncStudentsCommand(startTimeAfter, pageSize);
    }
}
