package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncStudentsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaStudentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class XiaoguanjiaStudentController {

    private final XiaoguanjiaStudentService xiaoguanjiaStudentService;

    @ResponseStatus(OK)
    @PostMapping("/students/sync")
    public void syncStudents(
            @RequestBody SyncStudentsRequest syncStudentsRequest
    ) {
        xiaoguanjiaStudentService.syncStudents(syncStudentsRequest.toSyncStudentsCommand());
    }
}
