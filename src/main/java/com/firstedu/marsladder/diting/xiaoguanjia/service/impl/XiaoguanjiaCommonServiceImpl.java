package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaEventRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaCommonService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class XiaoguanjiaCommonServiceImpl implements XiaoguanjiaCommonService {

    private final XiaoguanjiaClient xiaoguanjiaClient;

    private final XiaoguanjiaEventRepository xiaoguanjiaEventRepository;

    @Override
    public String getAccessToken() {
        return xiaoguanjiaClient.getAccessToken();
    }

    @Override
    public void saveEvent(XiaoguanjiaEvent event) {
        xiaoguanjiaEventRepository.save(event.toEntity());
    }
}
