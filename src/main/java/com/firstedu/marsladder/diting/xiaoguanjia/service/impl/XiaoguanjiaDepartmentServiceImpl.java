package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.exception.NotFoundException;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.DepartmentActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaDepartmentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaDepartmentService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.Campus;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncDepartmentsCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaDepartmentServiceImpl implements XiaoguanjiaDepartmentService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaDepartmentRepository xiaoguanjiaDepartmentRepository;

    @Override
    @Transactional
    public void syncDepartments(SyncDepartmentsCommand syncDepartmentsCommand) {
        var pageNumber = 1;
        var hasMore = 1;
        log.info("Start syncing departments");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfDepartments = xiaoguanjiaClient.getPageOfDepartments(
                    syncDepartmentsCommand.updateTimeAfter(),
                    syncDepartmentsCommand.pageSize(),
                    pageNumber
            );

            for (var departmentInfo : pageOfDepartments.data()) {
                xiaoguanjiaDepartmentRepository.save(departmentInfo.toEntity());
            }
            pageNumber += 1;
            hasMore = pageOfDepartments.hasMore();
        }
        log.info("Finished syncing departments");
    }

    @Override
    public List<Campus> getAllCampuses() {
        return xiaoguanjiaDepartmentRepository.findAllByDeletedAndStatusAndIsCampus(false, 1, true).stream()
                .map(Campus::from)
                .toList();
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void handleDepartmentActionEvent(DepartmentActionEvent actionEvent) {
        var departmentId = actionEvent.getDepartmentId();
        var actionType = actionEvent.getActionType();
        log.info("handling department action event, department id: {}, action is: {}", departmentId, actionType);

        switch (actionType) {
            case CREATE -> {
                if (xiaoguanjiaDepartmentRepository.existsById(departmentId)) {
                    log.info("current department existed when receive employee create event, department id: {}", departmentId);
                } else {
                    fetchAndSaveLatestDepartment(departmentId);
                }
            }
            case MODIFY -> {
                xiaoguanjiaDepartmentRepository.findById(departmentId)
                        .orElseThrow(() -> new NotFoundException("Failed to modify xiaoguanjia department " + departmentId + ", no such department found"));

                fetchAndSaveLatestDepartment(departmentId);
            }
            case REMOVE -> {
                var existedEntity = xiaoguanjiaDepartmentRepository.findById(departmentId)
                        .orElseThrow(() -> new NotFoundException("Failed to remove xiaoguanjia department " + departmentId + ", no such department found"));

                existedEntity.setStatus(0);
                xiaoguanjiaDepartmentRepository.save(existedEntity);
            }
        }
    }

    private void fetchAndSaveLatestDepartment(String departmentId) {
        var departmentInfo = xiaoguanjiaClient.fetchDepartment(departmentId);
        xiaoguanjiaDepartmentRepository.save(departmentInfo.toEntity());
    }
}
