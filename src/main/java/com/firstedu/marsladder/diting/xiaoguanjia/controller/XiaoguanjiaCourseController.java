package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncCoursesRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaCourseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class XiaoguanjiaCourseController {

    private final XiaoguanjiaCourseService xiaoguanjiaCourseService;

    @ResponseStatus(OK)
    @PostMapping("/courses/sync")
    public void syncCourses(
            @RequestBody SyncCoursesRequest syncCoursesRequest
    ) {
        xiaoguanjiaCourseService.syncCourses(syncCoursesRequest.toSyncCoursesCommand());
    }
}
