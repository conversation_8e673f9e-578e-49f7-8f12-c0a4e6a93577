package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.XiaoguanjiaAccessTokenResponse;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.XiaoguanjiaEventRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
@Log4j2
public class XiaoguanjiaCommonController {

    private final XiaoguanjiaCommonService xiaoguanjiaCommonService;

    @GetMapping("xiaogj-access-token")
    public ResponseEntity<XiaoguanjiaAccessTokenResponse> getAccessToken() {
        return ResponseEntity.ok(XiaoguanjiaAccessTokenResponse.builder().accessToken(xiaoguanjiaCommonService.getAccessToken()).build());
    }

    @ResponseStatus(OK)
    @PostMapping("/public/xiaogj-events")
    public String events(
            @RequestBody XiaoguanjiaEventRequest request
    ) {
        log.info("Xiaoguanjia Event Received: ");
        log.info(request.toString());
        xiaoguanjiaCommonService.saveEvent(request.toDomain());
        return "SUCCESS"; // required by xiaoguanjia
    }
}
