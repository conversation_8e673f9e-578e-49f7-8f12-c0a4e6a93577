package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassType;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.*;

import java.time.LocalDateTime;

import static jakarta.persistence.EnumType.STRING;

@Entity(name = "xiaoguanjia_class")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Getter
@Setter
public class XiaoguanjiaClassEntity {
    @Id
    private String id;

    private String name;

    private String gradeId;

    private String subjectId;

    private Boolean deleted;

    private String campusId;

    private String shiftId;

    private Integer maxStudentAmount;

    private LocalDateTime openDate;

    private Boolean isFinished;

    private LocalDateTime finishedDate;

    // 上课时间,
    // example: "周六13:30~15:30"
    private String courseTime;

    private String headmasterId;

    private String classroomId;

    @Enumerated(STRING)
    private ClassType type;

    private LocalDateTime closeDate;

    // 计划排课次数 x<PERSON><PERSON><PERSON><PERSON><PERSON> set this to float
    private Float courseTimes;

    private String shiftScheduleId;

    // example: "2016-10-2518:30-20:30[周二]"
    // seems no longer being used
    private String lastClassTime;

    private Integer shiftAmount;

    private Integer validAttendMinutes;

    private String tag;

    private Boolean isSkipHoliday;

    private Boolean isSendCourseMsg;

    // 0 无效 1 有效 from xiaoguanjia api
    private Integer status;

    private String description;

    private LocalDateTime lastCourseEndTime;

    private LocalDateTime earliestCourseStartTime;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public boolean isDeleted() {
        return deleted;
    }
}
