package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

public enum TeacherRole {
    TEACHER, ASSISTANT;

    public static TeacherRole of(int value) {
        switch (value) {
            case 1 -> {
                return TEACHER;
            }
            case 2 -> {
                return ASSISTANT;
            }
            default -> throw new IllegalArgumentException("Unknown Teacher role value: " + value);
        }
    }
}
