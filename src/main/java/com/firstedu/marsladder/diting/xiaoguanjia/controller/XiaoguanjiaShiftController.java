package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncShiftsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaShiftService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class XiaoguanjiaShiftController {

    private final XiaoguanjiaShiftService xiaoguanjiaShiftService;

    @ResponseStatus(OK)
    @PostMapping("/shifts/sync")
    public void syncShifts(
            @RequestBody SyncShiftsRequest syncShiftsRequest
    ) {
        xiaoguanjiaShiftService.syncShifts(syncShiftsRequest.toSyncShiftsCommand());
    }
}
