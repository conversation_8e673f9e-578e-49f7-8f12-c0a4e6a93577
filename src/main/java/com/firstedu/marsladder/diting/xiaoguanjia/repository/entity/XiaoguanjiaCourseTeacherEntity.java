package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

import static jakarta.persistence.GenerationType.UUID;

@Entity(name = "xiaoguanjia_course_teacher")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class XiaoguanjiaCourseTeacherEntity {
    @Id
    @GeneratedValue(strategy = UUID)
    private String id;

    private String courseId;

    private String employeeId;

    private int role;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
