package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaCourseNotExistException;
import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.CourseUpdatedOrCreatedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.CourseRemovedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseTeacherRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaCourseService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCourseStudentsCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCourseTeachersCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCoursesCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaCourseServiceImpl implements XiaoguanjiaCourseService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaCourseRepository xiaoguanjiaCourseRepository;
    private final XiaoguanjiaCourseTeacherRepository xiaoguanjiaCourseTeacherRepository;
    private final XiaoguanjiaCourseStudentRepository xiaoguanjiaCourseStudentRepository;

    @Override
    @Transactional
    public void syncCourses(SyncCoursesCommand syncCoursesCommand) {
        var pageNumber = 1;
        var hasMore = 1;
        log.info("Start syncing courses");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfCourses = xiaoguanjiaClient.getPageOfCourses(
                    syncCoursesCommand.startTimeAfter(),
                    syncCoursesCommand.pageSize(),
                    pageNumber
            );

            for (var courseInfo : pageOfCourses.data()) {
                xiaoguanjiaCourseRepository.save(courseInfo.toEntity());
                syncCourseTeacher(new SyncCourseTeachersCommand(courseInfo.courseId()));
                syncCourseStudent(new SyncCourseStudentsCommand(courseInfo.courseId()));
            }
            pageNumber += 1;
            hasMore = pageOfCourses.hasMore();
        }
        log.info("Finished syncing courses");
    }

    @Override
    @Transactional
    public void syncCourseTeacher(SyncCourseTeachersCommand command) {
        log.info("Start syncing course teachers for course: {}", command.courseId());
        xiaoguanjiaCourseTeacherRepository.findByCourseId(command.courseId());
        xiaoguanjiaCourseTeacherRepository.deleteByCourseId(command.courseId());
        xiaoguanjiaClient.fetchCourseTeachers(command.courseId()).forEach(courseTeacher ->
                xiaoguanjiaCourseTeacherRepository.save(courseTeacher.toEntity(command.courseId()))
        );
        log.info("Finished syncing course teachers for course: {}", command.courseId());
    }

    @Override
    @Transactional
    public void syncCourseStudent(SyncCourseStudentsCommand command) {
        log.info("Start syncing course students for course: {}", command.courseId());
        xiaoguanjiaCourseStudentRepository.findByCourseId(command.courseId());
        xiaoguanjiaCourseStudentRepository.deleteByCourseId(command.courseId());
        xiaoguanjiaClient.fetchCourseStudents(command.courseId()).forEach(courseStudent ->
                xiaoguanjiaCourseStudentRepository.save(courseStudent.toEntity(command.courseId()))
        );
        log.info("Finished syncing course students for course: {}", command.courseId());
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(fallbackExecution = true)
    public void handleCourseUpdatedOrCreatedEvent(CourseUpdatedOrCreatedEvent event) {
        var id = event.getCourseId();
        log.info("handling course updated or created event, course id: {}", id);
        var courseInfo = xiaoguanjiaClient.fetchCourse(id);
        xiaoguanjiaCourseRepository.save(courseInfo.toEntity());
        syncCourseTeacher(new SyncCourseTeachersCommand(courseInfo.courseId()));
        syncCourseStudent(new SyncCourseStudentsCommand(courseInfo.courseId()));
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void handleCourseRemovedEvent(CourseRemovedEvent event) {
        var id = event.getCourseId();
        log.info("handling course removed event, course id: {}", id);

        var existingCourse = xiaoguanjiaCourseRepository.findById(id)
                .orElseThrow(() -> new XiaoguanjiaCourseNotExistException("Failed to remove xiaoguanjia course " + id + ", no such course found"));
        if (!existingCourse.isDeleted()) {
            existingCourse.setDeleted(true);
            xiaoguanjiaCourseRepository.save(existingCourse);
        }
    }
}
