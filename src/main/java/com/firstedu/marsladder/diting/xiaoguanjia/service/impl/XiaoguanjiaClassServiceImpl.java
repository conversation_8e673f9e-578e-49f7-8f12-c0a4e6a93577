package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaClassNotExistException;
import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ClassCreatedOrUpdatedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.ClassRemovedEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassStudentService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncClassesCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaClassServiceImpl implements XiaoguanjiaClassService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaClassRepository xiaoguanjiaClassRepository;
    private final XiaoguanjiaClassStudentService xiaoguanjiaClassStudentService;
    private final XiaoguanjiaClassTeacherServiceImpl xiaoguanjiaClassTeacherService;

    @Override
    @Transactional
    public void syncClasses(SyncClassesCommand syncClassesCommand) {
        var pageNumber = 1; // page number from xiaoguanjia starts from 1 instead of 0
        var hasMore = 1;
        log.info("Start syncing classes");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfClasses = xiaoguanjiaClient.getPageOfClasses(
                    syncClassesCommand.createdAfter(),
                    syncClassesCommand.pageSize(),
                    pageNumber
            );
            pageOfClasses
                    .data()
                    .forEach(classInfo -> {
                        xiaoguanjiaClassRepository.save(classInfo.toEntity());
                        xiaoguanjiaClassStudentService.refreshClassStudents(classInfo.classId());
                        xiaoguanjiaClassTeacherService.refreshClassTeachers(classInfo.classId());
                    });
            pageNumber += 1;
            hasMore = pageOfClasses.has_more();
        }
        log.info("Finished syncing classes");
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void handleClassCreatedOrUpdatedEvent(ClassCreatedOrUpdatedEvent classCreatedOrModifiedEvent) {
        var classId = classCreatedOrModifiedEvent.getClassId();
        log.info("handling class created or modified event, class id: {}", classId);

        var classInfo = xiaoguanjiaClient.fetchClass(classId);
        xiaoguanjiaClassRepository.save(classInfo.toEntity());
        xiaoguanjiaClassStudentService.refreshClassStudents(classInfo.classId());
        xiaoguanjiaClassTeacherService.refreshClassTeachers(classInfo.classId());
    }

    @Async
    @TransactionalEventListener(fallbackExecution = true)
    public void handleClassRemovedEvent(ClassRemovedEvent classRemovedEvent) {
        var classId = classRemovedEvent.getClassId();
        log.info("handling class removed event, class id: {}", classId);

        var existingClass = xiaoguanjiaClassRepository.findById(classId)
                .orElseThrow(() -> new XiaoguanjiaClassNotExistException("Failed to remove xiaoguanjia class " + classId + ", no such class found"));
        if (existingClass.isDeleted()) {
            return;
        } else {
            existingClass.setDeleted(true);
            xiaoguanjiaClassRepository.save(existingClass);
        }
    }
}
