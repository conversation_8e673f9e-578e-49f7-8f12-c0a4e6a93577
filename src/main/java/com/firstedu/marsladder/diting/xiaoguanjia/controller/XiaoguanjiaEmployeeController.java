package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncEmployeesRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaEmployeeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class XiaoguanjiaEmployeeController {

    private final XiaoguanjiaEmployeeService xiaoguanjiaEmployeeService;

    @ResponseStatus(OK)
    @PostMapping("/employees/sync")
    public void syncCourses(
            @RequestBody SyncEmployeesRequest syncEmployeesRequest
    ) {
        xiaoguanjiaEmployeeService.syncEmployee(syncEmployeesRequest.toSyncEmployeesCommand());
    }
}
