package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaEventEntity;
import lombok.Builder;

@Builder
public record XiaoguanjiaEvent(
     String companyId,
     String appId,
     String eventKey,
     String eventId, // by xiaoguanjia api, this is not the id of the event, but the id of the affected resource
     String eventTime
) {
    public XiaoguanjiaEventEntity toEntity() {
        return XiaoguanjiaEventEntity.builder()
                .companyId(this.companyId)
                .appId(this.appId)
                .eventKey(this.eventKey)
                .eventId(this.eventId)
                .eventTime(this.eventTime)
                .build();
    }
}
