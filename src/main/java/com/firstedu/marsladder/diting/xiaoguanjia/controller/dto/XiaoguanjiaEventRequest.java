package com.firstedu.marsladder.diting.xiaoguanjia.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEvent;
import lombok.Builder;

@Builder
public record XiaoguanjiaEventRequest(
        @JsonProperty("companyid") String companyId,
        @JsonProperty("appid") String appId,
        @JsonProperty("eventkey") String eventKey,
        @JsonProperty("eventid") String eventId,
        @JsonProperty("eventtime") String eventTime
) {
    public XiaoguanjiaEvent toDomain() {
        return XiaoguanjiaEvent.builder()
                .companyId(this.companyId)
                .appId(this.appId)
                .eventKey(this.eventKey)
                .eventId(this.eventId)
                .eventTime(this.eventTime)
                .build();
    }
}
