package com.firstedu.marsladder.diting.xiaoguanjia.controller.dto;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCoursesCommand;

import java.time.LocalDate;

public record SyncCoursesRequest(
        LocalDate startTimeAfter, // this is beijing time to fit xiaoguanjia api
        int pageSize
) {
    public SyncCoursesCommand toSyncCoursesCommand() {
        return new SyncCoursesCommand(startTimeAfter, pageSize);
    }
}
