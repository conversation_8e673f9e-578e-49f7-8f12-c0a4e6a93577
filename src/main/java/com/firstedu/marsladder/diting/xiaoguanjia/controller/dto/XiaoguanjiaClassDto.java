package com.firstedu.marsladder.diting.xiaoguanjia.controller.dto;

import com.firstedu.marsladder.diting.xgjclass.service.domain.XiaoguanjiaClass;

import java.util.List;

public record XiaoguanjiaClassDto(
        String id,
        String name
) {

    public static XiaoguanjiaClassDto from(XiaoguanjiaClass xiaoguanjiaClass) {
        return new XiaoguanjiaClassDto(xiaoguanjiaClass.id(), xiaoguanjiaClass.name());
    }

    public static List<XiaoguanjiaClassDto> listFrom(List<XiaoguanjiaClass> classes) {
        return classes
                .stream()
                .map(XiaoguanjiaClassDto::from)
                .toList();
    }
}
