package com.firstedu.marsladder.diting.xiaoguanjia.event;

import com.firstedu.marsladder.diting.xiaoguanjia.event.events.*;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaEventEntity;
import jakarta.persistence.PostPersist;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class XiaoguanjiaEventEntityEventHandler {
    private final ApplicationEventPublisher publisher;

    public XiaoguanjiaEventEntityEventHandler(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    @PostPersist
    public void handle(XiaoguanjiaEventEntity entity) {
        var eventId = entity.getEventId();
        var eventKey = entity.getEventKey();
        log.info("handling xiaoguanjia event: {} {}", eventKey, eventId);

        switch (eventKey) {
            case "edu_class_create",
                 "edu_class_modify",
                 "edu_class_finish"  -> publisher.publishEvent(new ClassCreatedOrUpdatedEvent(this, eventId));
            case "edu_class_remove" -> publisher.publishEvent(new ClassRemovedEvent(this, eventId));

            case "edu_class_student_modify" -> publisher.publishEvent(new ClassStudentModifiedEvent(this, eventId));

            case "edu_course_create",
                 "edu_course_modify",
                 "edu_course_attend",
                 "edu_course_attendcancel",
                 "edu_course_student_modify",
                 "edu_course_cancel" -> publisher.publishEvent(new CourseUpdatedOrCreatedEvent(this, eventId));
            case "edu_course_remove" -> publisher.publishEvent(new CourseRemovedEvent(this, eventId));

            case "user_student_create" -> publisher.publishEvent(new StudentActionEvent(this, eventId, StudentAction.CREATE));
            case "user_student_modify" -> publisher.publishEvent(new StudentActionEvent(this, eventId, StudentAction.MODIFY));
            case "user_student_remove" -> publisher.publishEvent(new StudentActionEvent(this, eventId, StudentAction.REMOVE));

            case "org_employee_create" -> publisher.publishEvent(new EmployeeActionEvent(this, eventId, EmployeeAction.CREATE));
            case "org_employee_modify" -> publisher.publishEvent(new EmployeeActionEvent(this, eventId, EmployeeAction.MODIFY));
            case "org_employee_remove" -> publisher.publishEvent(new EmployeeActionEvent(this, eventId, EmployeeAction.REMOVE));

            case "edu_shift_create" -> publisher.publishEvent(new ShiftActionEvent(this, eventId, ShiftAction.CREATE));
            case "edu_shift_modify" -> publisher.publishEvent(new ShiftActionEvent(this, eventId, ShiftAction.MODIFY));
            case "edu_shift_remove" -> publisher.publishEvent(new ShiftActionEvent(this, eventId, ShiftAction.REMOVE));

            case "base_dept_create" -> publisher.publishEvent(new DepartmentActionEvent(this, eventId, DepartmentAction.CREATE));
            case "base_dept_modify" -> publisher.publishEvent(new DepartmentActionEvent(this, eventId, DepartmentAction.MODIFY));
            case "base_dept_remove" -> publisher.publishEvent(new DepartmentActionEvent(this, eventId, DepartmentAction.REMOVE));

            default -> log.info("No matching event for xiaoguanjia event {}, id: {}", eventKey, eventId);
        }
    }
}