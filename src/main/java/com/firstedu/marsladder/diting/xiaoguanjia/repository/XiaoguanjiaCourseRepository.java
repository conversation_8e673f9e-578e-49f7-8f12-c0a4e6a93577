package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseEntity;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface XiaoguanjiaCourseRepository extends JpaRepository<XiaoguanjiaCourseEntity, String> {
    List<XiaoguanjiaCourseEntity> findByClassId(String classId);
}
