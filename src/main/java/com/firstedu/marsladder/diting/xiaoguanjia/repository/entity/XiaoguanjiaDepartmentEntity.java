package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity(name = "xiaoguanjia_department")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class XiaoguanjiaDepartmentEntity {
    @Id
    private String id;

    private String name;

    private boolean isCampus;

    private int status;

    private String briefName;

    private boolean deleted;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
