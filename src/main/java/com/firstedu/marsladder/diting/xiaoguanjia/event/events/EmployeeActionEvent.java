package com.firstedu.marsladder.diting.xiaoguanjia.event.events;

import com.firstedu.marsladder.diting.xiaoguanjia.event.EmployeeAction;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class EmployeeActionEvent extends ApplicationEvent {
    private final String employeeId;
    private final EmployeeAction actionType;

    public EmployeeActionEvent(Object source, String employeeId, EmployeeAction actionType) {
        super(source);
        this.employeeId = employeeId;
        this.actionType = actionType;
    }
}
