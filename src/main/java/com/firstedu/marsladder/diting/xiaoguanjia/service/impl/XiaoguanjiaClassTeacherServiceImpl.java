package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassTeacherRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassTeacherService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class XiaoguanjiaClassTeacherServiceImpl implements XiaoguanjiaClassTeacherService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaClassTeacherRepository xiaoguanjiaClassTeacherRepository;

    @Override
    @Transactional
    public void refreshClassTeachers(String classId) {
        // lock rows to prevent other thread from saving to class teacher table
        xiaoguanjiaClassTeacherRepository.findByClassId(classId);
        xiaoguanjiaClassTeacherRepository.deleteByClassId(classId);

        xiaoguanjiaClient.getClassTeachers(classId).forEach(
                classTeacher -> {
                    xiaoguanjiaClassTeacherRepository.save(classTeacher.toEntity(classId));
                }
        );
    }

}
