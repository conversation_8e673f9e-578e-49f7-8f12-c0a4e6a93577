package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import lombok.Builder;

import java.time.LocalDateTime;

@Builder
public record XiaoguanjiaClassStudent(
        String id,
        String classId,
        String studentId,
        ClassStudentStatus status,
        LocalDateTime inDate,
        LocalDateTime outDate,
        String outCauseId,
        String outMemo,
        String outType,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
) {
    public XiaoguanjiaClassStudentEntity toEntity() {
        return new XiaoguanjiaClassStudentEntity(
                id,
                classId,
                studentId,
                status,
                inDate,
                outDate,
                outCauseId,
                outMemo,
                outType,
                createdAt,
                updatedAt
        );
    }

    public static XiaoguanjiaClassStudent fromEntity(XiaoguanjiaClassStudentEntity entity) {
        return new XiaoguanjiaClassStudent(
                entity.getId(),
                entity.getClassId(),
                entity.getStudentId(),
                entity.getStatus(),
                entity.getInDate(),
                entity.getOutDate(),
                entity.getOutCauseId(),
                entity.getOutMemo(),
                entity.getOutType(),
                entity.getCreatedAt(),
                entity.getUpdatedAt()
        );
    }
}
