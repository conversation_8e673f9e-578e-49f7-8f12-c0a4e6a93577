package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity(name = "xiaoguanjia_student")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Getter
@Builder
public class XiaoguanjiaStudentEntity {
    @Id
    private String id;
    @Setter
    @Enumerated(EnumType.STRING)
    private XiaoguanjiaStudentStatus status;
    private String campusId;
    private String name;
    private String englishName;
    private Integer sex;
    private String smsTel;
    private String address;
    private String userName;
    private String headImg;
    private String serial;
    private String idNumber;
    private LocalDateTime birthday;
    private LocalDateTime birthdayMoon;
    private String fullTimeSchool;
    private String fullTimeClass;
    private String gradeCode;
    private LocalDateTime gradeBaseDate;
    private String masterId;
    private LocalDateTime masterAdjustDate;
    private String typeId;
    private LocalDateTime importTime;
    private LocalDateTime feeUpdateTime;
    private String customerCampusId;
    private String introducerId;
    private LocalDateTime returnDate;
    private Integer isOutTransCustomer;
    private Integer tryStatus;
    private LocalDateTime tryStatusDate;
    private LocalDateTime inDate;
    private LocalDateTime outDate;
    private LocalDateTime quitSchoolDate;
    private Integer isExceptionOut;
    private String outCause;
    private String outCauseId;
    private String describe;
    private Integer canLoginSsx;
    private String salePersonId;
    private String point;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
