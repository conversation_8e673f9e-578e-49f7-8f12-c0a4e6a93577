package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity(name = "xiaoguanjia_classroom")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class XiaoguanjiaClassroomEntity {
    @Id
    private String id;

    private String campusId;

    private String name;

    private int personCount;

    private int status;

    private boolean deleted;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
