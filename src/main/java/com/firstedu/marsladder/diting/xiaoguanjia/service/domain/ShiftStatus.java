package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

public enum ShiftStatus {
    DELETED,
    DISABLED,
    ENABLED;

    public static ShiftStatus of(int value) {
        switch (value) {
            case -1 -> {
                return DELETED;
            }
            case 0 -> {
                return DISABLED;
            }
            case 1 -> {
                return ENABLED;
            }
            default -> throw new IllegalArgumentException("Unknown ShiftStatus value: " + value);
        }
    }
}
