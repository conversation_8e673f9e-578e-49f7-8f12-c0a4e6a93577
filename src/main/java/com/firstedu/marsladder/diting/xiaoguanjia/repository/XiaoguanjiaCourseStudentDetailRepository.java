package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface XiaoguanjiaCourseStudentDetailRepository extends JpaRepository<XiaoguanjiaCourseStudentDetailEntity, XiaoguanjiaCourseStudentDetailId>, JpaSpecificationExecutor<XiaoguanjiaCourseStudentDetailEntity> {
}
