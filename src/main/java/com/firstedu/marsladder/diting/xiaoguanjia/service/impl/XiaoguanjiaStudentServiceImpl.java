package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.classroom.exception.XiaoguanjiaStudentNotFoundException;
import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.event.events.StudentActionEvent;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaCourseStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaStudentRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaStudentService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncStudentsCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaStudentStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaStudentServiceImpl implements XiaoguanjiaStudentService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaStudentRepository xiaoguanjiaStudentRepository;
    private final XiaoguanjiaCourseStudentRepository xiaoguanjiaCourseStudentRepository;

    @Override
    public void syncStudents(SyncStudentsCommand syncStudentsCommand) {
        var pageNumber = 1;
        var hasMore = 1;
        log.info("Start syncing students");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfStudents = xiaoguanjiaClient.getPageOfStudents(
                    syncStudentsCommand.startTimeAfter(),
                    syncStudentsCommand.pageSize(),
                    pageNumber
            );

            for (var courseStudent : pageOfStudents.data()) {
                xiaoguanjiaStudentRepository.save(courseStudent.toEntity());
            }
            pageNumber += 1;
            hasMore = pageOfStudents.hasMore();
        }
        log.info("Finished syncing students");
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(fallbackExecution = true)
    public void handleClassStudentEvent(StudentActionEvent actionEvent) {
        var studentId = actionEvent.getStudentId();
        var actionType = actionEvent.getActionType();
        log.info("handling student action event, student id: {}, action is: {}", studentId, actionType);

        switch (actionType) {
            case CREATE -> {
                if (xiaoguanjiaStudentRepository.existsById(studentId)) {
                    log.info("current student existed when receive student create event, student id: {}", studentId);
                } else {
                    fetchAndSaveLatestStudent(studentId);
                }
            }
            case MODIFY -> {
                xiaoguanjiaStudentRepository.findById(studentId)
                        .orElseThrow(() -> new XiaoguanjiaStudentNotFoundException("Failed to modify xiaoguanjia student " + studentId + ", no such class found"));

                fetchAndSaveLatestStudent(studentId);
                syncStudentCourse(studentId);
            }
            case REMOVE -> {
                var existedEntity = xiaoguanjiaStudentRepository.findById(studentId)
                        .orElseThrow(() -> new XiaoguanjiaStudentNotFoundException("Failed to remove xiaoguanjia student " + studentId + ", no such class found"));

                existedEntity.setStatus(XiaoguanjiaStudentStatus.DELETED);
                xiaoguanjiaStudentRepository.save(existedEntity);
            }
        }
        log.info("handling student action event finished, student id: {}, action is: {}", studentId, actionType);
    }

    private void fetchAndSaveLatestStudent(String studentId) {
        var studentInfo = xiaoguanjiaClient.fetchStudent(studentId);
        xiaoguanjiaStudentRepository.save(studentInfo.toEntity());
    }

    private void syncStudentCourse(String studentId) {
        xiaoguanjiaClient.fetchStudentCourseIds(studentId).forEach(courseIdInfo ->
                syncCourseStudent(courseIdInfo.courseId(), studentId)
        );
    }

    private void syncCourseStudent(String courseId, String studentId) {
        log.info("Start syncing course students for course: {}, student: {}", courseId, studentId);
        xiaoguanjiaCourseStudentRepository.findByCourseIdAndStudentId(courseId, studentId);
        xiaoguanjiaCourseStudentRepository.deleteByCourseIdAndStudentId(courseId, studentId);
        xiaoguanjiaClient.fetchCourseStudents(courseId).forEach(courseStudent -> {
                if (studentId.equals(courseStudent.studentId())) {
                    xiaoguanjiaCourseStudentRepository.save(courseStudent.toEntity(courseId));
                }
            }
        );
        log.info("Finished syncing course students for course: {}, student: {}", courseId, studentId);
    }
}
