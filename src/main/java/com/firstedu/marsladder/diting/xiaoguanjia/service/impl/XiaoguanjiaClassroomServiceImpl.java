package com.firstedu.marsladder.diting.xiaoguanjia.service.impl;

import com.firstedu.marsladder.diting.client.xiaoguanjia.XiaoguanjiaClient;
import com.firstedu.marsladder.diting.xiaoguanjia.repository.XiaoguanjiaClassroomRepository;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassroomService;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncClassroomsCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
@EnableAsync
public class XiaoguanjiaClassroomServiceImpl implements XiaoguanjiaClassroomService {
    private final XiaoguanjiaClient xiaoguanjiaClient;
    private final XiaoguanjiaClassroomRepository xiaoguanjiaClassroomRepository;

    @Override
    @Transactional
    public void syncClassrooms(SyncClassroomsCommand syncClassroomsCommand) {
        var pageNumber = 1; // page number from xiaoguanjia starts from 1 instead of 0
        var hasMore = 1;
        log.info("Start syncing classrooms");
        while (hasMore != 0) {
            log.info("Current page: {} , has more: {}", pageNumber, hasMore);
            var pageOfClassrooms = xiaoguanjiaClient.getPageOfClassrooms(
                    syncClassroomsCommand.updateTimeAfter(),
                    syncClassroomsCommand.pageSize(),
                    pageNumber
            );

            for (var classroomInfo : pageOfClassrooms.data()) {
                xiaoguanjiaClassroomRepository.save(classroomInfo.toEntity());
            }
            pageNumber += 1;
            hasMore = pageOfClassrooms.hasMore();
        }
        log.info("Finished syncing classrooms");
    }
}
