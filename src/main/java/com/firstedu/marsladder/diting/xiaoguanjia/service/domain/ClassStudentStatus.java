package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import lombok.Getter;

@Getter
public enum ClassStudentStatus {
    REMOVED(0),
    ENTERED(1),
    SUSPENDED(2),
    EXITED(3),
    DELETED(-1);

    private final int code;

    ClassStudentStatus(int code) {
        this.code = code;
    }

    public static ClassStudentStatus fromCode(int code) {
        return java.util.Arrays.stream(ClassStudentStatus.values())
                .filter(status -> status.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown status code: " + code));
    }
}
