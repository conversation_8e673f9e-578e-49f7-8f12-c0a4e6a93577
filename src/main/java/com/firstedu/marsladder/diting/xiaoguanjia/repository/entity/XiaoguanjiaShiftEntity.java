package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ShiftStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.*;

import java.time.LocalDateTime;

import static jakarta.persistence.EnumType.STRING;

@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Entity(name = "xiaoguanjia_shift")
@Getter
@Builder
public class XiaoguanjiaShiftEntity {
    @Id
    private String id;

    private String subjectId;

    String name;

    private int year;

    private int unit;

    @Setter
    @Enumerated(STRING)
    private ShiftStatus status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
