package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

import static jakarta.persistence.GenerationType.UUID;

@Entity(name = "xiaoguanjia_course_student")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class XiaoguanjiaCourseStudentEntity {
    @Id
    @GeneratedValue(strategy = UUID)
    private String id;

    private String courseId;

    private String studentId;

    private String studentName;

    private int isAttend;

    private double cost;

    private String absentCauseId;

    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
