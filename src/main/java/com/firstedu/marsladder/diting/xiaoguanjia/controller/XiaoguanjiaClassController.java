package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncClassesRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaClassService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class <PERSON>gua<PERSON>aClassController {

    private final XiaoguanjiaClassService xiaoguanjiaClassService;

    @ResponseStatus(OK)
    @PostMapping("/classes/sync")
    public void syncClasses(
            @RequestBody SyncClassesRequest syncClassesRequest
    ) {
        xiaoguanjiaClassService.syncClasses(syncClassesRequest.toSyncClassesCommand());
    }
}
