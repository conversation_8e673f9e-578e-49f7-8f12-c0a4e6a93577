package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.ClassStudentStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.*;

import java.time.LocalDateTime;

@Entity(name = "xiaoguanjia_class_student")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Getter
@Setter
@Builder
public class XiaoguanjiaClassStudentEntity {
    @Id
    private String id;

    private String classId;

    private String studentId;

    @Enumerated(EnumType.STRING)
    private ClassStudentStatus status;

    // this is not the date when the student joined the class, but rather the date when the student `joined` this state
    private LocalDateTime inDate;
    private LocalDateTime outDate;

    private String outCauseId;

    private String outMemo;

    private String outType;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
