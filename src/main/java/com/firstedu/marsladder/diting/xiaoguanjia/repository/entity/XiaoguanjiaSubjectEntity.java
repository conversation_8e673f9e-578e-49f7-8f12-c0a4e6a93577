package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.*;

import java.time.LocalDateTime;

@Entity(name = "xiaoguanjia_subject")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Getter
@Setter
public class XiaoguanjiaSubjectEntity {
    @Id
    private String id;

    private String value;

    @Enumerated(EnumType.STRING)
    private DictStatus status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

}
