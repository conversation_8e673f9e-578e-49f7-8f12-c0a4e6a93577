package com.firstedu.marsladder.diting.xiaoguanjia.controller.dto;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncClassroomsCommand;

import java.time.LocalDate;

public record SyncClassroomsRequest(
        LocalDate updateTimeAfter, // this is beijing time to fit xiaoguanjia api
        int pageSize
) {
    public SyncClassroomsCommand toCommand() {
        return new SyncClassroomsCommand(updateTimeAfter, pageSize);
    }
}
