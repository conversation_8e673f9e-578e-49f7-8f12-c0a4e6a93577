package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentEntity;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XiaoguanjiaCourseStudentRepository extends JpaRepository<XiaoguanjiaCourseStudentEntity, String> {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<XiaoguanjiaCourseStudentEntity> findByCourseId(String courseId);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<XiaoguanjiaCourseStudentEntity> findByCourseIdAndStudentId(String courseId, String studentId);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    List<XiaoguanjiaCourseStudentEntity> findByStudentId(String studentId);

    void deleteByCourseId(String courseId);

    void deleteByCourseIdAndStudentId(String courseId, String studentId);

    void deleteByStudentIdAndCourseIdIn(String studentId, List<String> courseIds);
}
