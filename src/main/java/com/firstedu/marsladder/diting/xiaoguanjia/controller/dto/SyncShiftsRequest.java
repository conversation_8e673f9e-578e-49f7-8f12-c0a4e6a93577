package com.firstedu.marsladder.diting.xiaoguanjia.controller.dto;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncShiftsCommand;

import java.time.LocalDate;

public record SyncShiftsRequest(
        LocalDate updatedAfter, // this is beijing time to fit xiaoguanjia api
        int pageSize
) {
    public SyncShiftsCommand toSyncShiftsCommand() {
        return new SyncShiftsCommand(updatedAfter, pageSize);
    }
}
