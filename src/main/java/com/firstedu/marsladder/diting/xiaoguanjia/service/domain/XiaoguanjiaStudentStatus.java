package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import lombok.Getter;

@Getter
public enum XiaoguanjiaStudentStatus {
    DELETED(-1),
    AUDITION(0),
    ENROLLED(1),
    SUSPENDED(3),
    WITHDRAWN(99);

    private final int code;

    XiaoguanjiaStudentStatus(int code) {
        this.code = code;
    }

    public static XiaoguanjiaStudentStatus getByCode(int code) {
        return java.util.Arrays.stream(XiaoguanjiaStudentStatus.values())
                .filter(status -> status.getCode() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown status code: " + code));
    }
}
