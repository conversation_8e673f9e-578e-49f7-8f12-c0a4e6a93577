package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.event.XiaoguanjiaEventEntityEventHandler;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

import static jakarta.persistence.GenerationType.UUID;

@Entity(name = "xiaoguanjia_event")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@Getter
@EntityListeners(XiaoguanjiaEventEntityEventHandler.class)
public class XiaoguanjiaEventEntity {
    @Id
    @GeneratedValue(strategy = UUID)
    private String id;

    private String companyId;

    private String appId;

    private String eventKey;

    private String eventId;

    private String eventTime;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
