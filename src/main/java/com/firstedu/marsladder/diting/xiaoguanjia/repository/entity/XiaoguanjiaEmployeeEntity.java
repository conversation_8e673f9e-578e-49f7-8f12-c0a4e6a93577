package com.firstedu.marsladder.diting.xiaoguanjia.repository.entity;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.XiaoguanjiaEmployeeStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity(name = "xiaoguanjia_employee")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class XiaoguanjiaEmployeeEntity {
    @Id
    private String id;

    private String name;

    private String nickName;

    @Enumerated(EnumType.STRING)
    private XiaoguanjiaEmployeeStatus status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
