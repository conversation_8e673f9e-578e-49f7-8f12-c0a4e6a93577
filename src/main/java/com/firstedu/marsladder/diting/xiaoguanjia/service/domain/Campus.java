package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaDepartmentEntity;

public record Campus(String campusId, String campusName) {

    public static Campus from(XiaoguanjiaDepartmentEntity xiaoguanjiaDepartmentEntity) {
        return new Campus(xiaoguanjiaDepartmentEntity.getId(), xiaoguanjiaDepartmentEntity.getName());
    }
} 