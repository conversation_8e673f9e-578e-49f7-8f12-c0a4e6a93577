package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassStudentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XiaoguanjiaClassStudentRepository extends JpaRepository<XiaoguanjiaClassStudentEntity, String> {

    List<XiaoguanjiaClassStudentEntity> findByStudentId(String studentId);

    List<XiaoguanjiaClassStudentEntity> findByClassId(String classId);

    void deleteByClassId(String classId);
}
