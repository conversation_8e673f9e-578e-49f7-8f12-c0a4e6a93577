package com.firstedu.marsladder.diting.xiaoguanjia.utils;

import com.firstedu.marsladder.diting.exception.InternalServerErrorException;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class Utils {
    private static final String DATE_TIME_PATTERN = "yyyyMMddHHmmss";
    private static final String DATE_PATTERN = "yyyyMMdd";

    public static LocalDateTime fromBJTimeToUTC(String timestamp) {
        return convertTimeToUTC(timestamp, "Asia/Shanghai");
    }

    public static LocalDateTime fromMelTimeToUTC(String timestamp) {
        return convertTimeToUTC(timestamp, "Australia/Melbourne");
    }

    private static LocalDateTime convertTimeToUTC(String timestamp, String zoneId) {
        if (timestamp.length() == DATE_TIME_PATTERN.length()) {
            ;
        } else if (timestamp.length() == DATE_PATTERN.length()) {
            timestamp = timestamp + "120000"; // assume it is midday
        } else {
            throw new InternalServerErrorException("Unexpected timestamp received: " + timestamp);
        }

        var formatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
        var localDateTime = LocalDateTime.parse(timestamp, formatter).atZone(ZoneId.of(zoneId));
        return localDateTime.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime();
    }
}
