package com.firstedu.marsladder.diting.xiaoguanjia.service.domain;

public enum DictStatus {
    DELETED,
    DISABLED,
    ENABLED;

    public static DictStatus of(int value) {
        switch (value) {
            case -1 -> {
                return DELETED;
            }
            case 0 -> {
                return DISABLED;
            }
            case 1 -> {
                return ENABLED;
            }
            default -> throw new IllegalArgumentException("Unknown SubjectStatus value: " + value);
        }
    }
}
