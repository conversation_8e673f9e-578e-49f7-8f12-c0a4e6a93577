package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaGradeEntity;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.DictStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface XiaoguanjiaGradeRepository extends JpaRepository<XiaoguanjiaGradeEntity, String> {
    Optional<XiaoguanjiaGradeEntity> findByIdAndStatus(String id, DictStatus status);
}
