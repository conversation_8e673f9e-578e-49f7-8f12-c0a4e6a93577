package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaDepartmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XiaoguanjiaDepartmentRepository extends JpaRepository<XiaoguanjiaDepartmentEntity, String> {
    List<XiaoguanjiaDepartmentEntity> findAllByDeletedAndStatusAndIsCampus(boolean deleted, int status, boolean isCampus);
}
