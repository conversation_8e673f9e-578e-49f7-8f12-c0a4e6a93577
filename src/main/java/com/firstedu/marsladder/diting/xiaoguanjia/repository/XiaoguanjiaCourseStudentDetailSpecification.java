package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaCourseStudentDetailEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

public class XiaoguanjiaCourseStudentDetailSpecification {
    public static Specification<XiaoguanjiaCourseStudentDetailEntity> hasStudentIdIn(List<String> studentIds) {
        return (Root<XiaoguanjiaCourseStudentDetailEntity> entity, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (studentIds == null) {
                return cb.conjunction();
            } else {
                return cb.in(entity.get("id").get("studentId")).value(studentIds);
            }
        };
    }

    public static Specification<XiaoguanjiaCourseStudentDetailEntity> hasCourseStartTimeGreaterThanOrEqualTo(Long courseStartTime) {
        return (Root<XiaoguanjiaCourseStudentDetailEntity> entity, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (courseStartTime == null) {
                return cb.conjunction();
            } else {
                return cb.greaterThanOrEqualTo(entity.get("courseStartTime"), LocalDateTime.ofEpochSecond(courseStartTime, 0, ZoneOffset.UTC));
            }
        };
    }

    public static Specification<XiaoguanjiaCourseStudentDetailEntity> hasCourseStartTimeLessThanOrEqualTo(Long courseStartTime) {
        return (Root<XiaoguanjiaCourseStudentDetailEntity> entity, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (courseStartTime == null) {
                return cb.conjunction();
            } else {
                return cb.lessThanOrEqualTo(entity.get("courseStartTime"), LocalDateTime.ofEpochSecond(courseStartTime, 0, ZoneOffset.UTC));
            }
        };
    }

    public static Specification<XiaoguanjiaCourseStudentDetailEntity> hasCourseDeletedEqualTo(Boolean deleted) {
        return (Root<XiaoguanjiaCourseStudentDetailEntity> entity, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (deleted == null) {
                return cb.conjunction();
            } else {
                return cb.equal(entity.get("courseDeleted"), deleted);
            }
        };
    }

    public static Specification<XiaoguanjiaCourseStudentDetailEntity> hasClassIdEqualTo(String classId) {
        return (Root<XiaoguanjiaCourseStudentDetailEntity> entity, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (classId == null) {
                return cb.conjunction();
            } else {
                return cb.equal(entity.get("classId"), classId);
            }
        };
    }

    public static Specification<XiaoguanjiaCourseStudentDetailEntity> hasCourseIsFinishedNotEqualTo(Integer isFinished) {
        return (Root<XiaoguanjiaCourseStudentDetailEntity> entity, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
            if (isFinished == null) {
                return cb.conjunction();
            } else {
                return cb.notEqual(entity.get("courseIsFinished"), isFinished);
            }
        };
    }
}
