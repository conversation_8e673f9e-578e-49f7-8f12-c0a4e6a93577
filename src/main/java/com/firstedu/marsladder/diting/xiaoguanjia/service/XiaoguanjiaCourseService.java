package com.firstedu.marsladder.diting.xiaoguanjia.service;

import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCourseStudentsCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCourseTeachersCommand;
import com.firstedu.marsladder.diting.xiaoguanjia.service.domain.SyncCoursesCommand;

public interface XiaoguanjiaCourseService {
    void syncCourses(SyncCoursesCommand command);

    void syncCourseTeacher(SyncCourseTeachersCommand command);

    void syncCourseStudent(SyncCourseStudentsCommand command);
}
