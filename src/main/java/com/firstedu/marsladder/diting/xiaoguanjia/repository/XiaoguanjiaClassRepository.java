package com.firstedu.marsladder.diting.xiaoguanjia.repository;

import com.firstedu.marsladder.diting.xiaoguanjia.repository.entity.XiaoguanjiaClassEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XiaoguanjiaClassRepository extends JpaRepository<XiaoguanjiaClassEntity, String> {

    List<XiaoguanjiaClassEntity> findAllByIsFinishedAndDeletedAndStatus(boolean isFinished, boolean deleted, int status);

    List<XiaoguanjiaClassEntity> findByShiftIdAndDeletedAndStatus(String shiftId, boolean deleted, int status);

    List<XiaoguanjiaClassEntity> findAllByIsFinishedAndDeletedAndStatusAndCampusIdIn(Boolean isFinished, Boolean deleted, Integer status, List<String> campusId);
}
