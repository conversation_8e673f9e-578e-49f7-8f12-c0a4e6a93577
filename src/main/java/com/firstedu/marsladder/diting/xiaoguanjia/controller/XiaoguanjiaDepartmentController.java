package com.firstedu.marsladder.diting.xiaoguanjia.controller;

import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.CampusResponse;
import com.firstedu.marsladder.diting.xiaoguanjia.controller.dto.SyncDepartmentsRequest;
import com.firstedu.marsladder.diting.xiaoguanjia.service.XiaoguanjiaDepartmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.springframework.http.HttpStatus.OK;

@RestController
@RequiredArgsConstructor
public class XiaoguanjiaDepartmentController {

    private final XiaoguanjiaDepartmentService xiaoguanjiaDepartmentService;

    @ResponseStatus(OK)
    @PostMapping("/departments/sync")
    public void syncCourses(
            @RequestBody SyncDepartmentsRequest syncDepartmentsRequest
    ) {
        xiaoguanjiaDepartmentService.syncDepartments(syncDepartmentsRequest.toSyncDepartmentsCommand());
    }

    @ResponseStatus(OK)
    @GetMapping("/xiaoguanjia-campuses")
    @PreAuthorize("hasRole('ADMIN')")
    public List<CampusResponse> getAllCampuses() {
        return xiaoguanjiaDepartmentService.getAllCampuses().stream()
                .map(CampusResponse::from)
                .toList();
    }
}
