spring:
  datasource:
    url: ***********************************************

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_iNYdIXKNh/.well-known/jwks.json

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

sso:
  logoutEndpoint: https://sso.marsladder.com.au/logout
  clientId: 39kdjjoa4jdhjdj42j4ajsupcd
  logoutUri: https://realus.com.au

redis:
  url:
    host: redis.p.marsladder.com.au
    port: 6379
