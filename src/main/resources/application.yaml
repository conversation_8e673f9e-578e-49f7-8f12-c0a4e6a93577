server:
  port: 8080
spring:
  application:
    name: diting
  profiles:
    active: localmysql
  datasource:
    hikari:
      maximum-pool-size: 30
      max-lifetime: 890000
  data:
    redis:
      repositories:
        enabled: false
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: true
        jdbc:
          time_zone: UTC
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_5XijcEWvj/.well-known/jwks.json

klaviyo:
  host: ${KLAVIYO_HOST}
  private-api-key: ${KLAVIYO_PRIVATE_API_KEY}
  realus-lead-all-list: ${KLAVIYO_REALUS_LEAD_ALL_LIST}
  vce-mock-lead-all-list: ${KLAVIYO_VCE_MOCK_LEAD_ALL_LIST}

client:
  falcon:
    url: http://falcon
  xiaoguanjia:
    url: https://api.xiaogj.com
    appid: ${XIAOGUANJIA_APPID}
    secret: ${XIAOGUANJIA_SECRET}
  nezha:
    url: http://nezha
  kuixing:
    url: http://kuixing

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

sso:
  logoutEndpoint: https://sso.test.marsladder.com.au/logout
  clientId: 7e82143tp4pbrkp66d8eidbcj8
  logoutUri: https://test.realus.com.au

redis:
  url:
    host: redis.test.marsladder.com.au
    port: 6379
