#!/bin/bash -e

set -e
# set -x
cd $(dirname $0)/..
source ./auto/set-env-helpers

DB_MIGRATION_REPO="firstedu-engineering/diting-db-migration"
CLONE_DIR="/tmp/diting-db-migration"

echo "Cloning db migration repository"
git config --global user.email "${USER_EMAIL}"
git config --global user.name "${USER_NAME}"
git clone --single-branch --branch main "https://x-access-token:${GIT_API_TOKEN}@github.com/${DB_MIGRATION_REPO}.git" "${CLONE_DIR}"

echo "Finished cloning diting-db-migration to ${CLONE_DIR}"