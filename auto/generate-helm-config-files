#!/bin/bash -e

cd $(dirname $0)/..
source ./auto/set-env-helpers

ENV=${ENV:-${BRANCH_NAME}}
RELEASE_TAG=${RELEASE_TAG:-${GIT_HASH}}

sed -e "s/__TAG__/${RELEASE_TAG}/g" \
  -e "s/__APP_NAME__/${APP_NAME}/g" \
  template/values-image.yaml.tpl > template/values-image-${ENV}.yaml

if [[ "${ENV}" =~ ^r- ]]; then
  sed -e "s/__ENV__/${ENV}/g" \
    -e "s/__APP_NAME__/${APP_NAME}/g" \
    template/values-feature.yaml.tpl > template/values-feature-${ENV}.yaml
fi
