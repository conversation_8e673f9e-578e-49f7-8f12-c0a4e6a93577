#!/bin/bash -e

AWS_REGION=${AWS_REGION:-ap-southeast-2}
AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID:-************}
ECR_URL="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
APP_NAME="diting"
GIT_HASH=$(git rev-parse HEAD)
BRANCH_NAME=$(git branch --show-current)
TAG_TIME=$(date +'%Y%m%d%H%M')

function aws() {
  docker run --rm \
    -v $PWD:/working \
    -w /working \
    -e AWS_REGION=${AWS_REGION} \
    -e AWS_ACCESS_KEY_ID \
    -e AWS_SECRET_ACCESS_KEY \
    -e AWS_SESSION_TOKEN \
    amazon/aws-cli "$@"
}

function assume_role() {
  ROLE_ARN=${ROLE_ARN:-$1}

  if [ -z "${ROLE_ARN}" ]; then
    echo "Usage: $0 {ROLE_ARN}" >/dev/stderr
    return 1
  fi

  echo "=== Assuming role ${ROLE_ARN} ==="

  TOKENS=$(aws sts assume-role --role-arn ${ROLE_ARN} --role-session-name assumed-role)
  export AWS_ACCESS_KEY_ID=$(echo ${TOKENS} | jq -r '.Credentials.AccessKeyId')
  export AWS_SECRET_ACCESS_KEY=$(echo ${TOKENS} | jq -r '.Credentials.SecretAccessKey')
  export AWS_SESSION_TOKEN=$(echo ${TOKENS} | jq -r '.Credentials.SessionToken')
}
