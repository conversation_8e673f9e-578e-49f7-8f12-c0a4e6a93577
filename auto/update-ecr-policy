#!/bin/bash -e

cd $(dirname $0)/..
source ./auto/set-env-helpers

assume_role

APP_NAME=${APP_NAME:-1}
export AWS_REGION=${AWS_REGION:-ap-southeast-2}

echo "Update policy for repo ${APP_NAME}"

aws ecr put-lifecycle-policy \
--repository-name $APP_NAME \
--lifecycle-policy-text file://./cloudformation/ecr/lifecycle-policy.json

aws ecr set-repository-policy \
--repository-name $APP_NAME \
--policy-text file://./cloudformation/ecr/repository-policy.json 
