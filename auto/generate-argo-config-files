#!/bin/bash -e

cd $(dirname $0)/..
source ./auto/set-env-helpers

ENV=${ENV:-${BRANCH_NAME}}
RELEASE_TIMESTAMP=$(date +%s)

if [[ "${ENV}" =~ ^r- ]]; then
  sed -e "s/__ENV__/${ENV}/g" \
    -e "s/__APP_NAME__/${APP_NAME}/g" \
    -e "s/__RELEASE_TIMESTAMP__/${RELEASE_TIMESTAMP}/g" \
    template/argo-feature.yaml.tpl  > template/argo-feature-${ENV}.yaml
else
  echo "Please run this in feature branch, branch name must start with 'r-'."
  exit 1
fi
