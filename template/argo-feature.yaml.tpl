apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: __APP_NAME__-__ENV__
  namespace: argocd
  labels:
    ### Change lifetime as you wish, the max limit is 1440(1 day), any value greater than 1440 will be treated as 1440, unit: minutes
    ### DO NOT Change anything else!!!
    lifetime: 60
    releasetimestamp: __RELEASE_TIMESTAMP__
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  destination:
    namespace: feature
    server: https://kubernetes.default.svc
  project: default
  source:
    helm:
      valueFiles:
      - values.yaml
      - values-test.yaml
      - values-image-__ENV__.yaml
      - values-feature-__ENV__.yaml
    path: __APP_NAME__
    repoURL: **************:firstedu-engineering/Deployment-Manifests.git
    targetRevision: feature
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
