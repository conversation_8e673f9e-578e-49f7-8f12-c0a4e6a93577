<!--
  ~ Copyright 2010 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.2//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_2.dtd">
<module name="Checker">
    <module name="SuppressionFilter">
        <property name="file" value="${config_loc}/suppressions.xml"/>
    </module>
    <!-- allows suppressing using the @SuppressWarnings("checkstyle:...") -->
    <module name="SuppressWarningsFilter"/>
    <module name="TreeWalker">
        <!-- Blocks -->
        <module name="EmptyBlock">
            <property name="option" value="statement"/>
            <property name="tokens"
                      value="LITERAL_DO,LITERAL_ELSE,LITERAL_FINALLY,LITERAL_IF,LITERAL_FOR,LITERAL_TRY,LITERAL_WHILE,INSTANCE_INIT,STATIC_INIT"/>
        </module>
        <module name="EmptyBlock">
            <property name="option" value="text"/>
            <property name="tokens" value="LITERAL_CATCH"/>
        </module>
        <module name="AvoidNestedBlocks"/>

        <!-- Braces -->
        <module name="LeftCurly"/>
        <module name="RightCurly"/>
        <module name="NeedBraces"/>

        <!-- Whitespace -->
        <module name="GenericWhitespace"/>
        <module name="EmptyForInitializerPad"/>
        <module name="EmptyForIteratorPad"/>
<!--        <module name="MethodParamPad"/>-->
        <module name="NoWhitespaceBefore"/>
        <module name="NoWhitespaceAfter"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter">
            <property name="tokens" value="COMMA, SEMI"/>
        </module>
        <module name="WhitespaceAround">
            <property name="tokens" value="ASSIGN, BAND, BAND_ASSIGN, BOR, BOR_ASSIGN, BSR, BSR_ASSIGN, BXOR, BXOR_ASSIGN, COLON, DIV, DIV_ASSIGN, EQUAL, GE, GT, LAND, LE, LITERAL_ASSERT, LITERAL_CATCH, LITERAL_DO, LITERAL_ELSE, LITERAL_FINALLY, LITERAL_FOR, LITERAL_IF, LITERAL_RETURN, LITERAL_SYNCHRONIZED, LITERAL_TRY, LITERAL_WHILE, LOR, LT, MINUS, MINUS_ASSIGN, MOD, MOD_ASSIGN, NOT_EQUAL, PLUS, PLUS_ASSIGN, QUESTION, SL, SLIST, SL_ASSIGN, SR, SR_ASSIGN, STAR, STAR_ASSIGN, TYPE_EXTENSION_AND"/>
        </module>
        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
            <property name="allowMultipleEmptyLines" value="false"/>
            <property name="allowMultipleEmptyLinesInsideClassMembers" value="false"/>
        </module>

        <!-- Coding -->
        <module name="CovariantEquals"/>
        <module name="DefaultComesLast"/>
<!--        <module name="EmptyStatement"/>-->
        <module name="EqualsHashCode"/>
        <module name="ExplicitInitialization"/>
        <module name="MultipleVariableDeclarations"/>
        <module name="NoClone"/>
        <module name="NoFinalizer"/>
        <!--<module name="RedundantThrows">-->
        <!--<property name="allowUnchecked" value="true"/>-->
        <!--</module>-->
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="StringLiteralEquality"/>
        <module name="UnnecessaryParentheses"/>

        <!-- Design -->
        <!--
        <module name="InterfaceIsType"/>
        -->

        <!-- Imports -->
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>
        <module name="IllegalImport">
            <property name="illegalPkgs" value="jdk.internal,com.beust,org.testng.collections"/>
        </module>

        <!-- Naming -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName">
            <property name="format" value="^[a-z_][a-zA-Z0-9_]*$"/>
        </module>
        <module name="MethodName">
            <property name="format" value="^[a-z_][a-zA-Z0-9_]*$"/>
        </module>
        <module name="PackageName">
            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
        </module>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>
        <module name="ClassTypeParameterName">
            <property name="format" value="^[A-Z]+$"/>
        </module>
        <module name="InterfaceTypeParameterName">
            <property name="format" value="^[A-Z]+$"/>
        </module>
        <module name="MethodTypeParameterName">
            <property name="format" value="^[A-Z]+$"/>
        </module>

        <!-- Annotations -->
        <module name="MissingOverride"/>

        <!-- allows suppressing using the //CHECKSTYLE:ON //CHECKSTYLE:OFF -->
        <module name="SuppressionCommentFilter"/>
        <!-- to enable SuppressWarningsFilter -->
        <module name="SuppressWarningsHolder"/>

        <!-- Misc -->
        <module name="Indentation"/>
    </module>
    <module name="RegexpHeader">
        <property name="headerFile" value="${config_loc}/required-header.txt"/>
    </module>
    <module name="FileTabCharacter"/>
    <module name="RegexpSingleline">
        <property name="format" value="File \| Settings \| File Templates"/>
    </module>
    <module name="RegexpSingleline">
        <property name="format" value="Created with IntelliJ IDEA"/>
    </module>
    <!--    <module name="RegexpSingleline">-->
    <!--        <property name="format" value="^\s*\*\s*@author"/>-->
    <!--    </module>-->
</module>
