<?xml version="1.0"?>

<!DOCTYPE suppressions PUBLIC
        "-//Puppy Crawl//DTD Suppressions 1.1//EN"
        "http://www.puppycrawl.com/dtds/suppressions_1_1.dtd">

<suppressions>
    <!-- These packages are duplicated in core-api, don't require a package-info.java in each place -->
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]base-annotations[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]model-core[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]core[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]core[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]core[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]testfixtures[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]platform-base[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]core[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]testfixtures[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]resources[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]resources[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]maven[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]plugins[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]plugins[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]plugins[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]reporting[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]plugins[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]diagnostics[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]plugins[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]plugins[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]process-services[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]process[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]plugins[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]jvm[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]scala[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]base-services[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]base-services-groovy[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]base-services-groovy[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]specs[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]language-groovy[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\]javadoc[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]language-groovy[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\]compile[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]language-java[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\]compile[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]testing-jvm[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]jvm[/\\]plugins[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]testing-jvm[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\]testing[/\\][^/\\]+"/>
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]language-java[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]tasks[/\\][^/\\]+"/>

    <!-- These packages are duplicated in ide-native from ide, don't require a package-info.java in each place -->
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]ide-native[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]plugins[/\\]ide[/\\]api[/\\][^/\\]+"/>

    <!-- These packages are duplicated in platform-native from language-native, don't require a package-info.java in each place -->
    <suppress checks="JavadocPackage"
              files=".*[/\\]subprojects[/\\]platform-native[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]language[/\\]swift[/\\][^/\\]+"/>

    <!-- Don't require api docs for projects only used internally -->
    <suppress checks="Javadoc.*"
              files=".*[/\\]subprojects[/\\]internal-.+[/\\]src[/\\]main[/\\].+"/>

    <!-- JavaScript plugin is incubating -->
    <suppress checks="Javadoc.*"
              files=".*[/\\]subprojects[/\\]javascript[/\\].+"/>

    <!-- Protocol types may only define constants in interfaces -->
    <suppress checks="InterfaceIsTypeCheck"
              files=".*[/\\]subprojects[/\\]tooling-api[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]tooling[/\\]internal[/\\]protocol.+"/>
    <suppress checks="InterfaceIsTypeCheck"
              files=".*[/\\]subprojects[/\\]core-api[/\\]src[/\\]main[/\\]java[/\\]org[/\\]gradle[/\\]api[/\\]attributes[/\\]Usage.+"/>

    <!-- We like it explicit -->
    <suppress checks="ExplicitInitializationCheck"
              files=".*"/>

    <!-- Ignore code from maven subproject under org.apache -->
    <suppress checks="RegexpSingleline"
              files=".*[/\\]subprojects[/\\]maven[/\\]src[/\\]main[/\\]java[/\\]org[/\\]apache[/\\].+"/>
    <suppress checks="RegexpHeader"
              files=".*[/\\]subprojects[/\\]maven[/\\]src[/\\]main[/\\]java[/\\]org[/\\]apache[/\\].+"/>

    <!-- Ignore constant name for architecture tests since we want to use snake case there -->
    <suppress checks="ConstantName"
              files=".*[/\\]subprojects[/\\]architecture-test[/\\]src[/\\]test[/\\]java[/\\]org[/\\]gradle[/\\].+"/>

    <!-- Ignore all checks for the generated sources of org.gradle.samples plugin -->
    <suppress checks=".*" files=".*[/\\]subprojects[/\\]docs[/\\]build[/\\]generated-source-sets[/\\].+"/>
    <suppress checks=".*" files=".*[/\\]buildSrc[/\\].*[/\\]groovy-dsl-plugins[/\\].+"/>
</suppressions>
