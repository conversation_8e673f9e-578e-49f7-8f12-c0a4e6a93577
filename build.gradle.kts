import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent
import org.springframework.boot.gradle.tasks.bundling.BootBuildImage

plugins {
	java
	checkstyle
	jacoco
	id("org.springframework.boot") version "3.3.2"
	id("io.spring.dependency-management") version "1.1.6"
	id("org.flywaydb.flyway") version "10.18.1"
}

group = "com.firstedu.marsladder"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

checkstyle {
	toolVersion = "10.17.0"
}

jacoco {
	toolVersion = "0.8.11"
}

configurations {
	compileOnly {
		extendsFrom(configurations.annotationProcessor.get())
	}
}

repositories {
	mavenCentral()
	maven { url = uri("https://repo.spring.io/milestone") }
}

dependencies {
	implementation("org.springframework.boot:spring-boot-starter")
	implementation("org.springframework.boot:spring-boot-starter-data-jpa")
	implementation("org.springframework.boot:spring-boot-starter-actuator")
	implementation("org.springframework.boot:spring-boot-starter-validation")
	implementation("org.springframework.boot:spring-boot-starter-web")
	implementation("org.springframework.boot:spring-boot-starter-security")
	implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0")

	implementation("org.apache.commons:commons-lang3:3.14.0")
	implementation("org.apache.commons:commons-collections4:4.4")
	implementation("com.google.guava:guava:32.0.0-jre")
	implementation("net.logstash.logback:logstash-logback-encoder:7.4")
	implementation("redis.clients:jedis:5.1.2")
	implementation("org.redisson:redisson:3.19.0")

	compileOnly("org.projectlombok:lombok")
	annotationProcessor("org.projectlombok:lombok")
	runtimeOnly("com.h2database:h2")
	runtimeOnly("com.mysql:mysql-connector-j:8.3.0")

	testImplementation("org.flywaydb:flyway-core")
	testImplementation("org.flywaydb:flyway-mysql")
	testImplementation("org.springframework.cloud:spring-cloud-starter-contract-stub-runner:4.1.4")
	testImplementation("org.testcontainers:junit-jupiter")
	testImplementation("org.testcontainers:mysql")
	testImplementation("org.springframework.cloud:spring-cloud-starter-bootstrap:4.1.4")
	testImplementation("com.playtika.testcontainers:embedded-redis:3.1.9")
	testImplementation("org.springframework.boot:spring-boot-starter-test")
	testImplementation("org.springframework.security:spring-security-test")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}


tasks.withType<Checkstyle>().configureEach {
	reports {
		xml.required = false
		html.required = true
	}
}

tasks.test {
	useJUnitPlatform()
	testLogging { showStandardStreams = true }
	finalizedBy(tasks.jacocoTestCoverageVerification)
	exclude("com/firstedu/marsladder/diting/**/integration/**")
}

tasks.jacocoTestReport {
	dependsOn(tasks.test)

	reports {
		xml.required = false
		csv.required = false
		html.required = true
	}

}

tasks.jacocoTestCoverageVerification {
	dependsOn(tasks.jacocoTestReport)
	violationRules {
		rule {
			isEnabled = true
			element = "CLASS"
			excludes = listOf(
				"com.firstedu.marsladder.diting.DitingApplication",
				"com.firstedu.marsladder.diting.klaviyo.*",
				"com.firstedu.marsladder.diting.security.*",
				"com.firstedu.marsladder.diting.amazonoidc.*",
				"com.firstedu.marsladder.diting.client.*",
				"com.firstedu.marsladder.diting.config.*",
				"com.firstedu.marsladder.diting.utils.*",
				"com.firstedu.marsladder.diting.xiaoguanjia.event.*",
				"com.firstedu.marsladder.diting.xiaoguanjia.utils.*",
				"com.firstedu.marsladder.diting.xiaoguanjia.repository.*",
				"com.firstedu.marsladder.diting.**.domain.*",
			)

			limit {
				counter = "LINE"
				value = "COVEREDRATIO"
				minimum = "1.0".toBigDecimal()
			}
		}
	}
}

tasks.named<BootBuildImage>("bootBuildImage") {
	verboseLogging = true
	buildpacks.set(listOf("urn:cnb:builder:paketo-buildpacks/java", "./buildpack/"))
	environment.set(mapOf("BP_JVM_VERSION" to "21.*"))
}

tasks.check{
	dependsOn("installGitHooks")
	dependsOn("jacocoTestCoverageVerification")
}

tasks.build {
	dependsOn("installGitHooks")
}

tasks.register<Copy>("installGitHooks") {
	if (File(".git/hooks/pre-commit").exists()) {
		File(".git/hooks/pre-commit").delete()
	}
	from(File(rootProject.rootDir, "auto/pre-commit"))
	into(File(rootProject.rootDir, ".git/hooks"))
	fileMode = 511
}

tasks.register<Test>("integrationTest") {
	useJUnitPlatform()
	testLogging {
		lifecycle {
			events =
				mutableSetOf(
					TestLogEvent.FAILED,
					TestLogEvent.PASSED,
					TestLogEvent.SKIPPED,
				)
			exceptionFormat = TestExceptionFormat.FULL
			showExceptions = true
			showCauses = true
			showStackTraces = true
			showStandardStreams = true
		}
	}
	include("com/firstedu/marsladder/diting/**/integration/**")
}
